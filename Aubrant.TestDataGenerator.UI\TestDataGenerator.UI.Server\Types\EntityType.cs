using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.Data.Enums;

namespace Aubrant.TestDataGenerator.UI.Server.Types
{
    public class EntityType : ObjectType<Entity>
    {
        protected override void Configure(IObjectTypeDescriptor<Entity> descriptor)
        {
            descriptor.Field(e => e.Id).Type<IdType>();
            descriptor.Field(e => e.DataSourceId).Type<IntType>();
            descriptor.Field(e => e.Name).Type<StringType>();
            descriptor.Field(e => e.Schema).Type<StringType>();
            descriptor.Field(e => e.SyncStrategy).Type<EnumType<SyncStrategy>>();
            descriptor.Field(e => e.DataSource).Type<DataSourceType>();
            descriptor.Field(e => e.Fields).Type<ListType<FieldType>>();
            descriptor.Field(e => e.KeyConstraints).Type<ListType<KeyConstraintType>>();
            descriptor.Field(e => e.Relationships).Type<ListType<RelationshipType>>();
        }
    }
}
