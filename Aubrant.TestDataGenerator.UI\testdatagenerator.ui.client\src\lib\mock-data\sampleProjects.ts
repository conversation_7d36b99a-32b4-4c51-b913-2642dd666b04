import type { Project } from "../../types/database";

export const sampleProjects: Project[] = [
    {

        "Id": 1,
        "Name": "Test Data Generator - Project 1",
        "Description": "",
        "Author": "<PERSON><PERSON> <PERSON>",
        "CreatedDate": "7/21/2025 10:28:39 AM",
        "DataSources": [
            {
                "Id": 1,
                "Name": "ChinookDemoDb",
                "Type": "Relational Database",
                "Provider": "SQLite",
                "ConnectionString": "Data Source=C:\\Users\\<USER>\\AppData\\Roaming\\LINQPad\\ChinookDemoDb.sqlite"
            }
        ]
    }
];