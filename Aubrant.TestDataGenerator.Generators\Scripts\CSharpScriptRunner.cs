using Microsoft.CodeAnalysis.CSharp.Scripting;
using Microsoft.CodeAnalysis.Scripting;
using System.Data;
using Aubrant.TestDataGenerator.Core.Utils;

namespace Aubrant.TestDataGenerator.Generators.Scripts
{
    /// <summary>
    /// Provides functionality to compile and execute C# scripts.
    /// </summary>
    public class CSharpScriptRunner
    {
        private readonly Script<object> _script;
        private readonly ScriptRunner<object> _scriptRunner;

        /// <summary>
        /// Initializes a new instance of the <see cref="CSharpScriptRunner"/> class.
        /// </summary>
        /// <param name="code">The C# script code to execute.</param>
        public CSharpScriptRunner(string code)
        {
            var options = ScriptOptions.Default
                .AddReferences(typeof(DataRow).Assembly)
                .AddReferences(typeof(ScriptExtensions).Assembly)
                .AddReferences(typeof(Microsoft.CSharp.RuntimeBinder.CSharpArgumentInfo).Assembly)
                .AddImports("System")
                .AddImports("System.Linq")
                .AddImports("TestDataGenerator.Core.Utils");

            _script = CSharpScript.Create(code, options, typeof(ScriptGlobals));
            _scriptRunner = _script.CreateDelegate();
        }

        /// <summary>
        /// Executes the compiled script with the specified global context.
        /// </summary>
        /// <param name="globals">The global variables to be available in the script.</param>
        /// <returns>The result of the script execution.</returns>
        public object Execute(ScriptGlobals globals)
        {
            return _scriptRunner(globals).Result;
        }
    }
}
