﻿using Aubrant.TestDataGenerator.Data.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Aubrant.TestDataGenerator.Data.ReferentialIntegrity
{
    /// <summary>
    /// Builds dependency graphs from database schema relationships
    /// </summary>
    public class DependencyGraphBuilder : IDependencyGraphBuilder
    {
        public DependencyGraph BuildDependencyGraph(DataSource dataSource)
        {
            var graph = new DependencyGraph();
            var nodes = new Dictionary<string, EntityNode>();

            // Create nodes for all entities
            foreach (var entity in dataSource.Entities)
            {
                var node = new EntityNode
                {
                    Entity = entity
                };

                // Extract foreign key relationships
                foreach (var keyConstraint in entity.KeyConstraints.Where(kc => kc.KeyType == KeyType.Foreign))
                {
                    if (keyConstraint.ReferencedEntity != null)
                    {
                        var sourceField = entity.Fields.FirstOrDefault(f => keyConstraint.Columns.Contains(f.Name));
                        if (sourceField != null)
                        {
                            var relationship = new ForeignKeyRelationship
                            {
                                SourceField = sourceField,
                                TargetEntityName = keyConstraint.ReferencedEntity,
                                TargetFieldName = keyConstraint.ReferencedColumns.FirstOrDefault() ?? "Id",
                                IsNullable = sourceField.IsNullable,
                                Cardinality = GetCardinalityFromRelationships(entity, keyConstraint.ReferencedEntity)
                            };

                            node.ForeignKeyRelationships.Add(relationship);
                            node.Dependencies.Add(keyConstraint.ReferencedEntity);
                        }
                    }
                }

                nodes[entity.Name!] = node;
            }

            // Build dependency relationships
            foreach (var node in nodes.Values)
            {
                foreach (var dependency in node.Dependencies)
                {
                    if (nodes.TryGetValue(dependency, out var dependencyNode))
                    {
                        dependencyNode.Dependents.Add(node.Entity.Name!);
                    }
                }
            }

            graph.Nodes = nodes;

            // Detect circular references
            DetectCircularReferences(graph);

            // Calculate dependency levels and topological order
            CalculateDependencyLevels(graph);
            CreateTopologicalOrder(graph);

            return graph;
        }

        public DependencyGraphValidationResult ValidateGraph(DependencyGraph graph)
        {
            var result = new DependencyGraphValidationResult { IsValid = true };

            // Check for orphaned entities
            var orphanedEntities = graph.Nodes.Values
                .Where(n => !n.Dependencies.Any() && !n.Dependents.Any())
                .ToList();

            if (orphanedEntities.Any())
            {
                result.Warnings.Add($"Found {orphanedEntities.Count} entities with no relationships: {string.Join(", ", orphanedEntities.Select(e => e.Entity.Name))}");
            }

            // Check for missing referenced entities
            foreach (var node in graph.Nodes.Values)
            {
                foreach (var dependency in node.Dependencies)
                {
                    if (!graph.Nodes.ContainsKey(dependency))
                    {
                        result.Errors.Add($"Entity '{node.Entity.Name}' references missing entity '{dependency}'");
                        result.IsValid = false;
                    }
                }
            }

            // Validate circular reference handling
            foreach (var circularGroup in graph.CircularReferences)
            {
                var hasNullableRelationship = circularGroup.CircularRelationships.Any(r => r.IsNullable);
                if (!hasNullableRelationship)
                {
                    result.Warnings.Add($"Circular reference group [{string.Join(", ", circularGroup.EntityNames)}] has no nullable relationships. Consider making at least one relationship nullable.");
                    result.Suggestions.Add($"Add nullable foreign key in circular reference group: {string.Join(" -> ", circularGroup.EntityNames)}");
                }
            }

            // Check dependency depth
            if (graph.MaxDepth > 10)
            {
                result.Warnings.Add($"Deep dependency chain detected (depth: {graph.MaxDepth}). This may impact generation performance.");
            }

            return result;
        }

        private void DetectCircularReferences(DependencyGraph graph)
        {
            var visited = new HashSet<string>();
            var recursionStack = new HashSet<string>();
            var circularGroups = new List<CircularReferenceGroup>();

            foreach (var node in graph.Nodes.Values)
            {
                if (!visited.Contains(node.Entity.Name!))
                {
                    var path = new List<string>();
                    DetectCircularReferencesRecursive(node.Entity.Name!, graph.Nodes, visited, recursionStack, path, circularGroups);
                }
            }

            graph.CircularReferences = circularGroups;

            // Mark nodes that are part of circular references
            foreach (var group in circularGroups)
            {
                foreach (var entityName in group.EntityNames)
                {
                    if (graph.Nodes.TryGetValue(entityName, out var node))
                    {
                        node.IsInCircularReference = true;
                    }
                }
            }
        }

        private bool DetectCircularReferencesRecursive(
            string currentEntity,
            Dictionary<string, EntityNode> nodes,
            HashSet<string> visited,
            HashSet<string> recursionStack,
            List<string> path,
            List<CircularReferenceGroup> circularGroups)
        {
            visited.Add(currentEntity);
            recursionStack.Add(currentEntity);
            path.Add(currentEntity);

            if (nodes.TryGetValue(currentEntity, out var currentNode))
            {
                foreach (var dependency in currentNode.Dependencies)
                {
                    if (!visited.Contains(dependency))
                    {
                        if (DetectCircularReferencesRecursive(dependency, nodes, visited, recursionStack, path, circularGroups))
                        {
                            return true;
                        }
                    }
                    else if (recursionStack.Contains(dependency))
                    {
                        // Found circular reference
                        var circularStartIndex = path.IndexOf(dependency);
                        var circularPath = path.Skip(circularStartIndex).ToList();
                        circularPath.Add(dependency); // Complete the circle

                        var group = new CircularReferenceGroup
                        {
                            EntityNames = circularPath.Distinct().ToList(),
                            Strategy = DetermineCircularReferenceStrategy(circularPath, nodes)
                        };

                        // Add the relationships that form the circular reference
                        for (int i = 0; i < circularPath.Count - 1; i++)
                        {
                            var sourceEntity = circularPath[i];
                            var targetEntity = circularPath[i + 1];

                            if (nodes.TryGetValue(sourceEntity, out var sourceNode))
                            {
                                var relationship = sourceNode.ForeignKeyRelationships
                                    .FirstOrDefault(r => r.TargetEntityName == targetEntity);
                                if (relationship != null)
                                {
                                    group.CircularRelationships.Add(relationship);
                                }
                            }
                        }

                        circularGroups.Add(group);
                        return true;
                    }
                }
            }

            path.RemoveAt(path.Count - 1);
            recursionStack.Remove(currentEntity);
            return false;
        }

        private CircularReferenceStrategy DetermineCircularReferenceStrategy(List<string> circularPath, Dictionary<string, EntityNode> nodes)
        {
            // Check if any relationship in the circular path is nullable
            bool hasNullableRelationship = false;

            for (int i = 0; i < circularPath.Count - 1; i++)
            {
                var sourceEntity = circularPath[i];
                var targetEntity = circularPath[i + 1];

                if (nodes.TryGetValue(sourceEntity, out var sourceNode))
                {
                    var relationship = sourceNode.ForeignKeyRelationships
                        .FirstOrDefault(r => r.TargetEntityName == targetEntity);
                    if (relationship?.IsNullable == true)
                    {
                        hasNullableRelationship = true;
                        break;
                    }
                }
            }

            // Determine strategy based on relationship characteristics
            if (hasNullableRelationship)
            {
                return CircularReferenceStrategy.NullFirstThenUpdate;
            }
            else if (circularPath.Count <= 3)
            {
                return CircularReferenceStrategy.SubsetFirst;
            }
            else
            {
                return CircularReferenceStrategy.DeferredAssignment;
            }
        }

        private void CalculateDependencyLevels(DependencyGraph graph)
        {
            var levels = new Dictionary<string, int>();

            // Initialize all nodes to level -1 (unprocessed)
            foreach (var node in graph.Nodes.Values)
            {
                levels[node.Entity.Name!] = -1;
            }

            // Process nodes level by level
            var currentLevel = 0;
            var processed = new HashSet<string>();

            while (processed.Count < graph.Nodes.Count)
            {
                var nodesAtCurrentLevel = new List<string>();

                foreach (var node in graph.Nodes.Values)
                {
                    if (processed.Contains(node.Entity.Name!)) continue;

                    // Check if all dependencies are processed or if this is a circular reference
                    var unprocessedDependencies = node.Dependencies.Where(d => !processed.Contains(d)).ToList();

                    if (!unprocessedDependencies.Any() || node.IsInCircularReference)
                    {
                        nodesAtCurrentLevel.Add(node.Entity.Name!);
                        levels[node.Entity.Name!] = currentLevel;
                        node.DependencyLevel = currentLevel;
                    }
                }

                if (!nodesAtCurrentLevel.Any())
                {
                    // Handle remaining circular references
                    var remainingNodes = graph.Nodes.Values
                        .Where(n => !processed.Contains(n.Entity.Name!))
                        .ToList();

                    foreach (var node in remainingNodes)
                    {
                        levels[node.Entity.Name!] = currentLevel;
                        node.DependencyLevel = currentLevel;
                        nodesAtCurrentLevel.Add(node.Entity.Name!);
                    }
                }

                processed.UnionWith(nodesAtCurrentLevel);
                currentLevel++;
            }

            graph.MaxDepth = currentLevel - 1;
        }

        private void CreateTopologicalOrder(DependencyGraph graph)
        {
            // Sort nodes by dependency level, then by name for consistency
            graph.TopologicalOrder = graph.Nodes.Values
                .OrderBy(n => n.DependencyLevel)
                .ThenBy(n => n.Entity.Name)
                .ToList();
        }

        private Cardinality GetCardinalityFromRelationships(Entity entity, string targetEntityName)
        {
            var relationship = entity.Relationships
                .FirstOrDefault(r => r.TargetEntity == targetEntityName);

            return relationship?.Cardinality ?? Cardinality.ManyToOne;
        }
    }
}
