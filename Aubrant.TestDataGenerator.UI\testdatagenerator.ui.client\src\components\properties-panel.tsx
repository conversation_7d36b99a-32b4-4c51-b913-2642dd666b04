import { ScrollArea } from "@/components/ui/scroll-area"
import { Database } from "lucide-react"
import { RelationshipDetailsPanel } from "./relationship-details-panel"
import { FieldDetailsPanel } from "./field-details-panel"
import { EntityDetailsPanel } from "./entity-details-panel"
import type { Entity, Field, Relationship } from "../types/database";

interface PropertiesPanelProps {
  selectedEntity: Entity | null
  selectedField: Field | null
  selectedRelationship: {
    relationship: Relationship
    sourceEntity: Entity
    targetEntity: Entity
  } | null
}

export function PropertiesPanel({
  selectedEntity,
  selectedField,
  selectedRelationship,
}: PropertiesPanelProps) {
  return (
    <div className="w-80 border-l bg-background">
      <div className="p-4 border-b">
        <h2 className="text-lg font-semibold">Properties</h2>
      </div>
      <ScrollArea className="h-[calc(100vh-73px)]">
        <div className="p-4">
          {selectedRelationship ? (
            <RelationshipDetailsPanel selectedRelationship={selectedRelationship} />
          ) : selectedField && selectedEntity ? (
            <FieldDetailsPanel selectedField={selectedField} selectedEntity={selectedEntity} />
          ) : selectedEntity ? (
            <EntityDetailsPanel selectedEntity={selectedEntity} />
          ) : (
            <div className="text-center text-muted-foreground">
              <Database className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>
                Select a table, field, or relationship to view its properties
              </p>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}