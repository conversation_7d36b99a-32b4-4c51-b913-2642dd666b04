﻿using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Interfaces;
using System.ComponentModel;
using System.Text.Json;

namespace Aubrant.TestDataGenerator.Generators
{
    /// <summary>
    /// Implements the IGenerator interface to generate random DataValue objects.
    /// The type of random value and its range (if applicable) are determined
    /// during the generator's initialization.
    /// </summary>
    public class RandomGenerator : IRuntimeGenerator
    {
        public string Name => "Random";

        public string Description => "Selects a Random value in a Range";

        public DataType ReturnType => DataType.Integer | DataType.Decimal | DataType.DateTime;

        /// <summary>
        /// The minimum bound for random value generation
        /// </summary>
        [Description("The minimum bound for random value generation")]
        public DataValue? Min { get; set; }

        [Description("The maximum bound for random value generation")]
        public DataValue? Max { get; set; }

        [Description("The data type of the generated random value.")]
        public DataType DataType { get; private set; }

        /// <summary>
        /// Parameterless constructor required for Semantic Analyzer
        /// </summary>
        public RandomGenerator() { }

        /// <summary>
        /// Initializes a new instance of the RandomGenerator class with the specified data type
        /// and optional minimum and maximum DataValues.
        /// </summary>
        /// <param name="type">The DataType of values this generator will produce.</param>
        /// <param name="min">The minimum bound for random value generation (optional, depends on type).</param>
        /// <param name="max">The maximum bound for random value generation (optional, depends on type).</param>
        /// <exception cref="ArgumentNullException">Thrown if min/max are required but not provided.</exception>
        /// <exception cref="ArgumentException">Thrown for invalid type combinations or range issues.</exception>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if min value is greater than max value.</exception>
        public RandomGenerator(DataType type, DataValue? min = null, DataValue? max = null) : this()
        {
            DataType = type;
            Min = min;
            Max = max;

            // Validate constructor parameters based on the specified DataType
            switch (type)
            {
                case DataType.Integer:
                case DataType.Decimal:
                case DataType.DateTime:
                    // For these types, min and max DataValues must be provided and of the correct underlying type.
                    if (min == null || max == null)
                    {
                        throw new ArgumentNullException($"Min and Max values must be provided for {type} type.");
                    }
                    if (min.Type != type || max.Type != type)
                    {
                        throw new ArgumentException($"Min and Max DataValue types must match the generator's DataType ({type}).");
                    }
                    ValidateMinMaxOrder(type, min, max); // Validate order of min/max
                    break;
                case DataType.String:
                    // For string, min/max DataValues are optional and represent length.
                    // If provided, they must be of Integer DataType.
                    if ((min != null && min.Type != DataType.Integer) || (max != null && max.Type != DataType.Integer))
                    {
                        throw new ArgumentException("Min and Max for String type must be of Integer DataType for length.");
                    }
                    if (min?.ToInt() > max?.ToInt())
                    {
                        throw new ArgumentOutOfRangeException("Min length cannot be greater than Max length for String type.");
                    }
                    break;
                case DataType.Boolean:
                    // Min/Max are not used for Boolean type.
                    if (min != null || max != null)
                    {
                        throw new ArgumentException("Min and Max values are not applicable for Boolean type.");
                    }
                    break;
                default:
                    throw new ArgumentException($"Unsupported DataType for RandomGenerator: {type}");
            }
        }

        /// <summary>
        /// Convenience constructor for generating random integers within a specified range.
        /// </summary>
        /// <param name="min">The minimum integer value (inclusive).</param>
        /// <param name="max">The maximum integer value (inclusive).</param>
        public RandomGenerator(int min, int max) : this(DataType.Integer, new DataValue(min), new DataValue(max)) { }

        /// <summary>
        /// Convenience constructor for generating random decimal numbers within a specified range.
        /// </summary>
        /// <param name="min">The minimum decimal value (inclusive).</param>
        /// <param name="max">The maximum decimal value (inclusive).</param>
        public RandomGenerator(decimal min, decimal max) : this(DataType.Decimal, new DataValue(min), new DataValue(max)) { }

        /// <summary>
        /// Convenience constructor for generating random DateTime values within a specified range.
        /// </summary>
        /// <param name="min">The minimum DateTime value (inclusive).</param>
        /// <param name="max">The maximum DateTime value (inclusive).</param>
        public RandomGenerator(DateTime min, DateTime max) : this(DataType.DateTime, new DataValue(min), new DataValue(max)) { }

        /// <summary>
        /// Convenience constructor for generating random boolean values.
        /// Min and Max are not applicable for boolean.
        /// </summary>
        //public RandomGenerator() : this(DataType.Boolean) { }

        /// <summary>
        /// Convenience constructor for generating random strings with a specified length range.
        /// </summary>
        /// <param name="minLength">The minimum length of the generated string (inclusive).</param>
        /// <param name="maxLength">The maximum length of the generated string (inclusive).</param>
        /*
         * public RandomGenerator(int minLength, int maxLength) : this(DataType.String, new DataValue(minLength), new DataValue(maxLength))
        {
            // Additional check, though primary constructor should handle most validation
            if (minLength < 0) throw new ArgumentOutOfRangeException(nameof(minLength), "Minimum length cannot be negative.");
            if (maxLength < minLength) throw new ArgumentOutOfRangeException(nameof(maxLength), "Maximum length cannot be less than minimum length.");
        }
        */

        /// <summary>
        /// Helper method to validate the order of min and max DataValues based on their type.
        /// </summary>
        /// <param name="type">The DataType of the values being compared.</param>
        /// <param name="min">The DataValue representing the minimum bound.</param>
        /// <param name="max">The DataValue representing the maximum bound.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if min value is greater than max value.</exception>
        private void ValidateMinMaxOrder(DataType type, DataValue min, DataValue max)
        {
            switch (type)
            {
                case DataType.Integer:
                    if (min.ToInt() > max.ToInt())
                    {
                        throw new ArgumentOutOfRangeException(nameof(min), "Min value cannot be greater than Max value for Integer type.");
                    }
                    break;
                case DataType.Decimal:
                    if (min.ToDecimal() > max.ToDecimal())
                    {
                        throw new ArgumentOutOfRangeException(nameof(min), "Min value cannot be greater than Max value for Decimal type.");
                    }
                    break;
                case DataType.DateTime:
                    if (min.ToDate() > max.ToDate())
                    {
                        throw new ArgumentOutOfRangeException(nameof(min), "Min date cannot be greater than Max date for Date type.");
                    }
                    break;
                    // No default needed as other types are handled by other constructors or don't use ranges
            }
        }

        /// <summary>
        /// Generates a random DataValue according to the generator's configured type and range.
        /// </summary>
        /// <returns>A DataValue containing the randomly generated value.</returns>
        /// <exception cref="InvalidOperationException">Thrown if an unsupported DataType is encountered (should not happen with proper construction).</exception>
        public DataValue Generate(DataGeneratorContext context)
        {
            switch (DataType)
            {
                case DataType.Integer:
                    int minInt = Min!.ToInt()!.Value; // Null-forgiving as validated in constructor
                    int maxInt = Max!.ToInt()!.Value;
                    return new DataValue(context.Random.Next(minInt, maxInt + 1));

                case DataType.Decimal:
                    decimal minDec = Min!.ToDecimal()!.Value;
                    decimal maxDec = Max!.ToDecimal()!.Value;
                    // Generate a random double between 0.0 (inclusive) and 1.0 (exclusive)
                    // then scale it to the desired decimal range.
                    return new DataValue(minDec + (decimal)(context.Random.NextDouble() * (double)(maxDec - minDec)));

                case DataType.DateTime:
                    DateTime minDate = Min!.ToDate()!.Value; // Null-forgiving as validated in constructor
                    DateTime maxDate = Max!.ToDate()!.Value;
                    long ticksDiff = maxDate.Ticks - minDate.Ticks;
                    long randomTicks = (long)(context.Random.NextDouble() * ticksDiff);
                    return new DataValue(minDate.AddTicks(randomTicks));

                case DataType.Boolean:
                    return new DataValue(context.Random.Next(2) == 0); // 0 or 1, representing false or true

                case DataType.String:
                    // Default string length range if min/max are not provided for string.
                    // These defaults are used if the constructor was called with only DataType.String,
                    // or if min/max DataValues were null.
                    int minLength = Min?.ToInt() ?? 5; // Default min length 5
                    int maxLength = Max?.ToInt() ?? 15; // Default max length 15

                    // Basic sanity check, though constructor validation aims to prevent invalid ranges.
                    if (minLength < 0) minLength = 0;
                    if (maxLength < minLength) maxLength = minLength; // Ensure max is not less than min

                    int length = context.Random.Next(minLength, maxLength + 1);
                    const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789 ";
                    char[] stringChars = new char[length];
                    for (int i = 0; i < length; i++)
                    {
                        stringChars[i] = chars[context.Random.Next(chars.Length)];
                    }
                    return new DataValue(new string(stringChars));

                default:
                    throw new InvalidOperationException($"Unsupported data type for generation: {DataType}. This should have been caught during construction.");
            }
        }

        public void Parse(DataType dataType, IDictionary<string, object> settings)
        {
            if (settings.TryGetValue("DataType", out object? dataTypeObject) && dataTypeObject is JsonElement dataTypeElement && dataTypeElement.ValueKind == JsonValueKind.String)
            {
                var dataTypeString = dataTypeElement.GetString();
                if (Enum.TryParse(dataTypeString, true, out DataType parsedDataType))
                {
                    DataType = parsedDataType;
                }
            }

            if (settings.TryGetValue("Min", out object? minObject) && minObject is JsonElement minElement)
            {
                Min = new DataValue(dataType, minObject);

                if (DataType == 0 && Min.Type != 0)
                {
                    DataType = Min.Type; // Set DataType based on Min if not already set
                }
            }

            if (settings.TryGetValue("Max", out object? maxObject) && maxObject is JsonElement maxElement)
            {
                Max = new DataValue(dataType, maxObject);
            }
        }
    }
}
