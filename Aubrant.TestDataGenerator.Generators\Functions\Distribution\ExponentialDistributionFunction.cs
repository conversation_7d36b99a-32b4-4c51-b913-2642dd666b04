using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Functions;
using System;

namespace Aubrant.TestDataGenerator.Generators.Functions{
    /// <summary>
    /// A generator function that produces random decimal values following an Exponential distribution.
    /// </summary>
    public class ExponentialDistributionFunction : BaseFunction
    {
        public override string Name => "Exponential Distribution";
        public override string Description => "Generates random numbers from an Exponential distribution.";
        public override DataType ReturnType => DataType.Decimal;

        /// <summary>
        /// Initializes a new instance of the ExponentialDistributionFunction class.
        /// Defines a 'Rate' parameter for the exponential distribution.
        /// </summary>
        public ExponentialDistributionFunction() : base("Statistical")
        {
            Parameters.Add(new Parameter("Rate", "The rate of the distribution.", DataType.Decimal, new DataValue(1.0M))); // Default rate (lambda) is 1.0
        }

        /// <summary>
        /// Generates a random decimal value from an Exponential distribution based on the configured Rate.
        /// </summary>
        /// <param name="context">The data generator context.</param>
        /// <returns>A DataValue containing the exponentially distributed decimal number.</returns>
        /// <exception cref="InvalidOperationException">Thrown if the Rate parameter is missing or invalid.</exception>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if Rate is non-positive.</exception>
        public override DataValue Generate(DataGeneratorContext context)
        {
            decimal rate = Parameters["Rate"].Value?.ToDecimal() ??
                           throw new InvalidOperationException("Rate parameter is missing or invalid.");

            if (rate <= 0)
            {
                throw new ArgumentOutOfRangeException(nameof(rate), "Rate must be positive for Exponential distribution.");
            }

            // Generate a uniform random number between 0.0 (exclusive) and 1.0 (exclusive)
            // We use 1.0 - context.Random.NextDouble() to ensure the value is not 0, as log(0) is undefined.
            double u = 1.0 - context.Random.NextDouble();

            // Apply the inverse transform sampling for exponential distribution
            decimal result = (decimal)(-Math.Log(u) / (double)rate);

            return new DataValue(DataType.Decimal, result);
        }
    }
}
