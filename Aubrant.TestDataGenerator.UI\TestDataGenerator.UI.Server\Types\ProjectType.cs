using Aubrant.TestDataGenerator.Data;
using HotChocolate.Types;

namespace Aubrant.TestDataGenerator.UI.Server.Types
{
    public class ProjectType : ObjectType<Project>
    {
        protected override void Configure(IObjectTypeDescriptor<Project> descriptor)
        {
            descriptor.Field(p => p.Id).Type<IdType>();
            descriptor.Field(p => p.Name).Type<StringType>();
            descriptor.Field(p => p.Description).Type<StringType>();
            descriptor.Field(p => p.Author).Type<StringType>();
            descriptor.Field(p => p.CreatedDate).Type<DateTimeType>();
            descriptor.Field(p => p.DataSources).Type<ListType<DataSourceType>>();
        }
    }
}
