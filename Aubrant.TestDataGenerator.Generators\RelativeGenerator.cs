using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Interfaces;
using Aubrant.TestDataGenerator.Core.Utils.Models;
using System.ComponentModel;
using System.Text.Json;

namespace Aubrant.TestDataGenerator.Generators
{
    public class RelativeGenerator : IRuntimeGenerator
    {
        private readonly Dictionary<DataType, IOffsetCalculator> _offsetCalculators;

        public string Name => "Relative";
        public string Description => "Generates a value relative to another column.";
        public DataType ReturnType => DataType.Integer | DataType.Decimal | DataType.DateTime;

        [Description("The name of the column to reference.")]
        public string ReferenceColumn { get; private set; }
        [Description("The minimum offset to apply to the reference column.")]
        public string MinOffset { get; private set; }
        [Description("The maximum offset to apply to the reference column.")]
        public string MaxOffset { get; private set; }
        [Description("The probability (0-100) that the generated value will be null.")]
        public int NullProbability { get; private set; }

        public RelativeGenerator()
        {
            _offsetCalculators = new Dictionary<DataType, IOffsetCalculator>
            {
                { DataType.DateTime, new DateTimeOffsetCalculator() },
                { DataType.Integer, new NumericOffsetCalculator() },
                { DataType.Decimal, new NumericOffsetCalculator() }
            };
        }
        
        public RelativeGenerator(string referenceColumn, string minOffset, string maxOffset, int nullProbability = 0) : this()
        {
            ReferenceColumn = referenceColumn;
            MinOffset = minOffset;
            MaxOffset = maxOffset;
            NullProbability = nullProbability;
        }

        public DataValue Generate(DataGeneratorContext context)
        {
            if (NullProbability > 0 && context.Random.Next(100) < NullProbability)
            {
                return new DataValue(DataType.DateTime, DBNull.Value); // Return null
            }

            var referenceValue = context.Row[ReferenceColumn];

            if (referenceValue is null or DBNull)
            {
                return new DataValue(DataType.DateTime, DBNull.Value); // Return null if reference is null
            }

            DataValue value;

            if (referenceValue is int intValue)
            {
                value = new DataValue(intValue);
            }
            else if (referenceValue is decimal decimalValue)
            {
                value = new DataValue(decimalValue);
            }
            else if (referenceValue is DateTime dateValue)
            {
                value = new DataValue(dateValue);
            }
            else
            {
                throw new InvalidOperationException($"Unsupported reference type: {referenceValue}");
            }

            if (_offsetCalculators.TryGetValue(value.Type, out var calculator))
            {
                return calculator.ApplyOffset(value, MinOffset, MaxOffset, context.Random);
            }

            throw new NotSupportedException($"Relative generation is not supported for data type: {value.Type}");
        }

        public void Parse(DataType dataType, IDictionary<string, object> settings)
        {
            if (settings.TryGetValue("ReferenceColumn", out object? referenceColumnObject) && referenceColumnObject is JsonElement referenceColumnElement && referenceColumnElement.ValueKind == JsonValueKind.String)
            {
                ReferenceColumn = referenceColumnElement.GetString();
            }

            if (settings.TryGetValue("MinOffset", out object? minOffsetObject) && minOffsetObject is JsonElement minOffsetElement && minOffsetElement.ValueKind == JsonValueKind.String)
            {
                MinOffset = minOffsetElement.GetString();
            }

            if (settings.TryGetValue("MaxOffset", out object? maxOffsetObject) && maxOffsetObject is JsonElement maxOffsetElement && maxOffsetElement.ValueKind == JsonValueKind.String)
            {
                MaxOffset = maxOffsetElement.GetString();
            }

            if (settings.TryGetValue("NullProbability", out object? nullProbabilityObject) && nullProbabilityObject is JsonElement nullProbabilityElement && nullProbabilityElement.ValueKind == JsonValueKind.Number)
            {
                NullProbability = nullProbabilityElement.GetInt32();
            }
        }
    }
}
