using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.Data.Enums;
using Aubrant.TestDataGenerator.UI.Server.GraphQL;

namespace Aubrant.TestDataGenerator.UI.Server.Mutations
{
    [ExtendObjectType(typeof(Mutation))]
    public class FieldSettingMutations
    {
        public async Task<FieldSetting> CreateFieldSetting(int fieldId, GeneratorType type, string? name, string? settings, [Service] DatabaseContext context)
        {
            var fieldSetting = new FieldSetting
            {
                FieldId = fieldId,
                Type = type,
                Name = name,
                Settings = settings
            };

            context.FieldSettings.Add(fieldSetting);
            await context.SaveChangesAsync();

            return fieldSetting;
        }

        public async Task<FieldSetting> UpdateFieldSetting(int fieldId, GeneratorType? type, string? name, string? settings, [Service] DatabaseContext context)
        {
            var fieldSetting = await context.FieldSettings.FindAsync(fieldId);

            if (fieldSetting == null)
            {
                throw new System.Exception("FieldSetting not found.");
            }

            if (type.HasValue)
            {
                fieldSetting.Type = type.Value;
            }

            if (name != null)
            {
                fieldSetting.Name = name;
            }

            if (settings != null)
            {
                fieldSetting.Settings = settings;
            }

            await context.SaveChangesAsync();

            return fieldSetting;
        }

        public async Task<bool> DeleteFieldSetting(int fieldId, [Service] DatabaseContext context)
        {
            var fieldSetting = await context.FieldSettings.FindAsync(fieldId);

            if (fieldSetting == null)
            {
                return false;
            }

            context.FieldSettings.Remove(fieldSetting);
            await context.SaveChangesAsync();

            return true;
        }
    }
}
