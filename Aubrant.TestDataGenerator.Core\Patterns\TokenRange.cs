﻿namespace Aubrant.TestDataGenerator.Core.Patterns
{
    public class TokenRange
    {
        /// <summary>
        /// Min value in Range
        /// </summary>
        [System.ComponentModel.Description("Min value in Range")]
        public int Min { get; set; }

        /// <summary>
        /// Max value in Range
        /// </summary>
        [System.ComponentModel.Description("Max value in Range")]
        public int Max { get; set; }

        public TokenRange(int min, int max)
        {
            Min = min;
            Max = max;
        }

        public static implicit operator TokenRange((int Min, int Max) value)
        {
            return new TokenRange(value.Min, value.Max);
        }
    }
}