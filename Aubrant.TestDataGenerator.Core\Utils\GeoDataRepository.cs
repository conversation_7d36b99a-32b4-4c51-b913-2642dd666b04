using System.Reflection;
using System.Text.Json;
using Aubrant.TestDataGenerator.Core.Utils.Models;

namespace Aubrant.TestDataGenerator.Core.Utils
{
    public static class GeoDataRepository
    {
        private static List<Country>? _countries;
        private static readonly object _lock = new();

        public static List<Country> Countries
        {
            get
            {
                EnsureDataLoaded();
                return _countries!;
            }
        }

        private static void EnsureDataLoaded()
        {
            if (_countries != null) return;

            lock (_lock)
            {
                if (_countries != null) return;

                // Adjust path for embedded resource or direct file access
                // For simplicity, assuming direct file access for now. 
                // In a real application, consider embedding as a resource.
                string filePath = Path.Combine(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location)!,
                                               "Common", "geo_data.json");

                if (!File.Exists(filePath))
                {
                    // Fallback for development environment if file is not copied to output directory
                    filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Common", "geo_data.json");
                    if (!File.Exists(filePath))
                    {
                        // Another common path for source files during development
                        filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "Common", "geo_data.json");
                        if (!File.Exists(filePath))
                        {
                            throw new FileNotFoundException($"geo_data.json not found at {filePath} or other common locations.");
                        }
                    }
                }

                string jsonString = File.ReadAllText(filePath);
                _countries = JsonSerializer.Deserialize<List<Country>>(jsonString) ?? new List<Country>();
            }
        }

        public static Country? GetCountry(string countryIdentifier)
        {
            EnsureDataLoaded();
            return _countries?.FirstOrDefault(c => c.Code.Equals(countryIdentifier, StringComparison.OrdinalIgnoreCase) ||
                                                  c.Name.Equals(countryIdentifier, StringComparison.OrdinalIgnoreCase));
        }

        public static State? GetState(string countryIdentifier, string stateIdentifier)
        {
            EnsureDataLoaded();
            var country = GetCountry(countryIdentifier);
            return country?.States.FirstOrDefault(s => s.Code.Equals(stateIdentifier, StringComparison.OrdinalIgnoreCase) ||
                                                      s.Name.Equals(stateIdentifier, StringComparison.OrdinalIgnoreCase));
        }

        public static City? GetCity(string countryIdentifier, string stateIdentifier, string cityIdentifier)
        {
            EnsureDataLoaded();
            var state = GetState(countryIdentifier, stateIdentifier);
            return state?.Cities.FirstOrDefault(c => c.Name.Equals(cityIdentifier, StringComparison.OrdinalIgnoreCase));
        }

        public static string? GetCounty(string countryIdentifier, string stateIdentifier, string countyIdentifier)
        {
            EnsureDataLoaded();
            var state = GetState(countryIdentifier, stateIdentifier);
            return state?.Counties.FirstOrDefault(c => c.Equals(countyIdentifier, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Retrieves a random state from the specified country.
        /// A Random instance must be provided to ensure proper seeding and avoid duplicate sequences.
        /// </summary>
        /// <param name="countryIdentifier">The country code or name.</param>
        /// <param name="random">The Random instance to use for generating random numbers.</param>
        /// <returns>A random State object, or null if no matching states are found.</returns>
        public static State? GetRandomState(string countryIdentifier, Random random)
        {
            EnsureDataLoaded();
            var country = GetCountry(countryIdentifier);
            if (country?.States == null || !country.States.Any()) return null;
            return country.States[random.Next(country.States.Count)];
        }

        /// <summary>
        /// Retrieves a random city from the specified country and optional state.
        /// A Random instance must be provided to ensure proper seeding and avoid duplicate sequences.
        /// </summary>
        /// <param name="countryIdentifier">The country code or name.</param>
        /// <param name="random">The Random instance to use for generating random numbers.</param>
        /// <param name="stateIdentifier">Optional: The state code or name. If not provided, a random state will be selected.</param>
        /// <returns>A random City object, or null if no matching cities are found.</returns>
        public static City? GetRandomCity(string countryIdentifier, Random random, string? stateIdentifier = null)
        {
            EnsureDataLoaded();
            State? state = null;
            if (!string.IsNullOrWhiteSpace(stateIdentifier))
            {
                state = GetState(countryIdentifier, stateIdentifier);
            }
            else
            {
                state = GetRandomState(countryIdentifier, random);
            }

            if (state?.Cities == null || !state.Cities.Any()) return null;
            return state.Cities[random.Next(state.Cities.Count)];
        }

        /// <summary>
        /// Retrieves a random county from the specified country and optional state.
        /// A Random instance must be provided to ensure proper seeding and avoid duplicate sequences.
        /// </summary>
        /// <param name="countryIdentifier">The country code or name.</param>
        /// <param name="random">The Random instance to use for generating random numbers.</param>
        /// <param name="stateIdentifier">Optional: The state code or name. If not provided, a random state will be selected.</param>
        /// <returns>A random county name, or null if no matching counties are found.</returns>
        public static string? GetRandomCounty(string countryIdentifier, Random random, string? stateIdentifier = null)
        {
            EnsureDataLoaded();
            State? state = null;
            if (!string.IsNullOrWhiteSpace(stateIdentifier))
            {
                state = GetState(countryIdentifier, stateIdentifier);
            }
            else
            {
                state = GetRandomState(countryIdentifier, random);
            }

            if (state?.Counties == null || !state.Counties.Any()) return null;
            return state.Counties[random.Next(state.Counties.Count)];
        }
    }
}
