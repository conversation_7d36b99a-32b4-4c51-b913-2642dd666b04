using Aubrant.TestDataGenerator.Data.Enums;
using HotChocolate;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;

namespace Aubrant.TestDataGenerator.Data
{
    /// <summary>
    /// Represents an entity (e.g., a table in a relational database, a collection in NoSQL, or a sheet/file).
    /// </summary>
    public class Entity
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int DataSourceId { get; set; }

        /// <summary>
        /// Gets or sets the name of the entity.
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// Gets or sets the schema name of the entity (e.g., database schema).
        /// </summary>
        public string? Schema { get; set; }

        public SyncStrategy? SyncStrategy { get; set; }

        [ForeignKey("DataSourceId")]
        public virtual DataSource? DataSource { get; set; }

        /// <summary>
        /// Gets or sets the list of fields (columns) belonging to this entity.
        /// </summary>
        public virtual IList<Field> Fields { get; set; } = new List<Field>();

        /// <summary>
        /// Gets or sets the list of key constraints (primary, foreign) defined for this entity.
        /// </summary>
        public virtual ICollection<KeyConstraint> KeyConstraints { get; set; } = new List<KeyConstraint>();

        /// <summary>
        /// Gets or sets the list of relationships this entity participates in.
        /// </summary>
        public virtual ICollection<Relationship> Relationships { get; set; } = new List<Relationship>();

        /// <summary>
        /// Gets or sets the sample data loaded for this entity, stored in a DataTable.
        /// </summary>
        [NotMapped]
        [GraphQLIgnore]
        public DataTable Data { get; set; } = new DataTable();
    }
}