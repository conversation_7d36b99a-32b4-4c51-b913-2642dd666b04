using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Functions;
using System;

namespace Aubrant.TestDataGenerator.Generators.Functions{
    /// <summary>
    /// A generator function that produces random integer values following a Poisson distribution.
    /// </summary>
    public class PoissonDistributionFunction : BaseFunction
    {
        public override string Name => "Poisson Distribution";
        public override string Description => "Generates random integers from a Poisson distribution.";
        public override DataType ReturnType => DataType.Integer;

        /// <summary>
        /// Initializes a new instance of the PoissonDistributionFunction class.
        /// Defines a 'Lambda' parameter for the Poisson distribution.
        /// </summary>
        public PoissonDistributionFunction() : base("Statistical")
        {
            Parameters.Add(new Parameter("Lambda", "The lambda of the distribution.", DataType.Decimal, new DataValue(1.0M))); // Default lambda is 1.0
        }

        /// <summary>
        /// Generates a random integer value from a Poisson distribution based on the configured Lambda.
        /// Uses the <PERSON><PERSON>h algorithm for Poisson variate generation.
        /// </summary>
        /// <param name="context">The data generator context.</param>
        /// <returns>A DataValue containing the Poisson-distributed integer number.</returns>
        /// <exception cref="InvalidOperationException">Thrown if the Lambda parameter is missing or invalid.</exception>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if Lambda is non-positive.</exception>
        public override DataValue Generate(DataGeneratorContext context)
        {
            decimal lambda = Parameters["Lambda"].Value?.ToDecimal() ??
                             throw new InvalidOperationException("Lambda parameter is missing or invalid.");

            if (lambda <= 0)
            {
                throw new ArgumentOutOfRangeException(nameof(lambda), "Lambda must be positive for Poisson distribution.");
            }

            // Knuth's algorithm for Poisson variate generation
            int k = 0;
            double p = 1.0;
            double L = Math.Exp(-(double)lambda);

            do
            {
                k++;
                p *= context.Random.NextDouble();
            } while (p > L);

            return new DataValue(DataType.Integer, k - 1);
        }
    }
}
