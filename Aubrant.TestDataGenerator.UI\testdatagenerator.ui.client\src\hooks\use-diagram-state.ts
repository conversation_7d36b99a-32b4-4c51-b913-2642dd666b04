import { useState, useCallback, useRef, useEffect } from "react";
import type { Entity, Relationship, Field } from "../types/database";
import {
  arrangeLeftRight as arrangeLeftRightUtil,
  arrangeSnowflake as arrangeSnowflakeUtil,
  arrangeCompact as arrangeCompactUtil,
} from "../../utils/arrangement-utils";

export const useDiagramState = (initialEntities: Entity[]) => {
  const [entities, setEntities] = useState<Entity[]>(initialEntities);
  const [selectedEntities, setSelectedEntities] = useState<string[]>([]);
  const [activeEntity, setActiveEntity] = useState<string | null>(null);
  const [selectedField, setSelectedField] = useState<Field | null>(null);
  const [selectedRelationship, setSelectedRelationship] = useState<{
    relationship: Relationship;
    sourceEntity: Entity;
    targetEntity: Entity;
  } | null>(null);
  const [scale, setScale] = useState(1);
  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 });
  const [isPanning, setIsPanning] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [draggingEntities, setDraggingEntities] = useState<string[]>([]);
  const [draggedEntityName, setDraggedEntityName] = useState<string | null>(null);
  
  const initialMouseDiagramPosition = useRef<{ x: number; y: number } | null>(null);
  const initialDraggingEntitiesPositions = useRef<Record<string, { x: number; y: number }>>({});
  const originalDraggingEntitiesPositions = useRef<Record<string, { x: number; y: number }>>({});
  const [alignmentGuides, setAlignmentGuides] = useState<{
    x?: number;
    y?: number;
  }>({});
  const [marquee, setMarquee] = useState<{
    x: number;
    y: number;
    width: number;
    height: number;
  } | null>(null);
  const [marqueeStartCoords, setMarqueeStartCoords] = useState<{ x: number; y: number } | null>(null);
  const hasInitiallyFitted = useRef(false);

  const canvasRef = useRef<HTMLDivElement>(null);
  const svgRef = useRef<SVGSVGElement>(null);

  const SNAP_THRESHOLD = 10;

  const getEntityDimensions = useCallback((entity: Entity) => {
    const PADDING_Y = 16;
    const HEADER_HEIGHT = 49;
    const FIELD_HEIGHT = 28;
    const FIELD_GAP = 4;
    const contentHeight =
      entity.Fields.length * (FIELD_HEIGHT + FIELD_GAP) - FIELD_GAP;
    const width = 320;
    const height = HEADER_HEIGHT + contentHeight + PADDING_Y;
    return { width, height };
  }, []);

  const arrangeLeftRight = useCallback(() => {
    arrangeLeftRightUtil(entities, getEntityDimensions, setEntities);
  }, [entities, getEntityDimensions]);

  const arrangeSnowflake = useCallback(() => {
    arrangeSnowflakeUtil(entities, getEntityDimensions, setEntities);
  }, [entities, getEntityDimensions]);

  const arrangeCompact = useCallback(() => {
    arrangeCompactUtil(entities, getEntityDimensions, setEntities);
  }, [entities, getEntityDimensions]);

  const fitToWindow = useCallback(() => {
    if (!canvasRef.current) return;

    const canvasRect = canvasRef.current.getBoundingClientRect();
    const canvasWidth = canvasRect.width;
    const canvasHeight = canvasRect.height;

    let minX = Number.POSITIVE_INFINITY,
      minY = Number.POSITIVE_INFINITY,
      maxX = Number.NEGATIVE_INFINITY,
      maxY = Number.NEGATIVE_INFINITY;

    entities.forEach((entity) => {
      if (entity.position) {
        const entitySize = getEntityDimensions(entity);
        minX = Math.min(minX, entity.position.x);
        minY = Math.min(minY, entity.position.y);
        maxX = Math.max(maxX, entity.position.x + entitySize.width);
        maxY = Math.max(maxY, entity.position.y + entitySize.height);
      }
    });

    if (minX === Number.POSITIVE_INFINITY) return;

    const padding = 100;
    const contentWidth = maxX - minX + padding * 2;
    const contentHeight = maxY - minY + padding * 2;

    const scaleX = canvasWidth / contentWidth;
    const scaleY = canvasHeight / contentHeight;
    const newScale = Math.min(scaleX, scaleY, 1);

    const centerX = (canvasWidth - contentWidth * newScale) / 2;
    const centerY = (canvasHeight - contentHeight * newScale) / 2;
    const offsetX = centerX - (minX - padding) * newScale;
    const offsetY = centerY - (minY - padding) * newScale;

    setScale(newScale);
    setPanOffset({ x: offsetX, y: offsetY });
  }, [entities, getEntityDimensions]);

  const zoomTo100 = useCallback(() => {
    setScale(1);
    setPanOffset({ x: 0, y: 0 });
  }, []);

  const zoomIn = useCallback(() => {
    setScale((prev) => Math.min(prev * 1.2, 3));
  }, []);

  const zoomOut = useCallback(() => {
    setScale((prev) => Math.max(prev / 1.2, 0.1));
  }, []);

  useEffect(() => {
    const needsPositioning = entities.some((entity) => !entity.position);
    if (needsPositioning) {
      const entitiesWithPositions = entities.map((entity, index) => {
        if (!entity.position) {
          const col = index % 3;
          const row = Math.floor(index / 3);
          return {
            ...entity,
            position: {
              x: col * 400 + 100,
              y: row * 350 + 100,
            },
          };
        }
        return entity;
      });
      setEntities(entitiesWithPositions);
    }
  }, [initialEntities]);

  useEffect(() => {
    if (
      !hasInitiallyFitted.current &&
      entities.length > 0 &&
      entities.every((e) => e.position)
    ) {
      const timer = setTimeout(() => {
        fitToWindow();
        hasInitiallyFitted.current = true;
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [entities, fitToWindow]);

  const handleFitToWindow = useCallback(() => {
    fitToWindow();
  }, [fitToWindow]);

  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      const rect = canvasRef.current?.getBoundingClientRect();
      if (!rect) return;

      const canvasX = (e.clientX - rect.left - panOffset.x) / scale;
      const canvasY = (e.clientY - rect.top - panOffset.y) / scale;

      if (e.target === canvasRef.current || e.target === svgRef.current) {
        if (e.metaKey || e.ctrlKey) {
          setMarquee({ x: canvasX, y: canvasY, width: 0, height: 0 });
          setMarqueeStartCoords({ x: canvasX, y: canvasY });
        } else {
          setIsPanning(true);
          setDragStart({
            x: e.clientX - panOffset.x,
            y: e.clientY - panOffset.y,
          });
          setSelectedEntities([]);
          setActiveEntity(null);
          setSelectedField(null);
          setSelectedRelationship(null);
        }
      }
    },
    [panOffset]
  );

  const handleMouseMove = useCallback(
    (e: React.MouseEvent) => {
      if (isPanning && draggingEntities.length === 0) {
        setPanOffset({
          x: e.clientX - dragStart.x,
          y: e.clientY - dragStart.y,
        });
      } else if (marquee && marqueeStartCoords) {
        const rect = canvasRef.current?.getBoundingClientRect();
        if (!rect) return;
        const currentX = (e.clientX - rect.left - panOffset.x) / scale;
        const currentY = (e.clientY - rect.top - panOffset.y) / scale;
        setMarquee((prev) => {
          if (!prev) return null;
          const startX = marqueeStartCoords.x;
          const startY = marqueeStartCoords.y;
          const endX = currentX;
          const endY = currentY;

          const normalizedX = Math.min(startX, endX);
          const normalizedY = Math.min(startY, endY);
          const normalizedWidth = Math.abs(endX - startX);
          const normalizedHeight = Math.abs(endY - startY);

          return {
            x: normalizedX,
            y: normalizedY,
            width: normalizedWidth,
            height: normalizedHeight,
          };
        });
      }
    },
    [isPanning, dragStart, draggingEntities, marquee, marqueeStartCoords, scale, panOffset]
  );

  const handleMouseUp = useCallback(() => {
    if (marquee) {
      const marqueeRect = {
        x: marquee.x,
        y: marquee.y,
        width: marquee.width,
        height: marquee.height,
      };

      const selected = entities.filter((entity) => {
        if (!entity.position) return false;
        const entityDims = getEntityDimensions(entity);
        const entityRect = {
          x: entity.position.x,
          y: entity.position.y,
          width: entityDims.width,
          height: entityDims.height,
        };

        return (
          entityRect.x < marqueeRect.x + marqueeRect.width &&
          entityRect.x + entityRect.width > marqueeRect.x &&
          entityRect.y < marqueeRect.y + marqueeRect.height &&
          entityRect.y + entityRect.height > marqueeRect.y
        );
      });

      const selectedNames = selected.map((e) => e.Name);
      setSelectedEntities((prev) => [...new Set([...prev, ...selectedNames])]);
      if (selectedNames.length > 0) {
        setActiveEntity(selectedNames[selectedNames.length - 1]);
      }
    }

    setIsPanning(false);
    setDraggingEntities([]);
    setAlignmentGuides({});
    setDragStart({ x: 0, y: 0 });
    setMarquee(null);
    setMarqueeStartCoords(null);
    setDraggedEntityName(null);
    initialMouseDiagramPosition.current = null;
    initialDraggingEntitiesPositions.current = {};
  }, [marquee, entities, getEntityDimensions, scale, panOffset]);

  const resetDragState = useCallback(() => {
    setIsPanning(false);
    setDraggingEntities([]);
    setAlignmentGuides({});
    setDragStart({ x: 0, y: 0 });
    setMarquee(null);
    setMarqueeStartCoords(null);
    setDraggedEntityName(null);
    initialMouseDiagramPosition.current = null;
    initialDraggingEntitiesPositions.current = {};
    originalDraggingEntitiesPositions.current = {};
  }, []);

  const handleWheel = useCallback((e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    setScale((prev) => Math.max(0.1, Math.min(3, prev * delta)));
  }, []);

  const handleEntityClick = useCallback((entity: Entity, e: React.MouseEvent) => {
    if (e.metaKey || e.ctrlKey) {
      setSelectedEntities((prev) =>
        prev.includes(entity.Name)
          ? prev.filter((name) => name !== entity.Name)
          : [...prev, entity.Name]
      );
    } else {
      setSelectedEntities([entity.Name]);
    }
    setActiveEntity(entity.Name);
    setSelectedField(null);
    setSelectedRelationship(null);
  }, []);

  const handleFieldClick = useCallback((field: Field, entity: Entity) => {
    setSelectedField(field);
    setActiveEntity(entity.Name);
    setSelectedEntities([entity.Name]);
    setSelectedRelationship(null);
  }, []);

  const handleRelationshipClick = useCallback(
    (processedRelationship: any, e: React.MouseEvent) => {
      e.stopPropagation();
      setSelectedRelationship({
        relationship: processedRelationship.relationship,
        sourceEntity: processedRelationship.sourceEntity,
        targetEntity: processedRelationship.targetEntity,
      });
      setSelectedEntities([]);
      setActiveEntity(null);
      setSelectedField(null);
    },
    []
  );

  const handleEntityMouseDown = useCallback(
    (entityName: string, e: React.MouseEvent) => {
      e.stopPropagation();

      const entity = entities.find((e) => e.Name === entityName);
      if (!entity?.position) return;

      const isSelected = selectedEntities.includes(entityName);

      if (!isSelected && !e.metaKey && !e.ctrlKey) {
        setSelectedEntities([entityName]);
        setActiveEntity(entityName);
        setDraggingEntities([entityName]);
      } else {
        setDraggingEntities(selectedEntities.length > 0 ? selectedEntities : [entityName]);
      }
      setDraggedEntityName(entityName);

      const rect = canvasRef.current?.getBoundingClientRect();
      if (rect) {
        const canvasX = (e.clientX - rect.left - panOffset.x) / scale;
        const canvasY = (e.clientY - rect.top - panOffset.y) / scale;
        initialMouseDiagramPosition.current = { x: canvasX, y: canvasY };

        const initialPositions: Record<string, { x: number; y: number }> = {};
        const entitiesToDrag = isSelected && selectedEntities.length > 0 ? selectedEntities : [entityName];
        entitiesToDrag.forEach(name => {
          const foundEntity = entities.find(e => e.Name === name);
          if (foundEntity?.position) {
            initialPositions[name] = { ...foundEntity.position };
            originalDraggingEntitiesPositions.current[name] = { ...foundEntity.position };
          }
        });
        initialDraggingEntitiesPositions.current = initialPositions;
      }
    },
    [entities, panOffset, scale, selectedEntities]
  );

  const handleEntityDrag = useCallback(
    (e: MouseEvent) => {
      if (draggingEntities.length === 0) return;

      const rect = canvasRef.current?.getBoundingClientRect();
      if (!rect) return;

      const canvasX = (e.clientX - rect.left - panOffset.x) / scale;
      const canvasY = (e.clientY - rect.top - panOffset.y) / scale;

      if (!initialMouseDiagramPosition.current) return;

      const deltaX = canvasX - initialMouseDiagramPosition.current.x;
      const deltaY = canvasY - initialMouseDiagramPosition.current.y;

      const guides: { x?: number; y?: number; yMiddle?: number } = {};
      const otherEntities = entities.filter(
        (entity) => !draggingEntities.includes(entity.Name)
      );

      // Calculate the new position for the *clicked* entity to use for snapping
      const clickedEntity = entities.find(e => e.Name === draggedEntityName);
      if (!clickedEntity || !initialDraggingEntitiesPositions.current[clickedEntity.Name]) return;

      const clickedEntityInitialPos = initialDraggingEntitiesPositions.current[clickedEntity.Name];
      const newClickedEntityX = clickedEntityInitialPos.x + deltaX;
      const newClickedEntityY = clickedEntityInitialPos.y + deltaY;

      const clickedEntityDims = getEntityDimensions(clickedEntity);

      const draggingPoints = {
        x: {
          left: newClickedEntityX,
          center: newClickedEntityX + clickedEntityDims.width / 2,
          right: newClickedEntityX + clickedEntityDims.width,
        },
        y: {
          top: newClickedEntityY,
          middle: newClickedEntityY + clickedEntityDims.height / 2,
          bottom: newClickedEntityY + clickedEntityDims.height,
        },
      };

      let finalDeltaX = deltaX;
      let finalDeltaY = deltaY;

      for (const entity of otherEntities) {
        if (entity.position) {
          const entityDims = getEntityDimensions(entity);
          const targetPoints = {
            x: {
              left: entity.position.x,
              center: entity.position.x + entityDims.width / 2,
              right: entity.position.x + entityDims.width,
            },
            y: {
              top: entity.position.y,
              middle: entity.position.y + entityDims.height / 2,
              bottom: entity.position.y + entityDims.height,
            },
          };

          if (guides.x === undefined) {
            if (Math.abs(draggingPoints.x.left - targetPoints.x.left) < SNAP_THRESHOLD) {
              guides.x = targetPoints.x.left;
              finalDeltaX = targetPoints.x.left - clickedEntityInitialPos.x;
            } else if (Math.abs(draggingPoints.x.right - targetPoints.x.right) < SNAP_THRESHOLD) {
              guides.x = targetPoints.x.right;
              finalDeltaX = (targetPoints.x.right - clickedEntityDims.width) - clickedEntityInitialPos.x;
            } else if (Math.abs(draggingPoints.x.center - targetPoints.x.center) < SNAP_THRESHOLD) {
              guides.x = targetPoints.x.center;
              finalDeltaX = (targetPoints.x.center - clickedEntityDims.width / 2) - clickedEntityInitialPos.x;
            }
          }

          if (guides.y === undefined) {
            if (Math.abs(draggingPoints.y.top - targetPoints.y.top) < SNAP_THRESHOLD) {
              guides.y = targetPoints.y.top;
              finalDeltaY = targetPoints.y.top - clickedEntityInitialPos.y;
            } else if (Math.abs(draggingPoints.y.bottom - targetPoints.y.bottom) < SNAP_THRESHOLD) {
              guides.y = targetPoints.y.bottom;
              finalDeltaY = (targetPoints.y.bottom - clickedEntityDims.height) - clickedEntityInitialPos.y;
            } else if (Math.abs(draggingPoints.y.middle - targetPoints.y.middle) < SNAP_THRESHOLD) {
              guides.y = targetPoints.y.middle;
              finalDeltaY = (targetPoints.y.middle - clickedEntityDims.height / 2) - clickedEntityInitialPos.y;
            }
          }
        }
        if (guides.x !== undefined && guides.y !== undefined) break;
      }

      setAlignmentGuides(guides);

      setEntities((prev) =>
        prev.map((entity) => {
          if (draggingEntities.includes(entity.Name) && entity.position) {
            const initialPos = initialDraggingEntitiesPositions.current[entity.Name];
            if (!initialPos) return entity; // Should not happen
            return {
              ...entity,
              position: {
                x: initialPos.x + finalDeltaX,
                y: initialPos.y + finalDeltaY,
              },
            };
          }
          return entity;
        })
      );
    },
    [
      draggingEntities,
      panOffset,
      scale,
      entities,
      getEntityDimensions,
      draggedEntityName,
      initialMouseDiagramPosition,
      initialDraggingEntitiesPositions,
    ]
  );

  useEffect(() => {
    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (draggingEntities.length > 0) {
        e.preventDefault();
        handleEntityDrag(e);
      }
    };

    const handleGlobalMouseUp = (e: MouseEvent) => {
      if (e.button === 2) { // Right-click
        // Cancel drag
        setEntities(prev =>
          prev.map(entity => {
            if (originalDraggingEntitiesPositions.current[entity.Name]) {
              return { ...entity, position: originalDraggingEntitiesPositions.current[entity.Name] };
            }
            return entity;
          })
        );
      }
      resetDragState();
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        // Cancel drag
        setEntities(prev =>
          prev.map(entity => {
            if (originalDraggingEntitiesPositions.current[entity.Name]) {
              return { ...entity, position: originalDraggingEntitiesPositions.current[entity.Name] };
            }
            return entity;
          })
        );
        resetDragState();
      }
    };

    if (draggingEntities.length > 0 || isPanning) {
      document.addEventListener("mousemove", handleGlobalMouseMove);
      document.addEventListener("mouseup", handleGlobalMouseUp);
      document.addEventListener("keydown", handleKeyDown);
    }

    return () => {
      document.removeEventListener("mousemove", handleGlobalMouseMove);
      document.removeEventListener("mouseup", handleGlobalMouseUp);
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [draggingEntities, isPanning, handleEntityDrag, resetDragState]);

  const zoomToSelection = useCallback(() => {
    if (!canvasRef.current || selectedEntities.length === 0) return;

    const canvasRect = canvasRef.current.getBoundingClientRect();
    const canvasWidth = canvasRect.width;
    const canvasHeight = canvasRect.height;

    let minX = Number.POSITIVE_INFINITY,
      minY = Number.POSITIVE_INFINITY,
      maxX = Number.NEGATIVE_INFINITY,
      maxY = Number.NEGATIVE_INFINITY;

    const selected = entities.filter(e => selectedEntities.includes(e.Name));

    selected.forEach((entity) => {
      if (entity.position) {
        const entitySize = getEntityDimensions(entity);
        minX = Math.min(minX, entity.position.x);
        minY = Math.min(minY, entity.position.y);
        maxX = Math.max(maxX, entity.position.x + entitySize.width);
        maxY = Math.max(maxY, entity.position.y + entitySize.height);
      }
    });

    if (minX === Number.POSITIVE_INFINITY) return;

    const padding = 50; // Padding around the selected entities
    const contentWidth = maxX - minX + padding * 2;
    const contentHeight = maxY - minY + padding * 2;

    const scaleX = canvasWidth / contentWidth;
    const scaleY = canvasHeight / contentHeight;
    const newScale = Math.min(scaleX, scaleY, 1); // Don't zoom in beyond 100%

    const centerX = (canvasWidth - contentWidth * newScale) / 2;
    const centerY = (canvasHeight - contentHeight * newScale) / 2;
    const offsetX = centerX - (minX - padding) * newScale;
    const offsetY = centerY - (minY - padding) * newScale;

    setScale(newScale);
    setPanOffset({ x: offsetX, y: offsetY });
  }, [entities, selectedEntities, getEntityDimensions]);

  return {
    entities,
    selectedEntities,
    activeEntity,
    selectedField,
    selectedRelationship,
    scale,
    panOffset,
    alignmentGuides,
    marquee,
    canvasRef,
    svgRef,
    arrangeLeftRight,
    arrangeSnowflake,
    arrangeCompact,
    fitToWindow,
    zoomTo100,
    zoomIn,
    zoomOut,
    handleFitToWindow,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleWheel,
    handleEntityClick,
    handleFieldClick,
    handleRelationshipClick,
    handleEntityMouseDown,
    zoomToSelection,
  };
};
