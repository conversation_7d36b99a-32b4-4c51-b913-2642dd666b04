import { Separator } from "@/components/ui/separator"
import type { Entity } from "../types/database";

interface EntityDetailsPanelProps {
  selectedEntity: Entity
}

export function EntityDetailsPanel({
  selectedEntity,
}: EntityDetailsPanelProps) {
  return (
    <div className="space-y-4">
      <div>
        <h3 className="font-semibold text-base mb-2">Table Details</h3>
        <div className="space-y-2">
          <div>
            <label className="text-sm font-medium">Name</label>
            <p className="text-sm text-muted-foreground">
              {selectedEntity.Name}
            </p>
          </div>
          <div>
            <label className="text-sm font-medium">Schema</label>
            <p className="text-sm text-muted-foreground">
              {selectedEntity.Schema}
            </p>
          </div>
          <div>
            <label className="text-sm font-medium">Full Name</label>
            <p className="text-sm text-muted-foreground">
              {selectedEntity.Schema}.{selectedEntity.Name}
            </p>
          </div>
        </div>
      </div>
      <Separator />
      <div>
        <h4 className="font-medium mb-2">Statistics</h4>
        <div className="space-y-1">
          <div className="flex justify-between text-sm">
            <span>Fields</span>
            <span>{selectedEntity.Fields.length}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Primary Keys</span>
            <span>
              {selectedEntity.Fields.filter((f) => f.IsPrimaryKey).length}
            </span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Relationships</span>
            <span>{selectedEntity.Relationships.length}</span>
          </div>
        </div>
      </div>
      <Separator />
      <div>
        <h4 className="font-medium mb-2">Relationships</h4>
        <div className="space-y-2">
          {selectedEntity.Relationships.map((rel, index) => (
            <div key={index} className="text-sm p-2 bg-muted/50 rounded">
              <div className="font-medium">{rel.Cardinality}</div>
              <div className="text-muted-foreground">
                {rel.SourceField} →{" "}
                {rel.TargetEntity.split(".")[1] || rel.TargetEntity}.
                {rel.TargetField}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
