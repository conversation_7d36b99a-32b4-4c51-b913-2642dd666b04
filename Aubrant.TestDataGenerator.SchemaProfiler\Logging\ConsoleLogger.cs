using Aubrant.TestDataGenerator.SchemaProfiler.Enums;

namespace Aubrant.TestDataGenerator.SchemaProfiler.Logging
{
    /// <summary>
    /// Provides a console-based implementation of the ILogger interface.
    /// </summary>
    public class ConsoleLogger : ILogger
    {
        /// <summary>
        /// Gets the logging levels that are currently verbose (will be outputted).
        /// </summary>
        public LogLevel VerboseLogLevels { get; private set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="ConsoleLogger"/> class.
        /// </summary>
        /// <param name="verboseLogLevels">The logging levels to output. Defaults to All.</param>
        public ConsoleLogger(LogLevel verboseLogLevels = LogLevel.All)
        {
            VerboseLogLevels = verboseLogLevels;
        }

        /// <summary>
        /// Logs an informational message to the console.
        /// </summary>
        /// <param name="message">The message to log.</param>
        public void LogInfo(string message)
        {
            if (VerboseLogLevels.HasFlag(LogLevel.Info))
            {
                Console.WriteLine($"[INFO] {message}");
            }
        }

        /// <summary>
        /// Logs a warning message to the console.
        /// </summary>
        /// <param name="message">The message to log.</param>
        public void LogWarning(string message)
        {
            if (VerboseLogLevels.HasFlag(LogLevel.Warning))
            {
                Console.WriteLine($"[WARNING] {message}");
            }
        }

        /// <summary>
        /// Logs an error message to the console, optionally with exception details.
        /// </summary>
        /// <param name="message">The message to log.</param>
        /// <param name="ex">The exception to log (optional).</param>
        public void LogError(string message, Exception? ex = null)
        {
            if (VerboseLogLevels.HasFlag(LogLevel.Error))
            {
                Console.WriteLine($"[ERROR] {message}");
                if (ex != null) Console.WriteLine($"  Exception: {ex.GetType().Name} - {ex.Message}");
            }
        }
    }
}