﻿using Bogus;
using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using static Bogus.DataSets.Name;
using Aubrant.TestDataGenerator.Core.Functions;

namespace Aubrant.TestDataGenerator.Generators.Functions
{
    /// <summary>
    /// A generator function that produces realistic first names based on a specified locale and optional gender.
    /// It utilizes the Bogus library for name generation.
    /// </summary>
    public class FirstNameFunctionGenerator : BaseFunction
    {
        public override string Name => "FirstName";
        public override string Description => "Generates a realistic first name based on `locale` and optional `gender`.";
        public override DataType ReturnType => DataType.String;

        /// <summary>
        /// Initializes a new instance of the FirstNameFunctionGenerator class.
        /// Defines 'locale' parameter with 'en' default and an optional 'gender' parameter.
        /// </summary>
        public FirstNameFunctionGenerator() : base("Person")
        {
            Parameters.Add(new Parameter("locale", "The output locale.", DataType.String, new DataValue("en"))); // Default locale is English
                                                                                                                 // Gender parameter is optional, so no default value is provided.
                                                                                                                 // It's a string, which will be parsed into Bogus.DataSets.Name.Gender internally.
            Parameters.Add(new Parameter("gender", "The gender of the person.", DataType.String, null));
        }

        /// <summary>
        /// Generates a random first name using the Bogus library, considering the specified locale and gender.
        /// If gender is not specified or invalid, it defaults to any gender names.
        /// </summary>
        /// <returns>A DataValue containing the generated first name as a string.</returns>
        /// <exception cref="InvalidOperationException">Thrown if the 'locale' parameter is missing or invalid.</exception>
        /// <exception cref="ArgumentException">Thrown if an invalid gender string is provided.</exception>
        public override DataValue Generate(DataGeneratorContext context)
        {
            string locale = Parameters["locale"].Value?.ToString() ??
                            throw new InvalidOperationException("Locale parameter is missing or invalid.");

            string? genderString = Parameters["gender"].Value?.ToString();

            Gender? gender = null; // Bogus Gender enum
            if (!string.IsNullOrWhiteSpace(genderString))
            {
                // Handle specific common inputs like "male" and "female" (case-insensitive)
                if (genderString.Equals("male", StringComparison.OrdinalIgnoreCase) || (genderString.Equals("m", StringComparison.OrdinalIgnoreCase)))
                {
                    gender = Gender.Male;
                }
                else if (genderString.Equals("female", StringComparison.OrdinalIgnoreCase) || (genderString.Equals("f", StringComparison.OrdinalIgnoreCase)))
                {
                    gender = Gender.Female;
                }
                else
                {
                    // If the string is neither "Male", "Female", nor empty/null, it's an invalid input.
                    throw new ArgumentException($"Invalid gender specified: '{genderString}'. Expected 'Male', 'M', 'Female', 'F', or empty/null.");
                }
            }
            // If genderString is null or empty, `gender` remains null, meaning `Faker.Name.FirstName()` will be called without a gender parameter,
            // which defaults to random gender. This aligns with the "any gender names" requirement.

            // Bogus can throw FormatException if locale is invalid
            try
            {
                var faker = new Faker(locale);
                string firstName;

                if (gender.HasValue)
                {
                    firstName = faker.Name.FirstName(gender.Value);
                }
                else
                {
                    // If gender is not specified (null), let Bogus pick any gender.
                    firstName = faker.Name.FirstName();
                }

                return new DataValue(DataType.String, firstName);
            }
            catch (FormatException ex)
            {
                throw new InvalidOperationException($"Invalid locale '{locale}' provided for FirstNameFunctionGenerator.", ex);
            }
        }
    }
}
