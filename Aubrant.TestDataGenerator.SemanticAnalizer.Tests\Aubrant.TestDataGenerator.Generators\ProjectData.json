{"data": {"projects": [{"id": 1, "name": "Test Data Generator - Project 1", "description": "First Project with Northwind Small DB, with Schema Analysis loaded from Sai's Excel", "author": "<PERSON><PERSON>", "dataSources": [{"id": 1, "name": "northwind_small", "type": "RELATIONAL_DATABASE", "provider": "SQ_LITE", "connectionString": "Data Source=C:\\Users\\<USER>\\source\\repos\\TestDataGenerator\\Aubrant.TestDataGenerator.Data\\Database\\northwind_small.sqlite", "entities": [{"id": 1, "name": "Employee", "fields": [{"id": 1, "name": "Id", "type": "INTEGER", "nativeType": "INTEGER", "maxLength": null, "isPrimaryKey": true, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "AUTO_INCREMENT", "name": null, "settings": "{\n  \"Start\": 1,\n  \"Step\": 1\n}"}}, {"id": 2, "name": "LastName", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "LastName", "settings": "{\n  \"locale\": \"en\"\n}"}}, {"id": 3, "name": "FirstName", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "FirstName", "settings": "{\n  \"locale\": \"en\"\n}"}}, {"id": 4, "name": "Title", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "AI", "name": null, "settings": "{\n  \"Prompt\": \"Generate a realistic job title for an employee.\",\n  \"MaxTokens\": 10\n}"}}, {"id": 5, "name": "TitleOfCourtesy", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "CHOICE", "name": null, "settings": "{\n  \"Options\": [\n    {\n      \"Value\": \"Mr.\",\n      \"Probability\": 0.4\n    },\n    {\n      \"Value\": \"Ms.\",\n      \"Probability\": 0.4\n    },\n    {\n      \"Value\": \"Mrs.\",\n      \"Probability\": 0.2\n    }\n  ]\n}"}}, {"id": 6, "name": "BirthDate", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "RANDOM", "name": null, "settings": "{\n  \"Min\": \"1950-01-01\",\n  \"Max\": \"2005-12-31\",\n  \"DataType\": \"Date\"\n}"}}, {"id": 7, "name": "HireDate", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "RANDOM", "name": null, "settings": "{\n  \"Min\": \"2020-01-01\",\n  \"Max\": \"2026-12-31\",\n  \"DataType\": \"Date\"\n}"}}, {"id": 8, "name": "Address", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "Address", "settings": "{\n  \"country\": \"US\",\n  \"locale\": \"en\",\n  \"state\": null,\n  \"city\": null,\n  \"postalCode\": null\n}"}}, {"id": 9, "name": "City", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "City", "settings": "{\n  \"country\": \"US\",\n  \"locale\": \"en\"\n}"}}, {"id": 10, "name": "Region", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "State", "settings": "{\n  \"country\": \"US\",\n  \"locale\": \"en\"\n}"}}, {"id": 11, "name": "PostalCode", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "PostalCode", "settings": "{\n  \"country\": \"US\",\n  \"state\": null,\n  \"city\": null\n}"}}, {"id": 12, "name": "Country", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "Choice", "settings": "{\n  \"Options\": [\n    {\n      \"Value\": \"USA\",\n      \"Probability\": 0.5\n    },\n    {\n      \"Value\": \"UK\",\n      \"Probability\": 0.5\n    }\n  ]\n}"}}, {"id": 13, "name": "HomePhone", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "PATTERN", "name": "US Phone Number", "settings": "{}"}}, {"id": 14, "name": "Extension", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "RANDOM", "name": null, "settings": "{\n  \"Min\": 1000,\n  \"Max\": 9999,\n  \"DataType\": \"Number\"\n}"}}, {"id": 15, "name": "Photo", "type": "BINARY", "nativeType": "BLOB", "maxLength": null, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "AI", "name": null, "settings": "{\n  \"Prompt\": \"Generate a random binary representation of a photo.\",\n  \"MaxTokens\": 256\n}"}}, {"id": 16, "name": "Notes", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "LoremIpsum", "settings": "{\n  \"Type\": \"Sentences\",\n  \"Count\": 3\n}"}}, {"id": 17, "name": "ReportsTo", "type": "INTEGER", "nativeType": "INTEGER", "maxLength": null, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "AutoIncrement", "settings": "{\n  \"Start\": 1,\n  \"Step\": 1\n}"}}, {"id": 18, "name": "PhotoPath", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "Constant", "name": "Constant", "settings": "{\n  \"Value\": \"http://accweb/employees/default.bmp\"\n}"}}], "relationships": []}, {"id": 2, "name": "Category", "fields": [{"id": 19, "name": "Id", "type": "INTEGER", "nativeType": "INTEGER", "maxLength": null, "isPrimaryKey": true, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "AUTO_INCREMENT", "name": null, "settings": "{\n  \"Start\": 1,\n  \"Step\": 1\n}"}}, {"id": 20, "name": "CategoryName", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "CompanyName", "settings": "{\n  \"locale\": \"en\"\n}"}}, {"id": 21, "name": "Description", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "LoremIpsum", "settings": "{\n  \"Type\": \"Sentences\",\n  \"Count\": 3\n}"}}], "relationships": []}, {"id": 3, "name": "Customer", "fields": [{"id": 22, "name": "Id", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": true, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "UUID", "settings": "{}"}}, {"id": 23, "name": "CompanyName", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "CompanyName", "settings": "{\n  \"locale\": \"en\"\n}"}}, {"id": 24, "name": "ContactName", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "FullName", "settings": "{\n  \"locale\": \"en\",\n  \"gender\": \"null\",\n  \"format\": \"{firstName} {lastName}\"\n}"}}, {"id": 25, "name": "ContactTitle", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "JobTitle", "settings": "{\n  \"locale\": \"en\"\n}"}}, {"id": 26, "name": "Address", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "Address", "settings": "{\n  \"country\": \"US\",\n  \"locale\": \"en\",\n  \"state\": \"null\",\n  \"city\": \"null\",\n  \"postalCode\": \"null\"\n}"}}, {"id": 27, "name": "City", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "City", "settings": "{\n  \"country\": \"US\",\n  \"locale\": \"en\",\n  \"state\": \"null\"\n}"}}, {"id": 28, "name": "Region", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "State", "settings": "{\n  \"country\": \"US\",\n  \"locale\": \"en\"\n}"}}, {"id": 29, "name": "PostalCode", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "PostalCode", "settings": "{\n  \"country\": \"US\",\n  \"state\": \"null\",\n  \"city\": \"null\"\n}"}}, {"id": 30, "name": "Country", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "County", "settings": "{\n  \"country\": \"Germany\",\n  \"locale\": \"en\"\n}"}}, {"id": 31, "name": "Phone", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "PATTERN", "name": "US Phone Number", "settings": "{}"}}, {"id": 32, "name": "Fax", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "PATTERN", "name": "US Phone Number", "settings": "{}"}}], "relationships": []}, {"id": 4, "name": "Shipper", "fields": [{"id": 33, "name": "Id", "type": "INTEGER", "nativeType": "INTEGER", "maxLength": null, "isPrimaryKey": true, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "AUTO_INCREMENT", "name": null, "settings": "{\n  \"Start\": 1,\n  \"Step\": 1\n}"}}, {"id": 34, "name": "CompanyName", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "CompanyName", "settings": "{\n  \"locale\": \"en\"\n}"}}, {"id": 35, "name": "Phone", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "PATTERN", "name": "US Phone Number", "settings": "{}"}}], "relationships": []}, {"id": 5, "name": "Supplier", "fields": [{"id": 36, "name": "Id", "type": "INTEGER", "nativeType": "INTEGER", "maxLength": null, "isPrimaryKey": true, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "AUTO_INCREMENT", "name": null, "settings": "{\n  \"Start\": 1,\n  \"Step\": 1\n}"}}, {"id": 37, "name": "CompanyName", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "CompanyName", "settings": "{\n  \"locale\": \"en\"\n}"}}, {"id": 38, "name": "ContactName", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "FullName", "settings": "{\n  \"locale\": \"en\",\n  \"gender\": \"null\",\n  \"format\": \"{firstName} {lastName}\"\n}"}}, {"id": 39, "name": "ContactTitle", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "JobTitle", "settings": "{\n  \"locale\": \"en\"\n}"}}, {"id": 40, "name": "Address", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "Address", "settings": "{\n  \"country\": \"US\",\n  \"state\": \"null\",\n  \"city\": \"null\",\n  \"locale\": \"en\"\n}"}}, {"id": 41, "name": "City", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "City", "settings": "{\n  \"country\": \"US\",\n  \"state\": \"null\",\n  \"locale\": \"en\"\n}"}}, {"id": 42, "name": "Region", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "State", "settings": "{\n  \"country\": \"US\",\n  \"locale\": \"en\"\n}"}}, {"id": 43, "name": "PostalCode", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "PostalCode", "settings": "{\n  \"country\": \"US\",\n  \"state\": \"null\",\n  \"city\": \"null\"\n}"}}, {"id": 44, "name": "Country", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "Choice", "settings": "{\n  \"Options\": [\n    {\n      \"Value\": \"UK\",\n      \"Probability\": 0.5\n    },\n    {\n      \"Value\": \"Canada\",\n      \"Probability\": 0.5\n    }\n  ]\n}"}}, {"id": 45, "name": "Phone", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "PATTERN", "name": "US Phone Number", "settings": "{}"}}, {"id": 46, "name": "Fax", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "PATTERN", "name": "US Phone Number", "settings": "{}"}}, {"id": 47, "name": "HomePage", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "AI", "name": null, "settings": "{\n  \"Prompt\": \"Generate a realistic homepage URL for a supplier.\",\n  \"MaxTokens\": 20\n}"}}], "relationships": []}, {"id": 6, "name": "Order", "fields": [{"id": 48, "name": "Id", "type": "INTEGER", "nativeType": "INTEGER", "maxLength": null, "isPrimaryKey": true, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "AUTO_INCREMENT", "name": null, "settings": "{\n  \"Start\": 1,\n  \"Step\": 1\n}"}}, {"id": 49, "name": "CustomerId", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "Choice", "settings": "{\n  \"Options\": [\n    {\n      \"Value\": \"VINET\",\n      \"Probability\": 0.5\n    },\n    {\n      \"Value\": \"RATTC\",\n      \"Probability\": 0.5\n    }\n  ]\n}"}}, {"id": 50, "name": "EmployeeId", "type": "INTEGER", "nativeType": "INTEGER", "maxLength": null, "isPrimaryKey": false, "isNullable": false, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "Random", "settings": "{\n  \"Min\": 1,\n  \"<PERSON>\": 100\n}"}}, {"id": 51, "name": "OrderDate", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "PATTERN", "name": "Date", "settings": "{}"}}, {"id": 52, "name": "RequiredDate", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "PATTERN", "name": "Date", "settings": "{}"}}, {"id": 53, "name": "ShippedDate", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "PATTERN", "name": "Date", "settings": "{}"}}, {"id": 54, "name": "ShipVia", "type": "INTEGER", "nativeType": "INTEGER", "maxLength": null, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "RANDOM", "name": null, "settings": "{\n  \"<PERSON>\": 1,\n  \"<PERSON>\": 5\n}"}}, {"id": 55, "name": "Freight", "type": "DECIMAL", "nativeType": "DECIMAL", "maxLength": null, "isPrimaryKey": false, "isNullable": false, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "Normal Distribution", "settings": "{\n  \"Mean\": 20.0,\n  \"StdDev\": 10.0,\n  \"<PERSON>\": 0.0,\n  \"<PERSON>\": 100.0\n}"}}, {"id": 56, "name": "ShipName", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "CompanyName", "settings": "{\n  \"locale\": \"en\"\n}"}}, {"id": 57, "name": "ShipAddress", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "Address", "settings": "{\n  \"country\": \"FR\",\n  \"locale\": \"en\"\n}"}}, {"id": 58, "name": "ShipCity", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "City", "settings": "{\n  \"country\": \"FR\",\n  \"locale\": \"en\"\n}"}}, {"id": 59, "name": "ShipRegion", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "State", "settings": "{\n  \"country\": \"FR\",\n  \"locale\": \"en\"\n}"}}, {"id": 60, "name": "ShipPostalCode", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "PostalCode", "settings": "{\n  \"country\": \"FR\",\n  \"state\": null,\n  \"city\": null\n}"}}, {"id": 61, "name": "ShipCountry", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "CHOICE", "name": null, "settings": "{\n  \"Options\": [\n    {\n      \"Value\": \"France\",\n      \"Probability\": 0.6\n    },\n    {\n      \"Value\": \"USA\",\n      \"Probability\": 0.4\n    }\n  ]\n}"}}], "relationships": []}, {"id": 7, "name": "Product", "fields": [{"id": 62, "name": "Id", "type": "INTEGER", "nativeType": "INTEGER", "maxLength": null, "isPrimaryKey": true, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "AUTO_INCREMENT", "name": null, "settings": "{\n  \"Start\": 1,\n  \"Step\": 1\n}"}}, {"id": 63, "name": "ProductName", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "AI", "name": null, "settings": "{\n  \"Prompt\": \"Generate a realistic product name for a grocery item.\",\n  \"MaxTokens\": 10\n}"}}, {"id": 64, "name": "SupplierId", "type": "INTEGER", "nativeType": "INTEGER", "maxLength": null, "isPrimaryKey": false, "isNullable": false, "isUnique": false, "fieldSetting": {"type": "REFERENCE", "name": null, "settings": "{\n  \"TargetEntity\": \"Supplier\",\n  \"TargetField\": \"Id\"\n}"}}, {"id": 65, "name": "CategoryId", "type": "INTEGER", "nativeType": "INTEGER", "maxLength": null, "isPrimaryKey": false, "isNullable": false, "isUnique": false, "fieldSetting": {"type": "REFERENCE", "name": null, "settings": "{\n  \"TargetEntity\": \"Category\",\n  \"TargetField\": \"Id\"\n}"}}, {"id": 66, "name": "QuantityPerUnit", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "LoremIpsum", "settings": "{\n  \"Type\": \"Sentences\",\n  \"Count\": 1\n}"}}, {"id": 67, "name": "UnitPrice", "type": "DECIMAL", "nativeType": "DECIMAL", "maxLength": null, "isPrimaryKey": false, "isNullable": false, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "Uniform Distribution", "settings": "{\n  \"Min\": 1.0,\n  \"Max\": 100.0,\n  \"DataType\": \"Decimal\"\n}"}}, {"id": 68, "name": "UnitsInStock", "type": "INTEGER", "nativeType": "INTEGER", "maxLength": null, "isPrimaryKey": false, "isNullable": false, "isUnique": false, "fieldSetting": {"type": "RANDOM", "name": null, "settings": "{\n  \"Min\": 0,\n  \"<PERSON>\": 100,\n  \"DataType\": \"Integer\"\n}"}}, {"id": 69, "name": "UnitsOnOrder", "type": "INTEGER", "nativeType": "INTEGER", "maxLength": null, "isPrimaryKey": false, "isNullable": false, "isUnique": false, "fieldSetting": {"type": "RANDOM", "name": null, "settings": "{\n  \"Min\": 0,\n  \"<PERSON>\": 50,\n  \"DataType\": \"Integer\"\n}"}}, {"id": 70, "name": "ReorderLevel", "type": "INTEGER", "nativeType": "INTEGER", "maxLength": null, "isPrimaryKey": false, "isNullable": false, "isUnique": false, "fieldSetting": {"type": "RANDOM", "name": null, "settings": "{\n  \"Min\": 1,\n  \"<PERSON>\": 100,\n  \"DataType\": \"Integer\"\n}"}}, {"id": 71, "name": "Discontinued", "type": "INTEGER", "nativeType": "INTEGER", "maxLength": null, "isPrimaryKey": false, "isNullable": false, "isUnique": false, "fieldSetting": {"type": "CHOICE", "name": null, "settings": "{\n  \"Options\": [\n    {\n      \"Value\": 0,\n      \"Probability\": 0.8,\n      \"Label\": \"Not Discontinued\"\n    },\n    {\n      \"Value\": 1,\n      \"Probability\": 0.2,\n      \"Label\": \"Discontinued\"\n    }\n  ]\n}"}}], "relationships": []}, {"id": 8, "name": "OrderDetail", "fields": [{"id": 72, "name": "Id", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": true, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "UUID", "settings": "{}"}}, {"id": 73, "name": "OrderId", "type": "INTEGER", "nativeType": "INTEGER", "maxLength": null, "isPrimaryKey": false, "isNullable": false, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "Random", "settings": "{\n  \"Min\": 1,\n  \"<PERSON>\": 99999\n}"}}, {"id": 74, "name": "ProductId", "type": "INTEGER", "nativeType": "INTEGER", "maxLength": null, "isPrimaryKey": false, "isNullable": false, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "Random", "settings": "{\n  \"Min\": 1,\n  \"<PERSON>\": 100\n}"}}, {"id": 75, "name": "UnitPrice", "type": "DECIMAL", "nativeType": "DECIMAL", "maxLength": null, "isPrimaryKey": false, "isNullable": false, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "Uniform Distribution", "settings": "{\n  \"Min\": 5.0,\n  \"Max\": 100.0\n}"}}, {"id": 76, "name": "Quantity", "type": "INTEGER", "nativeType": "INTEGER", "maxLength": null, "isPrimaryKey": false, "isNullable": false, "isUnique": false, "fieldSetting": {"type": "RANDOM", "name": null, "settings": "{\n  \"<PERSON>\": 1,\n  \"<PERSON>\": 20\n}"}}, {"id": 77, "name": "Discount", "type": "DECIMAL", "nativeType": "DOUBLE", "maxLength": null, "isPrimaryKey": false, "isNullable": false, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "Uniform Distribution", "settings": "{\n  \"<PERSON>\": 0.0,\n  \"<PERSON>\": 0.5\n}"}}], "relationships": []}, {"id": 9, "name": "CustomerCustomerDemo", "fields": [{"id": 78, "name": "Id", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": true, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "PATTERN", "name": "UUID", "settings": "{}"}}, {"id": 79, "name": "CustomerTypeId", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "CHOICE", "name": null, "settings": "{\n  \"Options\": [\n    {\n      \"Value\": \"Regular\",\n      \"Probability\": 0.6,\n      \"Label\": \"Regular Customer\"\n    },\n    {\n      \"Value\": \"Premium\",\n      \"Probability\": 0.3,\n      \"Label\": \"Premium Customer\"\n    },\n    {\n      \"Value\": \"VIP\",\n      \"Probability\": 0.1,\n      \"Label\": \"VIP Customer\"\n    }\n  ]\n}"}}], "relationships": []}, {"id": 10, "name": "CustomerDemographic", "fields": [{"id": 80, "name": "Id", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": true, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "UUID", "settings": "{}"}}, {"id": 81, "name": "CustomerDesc", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "LoremIpsum", "settings": "{\n  \"Type\": \"Sentences\",\n  \"Count\": 3\n}"}}], "relationships": []}, {"id": 11, "name": "Region", "fields": [{"id": 82, "name": "Id", "type": "INTEGER", "nativeType": "INTEGER", "maxLength": null, "isPrimaryKey": true, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "AUTO_INCREMENT", "name": null, "settings": "{\n  \"Start\": 1,\n  \"Step\": 1\n}"}}, {"id": 83, "name": "RegionDescription", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "Choice", "settings": "{\n  \"Options\": [\n    {\n      \"Value\": \"Eastern\",\n      \"Probability\": 0.5\n    },\n    {\n      \"Value\": \"Southern\",\n      \"Probability\": 0.5\n    }\n  ]\n}"}}], "relationships": []}, {"id": 12, "name": "Territory", "fields": [{"id": 84, "name": "Id", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": true, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "UUID", "settings": "{}"}}, {"id": 85, "name": "TerritoryDescription", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "City", "settings": "{\n  \"country\": \"US\",\n  \"locale\": \"en\",\n  \"state\": null\n}"}}, {"id": 86, "name": "RegionId", "type": "INTEGER", "nativeType": "INTEGER", "maxLength": null, "isPrimaryKey": false, "isNullable": false, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "AutoIncrement", "settings": "{\n  \"Start\": 1,\n  \"Step\": 1\n}"}}], "relationships": []}, {"id": 13, "name": "EmployeeTerritory", "fields": [{"id": 87, "name": "Id", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": true, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "PATTERN", "name": "UUID", "settings": "{}"}}, {"id": 88, "name": "EmployeeId", "type": "INTEGER", "nativeType": "INTEGER", "maxLength": null, "isPrimaryKey": false, "isNullable": false, "isUnique": false, "fieldSetting": {"type": "AUTO_INCREMENT", "name": null, "settings": "{\n  \"Start\": 1,\n  \"Step\": 1\n}"}}, {"id": 89, "name": "TerritoryId", "type": "STRING", "nativeType": "VARCHAR", "maxLength": 8000, "isPrimaryKey": false, "isNullable": true, "isUnique": false, "fieldSetting": {"type": "FUNCTION", "name": "PostalCode", "settings": "{\n  \"country\": \"US\",\n  \"state\": null,\n  \"city\": null\n}"}}], "relationships": []}]}]}]}}