using Aubrant.TestDataGenerator.Core.Enums;
using System.Text.Json.Serialization;

namespace Aubrant.TestDataGenerator.Core.Patterns
{
    public class LettersToken : Token
    {
        public int Length { get; }

        [JsonConverter(typeof(JsonStringEnumConverter))]
        public CaseOption Case { get; }

        [JsonConstructor]
        public LettersToken(string name, int length, CaseOption @case = CaseOption.Random) : this(name, string.Empty, length, @case)
        {
        }

        public LettersToken(string name, string description, int length, CaseOption caseOption = CaseOption.Random) : base(name, description)
        {
            if (length <= 0) throw new ArgumentException("Length must be positive.");
            Length = length;
            Case = caseOption;
        }

        public override DataValue Generate(DataGeneratorContext context)
        {
            var letters = new char[Length];

            bool useUpper = Case switch
            {
                CaseOption.Upper => true,
                CaseOption.Lower => false,
                CaseOption.Random => context.Random.Next(2) == 0,
                _ => context.Random.Next(2) == 0
            };

            for (int i = 0; i < Length; i++)
            {
                letters[i] = (char)(useUpper ? ('A' + context.Random.Next(0, 26)) : ('a' + context.Random.Next(0, 26)));
            }

            return new DataValue(DataType.String, new string(letters));
        }
    }
}