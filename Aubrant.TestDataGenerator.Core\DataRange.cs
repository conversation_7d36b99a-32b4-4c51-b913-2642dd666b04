﻿using System.ComponentModel;

namespace Aubrant.TestDataGenerator.Core
{
    public class DataRange
    {
        /// <summary>
        /// Min value in Range
        /// </summary>
        [Description("Min value in Range")]
        public DataValue Min { get; set; }

        /// <summary>
        /// Max value in Range
        /// </summary>
        [Description("Max value in Range")]
        public DataValue Max { get; set; }

        public DataRange()
        {
            Min = new DataValue(0); // Initialize with default DataValue
            Max = new DataValue(0); // Initialize with default DataValue
        }

        public DataRange(DataValue min, DataValue max)
        {
            Min = min;
            Max = max;
        }
    }
}