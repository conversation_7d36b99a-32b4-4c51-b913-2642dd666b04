using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.Data.Enums;
using HotChocolate.Types;

namespace Aubrant.TestDataGenerator.UI.Server.Types
{
    public class FieldSettingType : ObjectType<FieldSetting>
    {
        protected override void Configure(IObjectTypeDescriptor<FieldSetting> descriptor)
        {
            descriptor.Field(fs => fs.FieldId).Type<IntType>();
            descriptor.Field(fs => fs.Type).Type<EnumType<GeneratorType>>();
            descriptor.Field(fs => fs.Name).Type<StringType>();
            descriptor.Field(fs => fs.Settings).Type<StringType>();
            descriptor.Field(fs => fs.Field).Type<FieldType>();
        }
    }
}
