using Aubrant.TestDataGenerator.Core.Patterns;

namespace Aubrant.TestDataGenerator.Generators.Patterns
{
    public class CreditCardPattern : BasePattern
    {
        public CreditCardPattern() : base(
            name: "Credit Card (Visa, Mastercard)",
            description: "Generates a 16-digit number that mimics a Visa or Mastercard number.",
            pattern: "{P2}{D2}-{D4}-{D4}-{D4}",
            tokens: new List<Token>
            {
                new DigitsToken("P2", "Prefix", 2, (40, 49), (51, 55)),
                new DigitsToken("D2", "Two digits", 2),
                new DigitsToken("D4", "Four digits", 4)
            })
        { }
    }
}
