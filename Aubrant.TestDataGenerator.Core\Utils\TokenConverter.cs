using System.Text.Json;
using System.Text.Json.Serialization;
using Aubrant.TestDataGenerator.Core.Patterns;
using System.Reflection;

namespace Aubrant.TestDataGenerator.Core.Utils
{
    public class TokenConverter : JsonConverter<Token>
    {
        private static readonly Dictionary<string, Type> _tokenTypes;

        static TokenConverter()
        {
            var tokenType = typeof(Token);
            _tokenTypes = Assembly.GetExecutingAssembly().GetTypes()
                .Where(t => t.IsClass && !t.IsAbstract && tokenType.IsAssignableFrom(t))
                .ToDictionary(t => t.Name.Replace("Token", ""), t => t, StringComparer.OrdinalIgnoreCase);
        }

        public override Token Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            using (var jsonDoc = JsonDocument.ParseValue(ref reader))
            {
                var jsonObject = jsonDoc.RootElement;
                if (jsonObject.TryGetProperty("Type", out var typeProperty))
                {
                    var typeName = typeProperty.GetString();
                    if (typeName != null && _tokenTypes.TryGetValue(typeName, out var type))
                    {
                        return (Token)JsonSerializer.Deserialize(jsonObject.GetRawText(), type, options);
                    }
                }
                throw new JsonException("Could not determine the token type.");
            }
        }

        public override void Write(Utf8JsonWriter writer, Token value, JsonSerializerOptions options)
        {
            JsonSerializer.Serialize(writer, (object)value, options);
        }
    }
}