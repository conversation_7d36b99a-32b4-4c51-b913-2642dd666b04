using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Functions;
using Aubrant.TestDataGenerator.Core.Utils;
using Aubrant.TestDataGenerator.Core.Utils.Models;
using Bogus;

namespace Aubrant.TestDataGenerator.Generators.Functions{
    /// <summary>
    /// A generator function that produces realistic city names based on the specified country, optional state, and output locale.
    /// </summary>
    public class CityFunction : BaseFunction
    {
        public override string Name => "City";
        public override string Description => "Generates a realistic city name based on the specified country, optional state, and output locale.";
        public override DataType ReturnType => DataType.String;

        /// <summary>
        /// Initializes a new instance of the CityFunction class.
        /// Defines 'country', 'locale', and optional 'state' parameters.
        /// </summary>
        public CityFunction() : base("Location")
        {
            Parameters.Add(new Parameter("country", "The country to generate the city in.", DataType.String, new DataValue("US"))); // Default country is US
            Parameters.Add(new Parameter("locale", "The output locale.", DataType.String, new DataValue("en")));  // Default output locale is English
            Parameters.Add(new Parameter("state", "The state to generate the city in.", DataType.String, null)); // Optional state parameter
        }

        /// <summary>
        /// Generates a random city name using the GeoDataRepository or Bogus library, considering the specified country, optional state, and output locale.
        /// </summary>
        /// <returns>A DataValue containing the generated city name as a string.</returns>
        /// <exception cref="InvalidOperationException">Thrown if any parameter is missing or invalid.</exception>
        public override DataValue Generate(DataGeneratorContext context)
        {
            string countryInput = Parameters["country"].Value?.ToString() ??
                                  throw new InvalidOperationException("Country parameter is missing or invalid.");
            string outputLocale = Parameters["locale"].Value?.ToString() ??
                                  throw new InvalidOperationException("Locale parameter is missing or invalid.");
            string? stateInput = Parameters["state"].Value?.ToString();

            try
            {
                City? city = null;

                // Try to get a realistic city from our curated data
                if (!string.IsNullOrWhiteSpace(stateInput))
                {
                    city = GeoDataRepository.GetRandomCity(countryInput, context.Random, stateInput);
                }
                else
                {
                    city = GeoDataRepository.GetRandomCity(countryInput, context.Random);
                }

                if (city != null)
                {
                    return new DataValue(DataType.String, city.Name);
                }

                // Fallback to Bogus.NET if no specific city found in curated data for the country/state
                var faker = new Faker(outputLocale);
                string bogusCity = faker.Address.City();
                return new DataValue(DataType.String, bogusCity);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error generating city for country '{countryInput}' with state '{stateInput}' and locale '{outputLocale}': {ex.Message}", ex);
            }
        }
    }
}
