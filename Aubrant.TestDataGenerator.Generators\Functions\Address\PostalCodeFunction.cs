using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Functions;
using Aubrant.TestDataGenerator.Core.Utils;
using Aubrant.TestDataGenerator.Core.Utils.Models;
using Bogus;

namespace Aubrant.TestDataGenerator.Generators.Functions
{
    /// <summary>
    /// A generator function that produces realistic postal codes based on the specified country, optional state/city, and output locale.
    /// </summary>
    public class PostalCodeFunction : BaseFunction
    {
        public override string Name => "PostalCode";
        public override string Description => "Generates a realistic postal code based on the specified country, optional state/city, and output locale.";
        public override DataType ReturnType => DataType.String;

        /// <summary>
        /// Initializes a new instance of the PostalCodeFunction class.
        /// Defines 'country', 'locale', and optional 'state', 'city' parameters.
        /// </summary>
        public PostalCodeFunction() : base("Location")
        {
            Parameters.Add(new Parameter("country", "The country to generate the postal code in.", DataType.String, new DataValue("US"))); // Default country is US
            Parameters.Add(new Parameter("state", "The state to generate the postal code in.", DataType.String, null)); // Optional state parameter
            Parameters.Add(new Parameter("city", "The city to generate the postal code in.", DataType.String, null)); // Optional city parameter
        }

        /// <summary>
        /// Generates a random postal code using the Bogus library, considering the specified country, optional state/city, and output locale.
        /// </summary>
        /// <returns>A DataValue containing the generated postal code as a string.</returns>
        /// <exception cref="InvalidOperationException">Thrown if any parameter is missing or invalid.</exception>
        public override DataValue Generate(DataGeneratorContext context)
        {
            string countryInput = Parameters["country"].Value?.ToString() ?? throw new InvalidOperationException("Country parameter is missing or invalid.");
            string? stateInput = Parameters["state"].Value?.ToString();
            string? cityInput = Parameters["city"].Value?.ToString();

            var faker = new Faker();

            try
            {
                string postalCode;
                State? state = null;
                City? city = null;
                List<string>? patternsToUse = null;

                if (!string.IsNullOrWhiteSpace(stateInput))
                {
                    state = GeoDataRepository.GetState(countryInput, stateInput);
                }
                else
                {
                    state = GeoDataRepository.GetRandomState(countryInput, context.Random);
                }

                if (!string.IsNullOrWhiteSpace(cityInput) && state != null)
                {
                    city = GeoDataRepository.GetCity(countryInput, stateInput, cityInput);
                }

                if (city != null && city.PostalCodePatterns != null && city.PostalCodePatterns.Any())
                {
                    patternsToUse = city.PostalCodePatterns;
                }
                else if (state != null && state.PostalCodePatterns != null && state.PostalCodePatterns.Any())
                {
                    patternsToUse = state.PostalCodePatterns;
                }

                if (patternsToUse != null && patternsToUse.Any())
                {
                    // Select a random pattern and generate the postal code
                    string pattern = patternsToUse[context.Random.Next(patternsToUse.Count)];
                    postalCode = new string(pattern.Select(c => c == '#' ? (char)('0' + faker.Random.Number(0, 9)) : c).ToArray());
                }
                else
                {
                    // Fallback to Bogus.NET if no specific patterns found
                    postalCode = faker.Address.ZipCode();
                }

                return new DataValue(DataType.String, postalCode);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error generating postal code for country '{countryInput}' with state '{stateInput}' and city '{cityInput}': {ex.Message}", ex);
            }
        }
    }
}
