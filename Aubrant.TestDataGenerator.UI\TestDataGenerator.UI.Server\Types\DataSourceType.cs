using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.Data.Enums;

namespace Aubrant.TestDataGenerator.UI.Server.Types
{
    public class DataSourceType : ObjectType<DataSource>
    {
        protected override void Configure(IObjectTypeDescriptor<DataSource> descriptor)
        {
            descriptor.Field(d => d.Id).Type<IdType>();
            descriptor.Field(d => d.ProjectId).Type<IntType>();
            descriptor.Field(d => d.Name).Type<StringType>();
            descriptor.Field(d => d.Description).Type<StringType>();
            descriptor.Field(d => d.Type).Type<EnumType<DataSourceType>>();
            descriptor.Field(d => d.Provider).Type<EnumType<DataSourceProvider>>();
            descriptor.Field(d => d.ConnectionString).Type<StringType>();
            descriptor.Field(d => d.Project).Type<ProjectType>();
            descriptor.Field(d => d.Entities).Type<ListType<EntityType>>();
        }
    }
}
