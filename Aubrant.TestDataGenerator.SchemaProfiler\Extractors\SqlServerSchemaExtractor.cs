using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.Data.Enums;
using Aubrant.TestDataGenerator.SchemaProfiler.Common;
using Aubrant.TestDataGenerator.SchemaProfiler.Logging;
using System.Data;
using System.Data.SqlClient;

namespace Aubrant.TestDataGenerator.SchemaProfiler.Extractors
{
    /// <summary>
    /// Extracts schema and loads sample data from SQL Server databases.
    /// </summary>
    public class SqlServerSchemaExtractor : IDataSourceSchemaExtractor
    {
        private readonly ILogger _logger;
        private readonly int _sampleSize;

        public SqlServerSchemaExtractor(SchemaExtractionOptions? options = null, ILogger? logger = null)
        {
            options = options ?? new SchemaExtractionOptions();
            _sampleSize = options.SampleSize;
            _logger = logger ?? new NullLogger();
            _logger.LogInfo($"SqlServerSchemaExtractor initialized with SampleSize: {_sampleSize}");
        }

        public async Task<DataSource> ExtractAsync(string connectionString, string? dataSourceName = null)
        {
            _logger.LogInfo($"Extracting schema from SQL Server: {connectionString}");
            var effectiveDataSourceName = string.IsNullOrWhiteSpace(dataSourceName) ? new SqlConnectionStringBuilder(connectionString).InitialCatalog : dataSourceName;
            var dataSource = new DataSource { Name = effectiveDataSourceName, Type = DataSourceType.RelationalDatabase, Provider = DataSourceProvider.SQLServer, ConnectionString = connectionString };
            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    _logger.LogInfo($"Connected to SQL Server database: {connection.Database}");
                    dataSource.Entities = (await ExtractSchemaMetadataAsync(connection)).Values.ToList();
                }
                _logger.LogInfo($"Schema extracted for {dataSource.Name}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error extracting schema from SQL Server database {connectionString}", ex);
                throw;
            }
            return dataSource;
        }

        private async Task<Dictionary<string, Entity>> ExtractSchemaMetadataAsync(SqlConnection connection)
        {
            var entities = new Dictionary<string, Entity>();
            var foreignKeyRelationships = new List<(string FkConstraintName, string FkTableSchema, string FkTableName, string FkColumnName, string PkTableSchema, string PkTableName, string PkColumnName)>();
            var uniqueIndexes = new Dictionary<string, Dictionary<string, List<string>>>(); // TableKey -> IndexName -> List<ColumnName>

            using (var command = new SqlCommand(GetSchemaQuery(), connection))
            using (var reader = await command.ExecuteReaderAsync())
            {
                while (await reader.ReadAsync())
                {
                    string schema = reader["TABLE_SCHEMA"].ToString()!;
                    string tableName = reader["TABLE_NAME"].ToString()!;
                    string entityKey = $"{schema}.{tableName}";

                    if (!entities.TryGetValue(entityKey, out var entity))
                    {
                        entity = new Entity { Name = tableName, Schema = schema };
                        entities[entityKey] = entity;
                    }

                    var field = new Field
                    {
                        Name = reader["COLUMN_NAME"].ToString(),
                        Type = MapSqlTypeToDataType(reader["DATA_TYPE"].ToString()!),
                        NativeType = reader["DATA_TYPE"].ToString(),
                        IsNullable = reader["IS_NULLABLE"].ToString() == "YES",
                        IsPrimaryKey = Convert.ToInt32(reader["IsPrimaryKey"]) == 1,
                        IsIdentity = Convert.ToInt32(reader["IsIdentity"]) == 1,
                        IsUnique = reader["UniqueIndexName"] != DBNull.Value, // This will be re-evaluated later
                        MaxLength = reader["CHARACTER_MAXIMUM_LENGTH"] as int?
                    };
                    entity.Fields.Add(field);

                    if (field.IsPrimaryKey)
                    {
                        var pk = entity.KeyConstraints.FirstOrDefault(k => k.KeyType == KeyType.Primary);
                        if (pk == null)
                        {
                            pk = new KeyConstraint { KeyType = KeyType.Primary, Columns = new List<string>() };
                            entity.KeyConstraints.Add(pk);
                        }
                        pk.Columns.Add(field.Name!);
                    }

                    // Collect unique index information
                    if (reader["UniqueIndexName"] != DBNull.Value)
                    {
                        string indexName = reader["UniqueIndexName"].ToString()!;
                        string uniqueColumnName = reader["UniqueIndexColumnName"].ToString()!;
                        if (!uniqueIndexes.ContainsKey(entityKey)) uniqueIndexes[entityKey] = new Dictionary<string, List<string>>();
                        if (!uniqueIndexes[entityKey].ContainsKey(indexName)) uniqueIndexes[entityKey][indexName] = new List<string>();
                        uniqueIndexes[entityKey][indexName].Add(uniqueColumnName);
                    }

                    if (reader["FK_CONSTRAINT_NAME"] != DBNull.Value)
                    {
                        foreignKeyRelationships.Add((
                            reader["FK_CONSTRAINT_NAME"].ToString()!,
                            reader["FK_TABLE_SCHEMA"].ToString()!,
                            reader["FK_TABLE_NAME"].ToString()!,
                            reader["FK_COLUMN_NAME"].ToString()!,
                            reader["ReferencedTableSchema"].ToString()!,
                            reader["ReferencedTableName"].ToString()!,
                            reader["ReferencedColumnName"].ToString()!
                        ));
                    }
                }
            }

            // Re-evaluate IsUnique for fields based on collected unique indexes
            foreach (var entity in entities.Values)
            {
                string entityKey = $"{entity.Schema}.{entity.Name}";
                if (uniqueIndexes.TryGetValue(entityKey, out var tableUniqueIndexes))
                {
                    foreach (var field in entity.Fields)
                    {
                        field.IsUnique = tableUniqueIndexes.Any(idx => idx.Value.Contains(field.Name!) && idx.Value.Count == 1); // Simple unique column
                    }
                }
            }

            var groupedFks = foreignKeyRelationships
                .GroupBy(fk => new { fk.FkConstraintName, fk.FkTableSchema, fk.FkTableName, fk.PkTableSchema, fk.PkTableName })
                .ToList();

            foreach (var fkGroup in groupedFks)
            {
                string sourceEntityKey = $"{fkGroup.Key.FkTableSchema}.{fkGroup.Key.FkTableName}";
                string referencedEntityKey = $"{fkGroup.Key.PkTableSchema}.{fkGroup.Key.PkTableName}";

                if (entities.TryGetValue(sourceEntityKey, out var sourceEntity) && entities.TryGetValue(referencedEntityKey, out var referencedEntity))
                {
                    var fkColumns = fkGroup.Select(f => f.FkColumnName).ToList();
                    var pkColumns = fkGroup.Select(f => f.PkColumnName).ToList();

                    sourceEntity.KeyConstraints.Add(new KeyConstraint
                    {
                        KeyType = KeyType.Foreign,
                        Columns = fkColumns,
                        ReferencedEntity = referencedEntityKey,
                        ReferencedColumns = pkColumns
                    });

                    // Determine if it's a One-to-One relationship
                    bool isOneToOne = false;
                    // Check if the FK columns in the source table form a unique key (primary key or unique index)
                    var sourcePkColumns = sourceEntity.KeyConstraints.FirstOrDefault(k => k.KeyType == KeyType.Primary)?.Columns;
                    if (sourcePkColumns != null && sourcePkColumns.SequenceEqual(fkColumns))
                    {
                        isOneToOne = true; // FK is also the PK in the source table
                    }
                    else if (uniqueIndexes.TryGetValue(sourceEntityKey, out var tableUniqueIndexes))
                    {
                        isOneToOne = tableUniqueIndexes.Any(idx => new HashSet<string>(idx.Value).SetEquals(fkColumns));
                    }

                    sourceEntity.Relationships.Add(new Relationship
                    {
                        SourceEntityId = sourceEntity.Id,
                        TargetEntity = referencedEntityKey,
                        SourceField = string.Join(", ", fkColumns),
                        TargetField = string.Join(", ", pkColumns),
                        Cardinality = isOneToOne ? Cardinality.OneToOne : Cardinality.ManyToOne
                    });

                    referencedEntity.Relationships.Add(new Relationship
                    {
                        SourceEntityId = referencedEntity.Id,
                        TargetEntity = sourceEntityKey,
                        SourceField = string.Join(", ", pkColumns),
                        TargetField = string.Join(", ", fkColumns),
                        Cardinality = isOneToOne ? Cardinality.OneToOne : Cardinality.OneToMany
                    });
                }
            }

            return entities;
        }

        public async Task LoadSampleDataAsync(DataSource dataSource, string connectionString, int? sampleSize = null)
        {
            sampleSize = sampleSize ?? _sampleSize;
            _logger.LogInfo($"Loading sample data for SQL Server: {dataSource.Name} (sample size: {sampleSize})");
            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    foreach (var entity in dataSource.Entities)
                    {
                        _logger.LogInfo($"Loading sample data for entity: {entity.Name}");
                        await LoadSampleDataForEntityAsync(connection, entity, sampleSize.Value);
                    }
                }
                _logger.LogInfo($"Sample data loaded for {dataSource.Name}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error loading sample data from SQL Server database {connectionString}", ex);
                throw;
            }
        }

        private async Task LoadSampleDataForEntityAsync(SqlConnection connection, Entity entity, int sampleSize)
        {
            var dataTable = new DataTable(entity.Name);
            foreach (var field in entity.Fields)
            {
                dataTable.Columns.Add(new DataColumn(field.Name!, DataTypeMapper.ToSystemType(field.Type)));
            }

            var pk = entity.KeyConstraints.FirstOrDefault(k => k.KeyType == KeyType.Primary);
            string orderByColumn = pk != null && pk.Columns.Any() ? $"[{pk.Columns.First()}]" : "rowid";
            int halfSampleSize = sampleSize > 0 ? Math.Max(1, sampleSize / 2) : 0;

            string query = $"SELECT TOP {sampleSize} * FROM [{entity.Schema}].[{entity.Name}];";
            if (pk != null && pk.Columns.Any())
            {
                orderByColumn = pk.Columns.First();
                query = $"WITH SampleKeys AS ((SELECT TOP {halfSampleSize} [{orderByColumn}] FROM [{entity.Schema}].[{entity.Name}] ORDER BY [{orderByColumn}] ASC) UNION (SELECT TOP {halfSampleSize} [{orderByColumn}] FROM [{entity.Schema}].[{entity.Name}] ORDER BY [{orderByColumn}] DESC)) SELECT T.* FROM [{entity.Schema}].[{entity.Name}] AS T INNER JOIN SampleKeys SK ON T.[{orderByColumn}] = SK.[{orderByColumn}];";
            }

            using (var command = new SqlCommand(query, connection))
            using (var reader = await command.ExecuteReaderAsync())
            {
                dataTable.Load(reader);
            }
            entity.Data = dataTable;
        }

        private string GetSchemaQuery()
        {
            return """
            WITH ColumnInfo AS (
                SELECT
                    t.TABLE_SCHEMA, t.TABLE_NAME, c.COLUMN_NAME, c.DATA_TYPE,
                    c.IS_NULLABLE, c.CHARACTER_MAXIMUM_LENGTH, c.ORDINAL_POSITION
                FROM INFORMATION_SCHEMA.TABLES t
                JOIN INFORMATION_SCHEMA.COLUMNS c ON t.TABLE_SCHEMA = c.TABLE_SCHEMA AND t.TABLE_NAME = c.TABLE_NAME
                WHERE t.TABLE_TYPE = 'BASE TABLE'
            ),
            PrimaryKeyInfo AS (
                SELECT kcu.TABLE_SCHEMA, kcu.TABLE_NAME, kcu.COLUMN_NAME
                FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
                JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu ON tc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
                WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY'
            ),
            ForeignKeyInfo AS (
                SELECT 
                    fk_kcu.CONSTRAINT_NAME, fk_kcu.TABLE_SCHEMA AS FK_TABLE_SCHEMA, fk_kcu.TABLE_NAME AS FK_TABLE_NAME, 
                    fk_kcu.COLUMN_NAME AS FK_COLUMN_NAME, pk_kcu.TABLE_SCHEMA AS PK_TABLE_SCHEMA, pk_kcu.TABLE_NAME AS PK_TABLE_NAME,
                    pk_kcu.COLUMN_NAME AS PK_COLUMN_NAME, rc.UNIQUE_CONSTRAINT_NAME
                FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc
                JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE fk_kcu ON rc.CONSTRAINT_NAME = fk_kcu.CONSTRAINT_NAME
                JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE pk_kcu ON rc.UNIQUE_CONSTRAINT_NAME = pk_kcu.CONSTRAINT_NAME AND fk_kcu.ORDINAL_POSITION = pk_kcu.ORDINAL_POSITION
            ),
            UniqueIndexInfo AS (
                SELECT 
                    s.name AS TABLE_SCHEMA,
                    t.name AS TABLE_NAME,
                    c.name AS COLUMN_NAME,
                    ind.name AS INDEX_NAME
                FROM sys.indexes ind
                INNER JOIN sys.index_columns ic ON ind.object_id = ic.object_id AND ind.index_id = ic.index_id
                INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
                INNER JOIN sys.tables t ON ind.object_id = t.object_id
                INNER JOIN sys.schemas s ON t.schema_id = s.schema_id
                WHERE ind.is_unique = 1 AND ind.is_primary_key = 0
            ),
            IdentityColumnInfo AS (
                SELECT s.name AS TABLE_SCHEMA, t.name AS TABLE_NAME, c.name AS COLUMN_NAME
                FROM sys.tables t
                JOIN sys.schemas s ON t.schema_id = s.schema_id
                JOIN sys.columns c ON t.object_id = c.object_id
                WHERE c.is_identity = 1
            )
            SELECT
                            ci.TABLE_SCHEMA, ci.TABLE_NAME, ci.COLUMN_NAME, ci.DATA_TYPE, ci.IS_NULLABLE, ci.CHARACTER_MAXIMUM_LENGTH,
                CASE WHEN pki.COLUMN_NAME IS NOT NULL THEN 1 ELSE 0 END AS IsPrimaryKey,
                CASE WHEN ici.COLUMN_NAME IS NOT NULL THEN 1 ELSE 0 END AS IsIdentity,
                fki.CONSTRAINT_NAME AS FK_CONSTRAINT_NAME, fki.FK_TABLE_SCHEMA AS FK_TABLE_SCHEMA, fki.FK_TABLE_NAME AS FK_TABLE_NAME, fki.FK_COLUMN_NAME AS FK_COLUMN_NAME, fki.PK_TABLE_SCHEMA AS ReferencedTableSchema,
                fki.PK_TABLE_NAME AS ReferencedTableName, fki.PK_COLUMN_NAME AS ReferencedColumnName,
                uii.INDEX_NAME AS UniqueIndexName, uii.COLUMN_NAME AS UniqueIndexColumnName
            FROM ColumnInfo ci
            LEFT JOIN PrimaryKeyInfo pki ON ci.TABLE_SCHEMA = pki.TABLE_SCHEMA AND ci.TABLE_NAME = pki.TABLE_NAME AND ci.COLUMN_NAME = pki.COLUMN_NAME
            LEFT JOIN ForeignKeyInfo fki ON ci.TABLE_SCHEMA = fki.FK_TABLE_SCHEMA AND ci.TABLE_NAME = fki.FK_TABLE_NAME AND ci.COLUMN_NAME = fki.FK_COLUMN_NAME
            LEFT JOIN IdentityColumnInfo ici ON ci.TABLE_SCHEMA = ici.TABLE_SCHEMA AND ci.TABLE_NAME = ici.TABLE_NAME AND ci.COLUMN_NAME = ici.COLUMN_NAME
            LEFT JOIN UniqueIndexInfo uii ON ci.TABLE_SCHEMA = uii.TABLE_SCHEMA AND ci.TABLE_NAME = uii.TABLE_NAME AND ci.COLUMN_NAME = uii.COLUMN_NAME
            ORDER BY ci.TABLE_SCHEMA, ci.TABLE_NAME, ci.ORDINAL_POSITION;
            """;
        }

        private DataType MapSqlTypeToDataType(string sqlType)
        {
            return sqlType.ToLower() switch
            {
                "int" or "tinyint" or "smallint" or "bigint" => DataType.Integer,
                "decimal" or "numeric" or "money" or "smallmoney" or "float" or "real" => DataType.Decimal,
                "varchar" or "nvarchar" or "char" or "nchar" or "text" or "ntext" or "xml" or "uniqueidentifier" => DataType.String,
                "datetime" or "smalldatetime" or "date" or "time" or "datetime2" or "datetimeoffset" => DataType.DateTime,
                "bit" => DataType.Boolean,
                "varbinary" or "binary" or "image" or "timestamp" => DataType.Binary,
                _ => DataType.Unsupported
            };
        }
    }
}