import type React from "react"
import type { Entity } from "../types/database";
import { getCardinalityInfo } from "../../utils/relationship-utils"

interface SimpleConnectionsProps {
  entities: Entity[]
}

export const SimpleConnections = ({ entities }: SimpleConnectionsProps) => {
  const connections: React.ReactElement[] = []

  entities.forEach((sourceEntity) => {
    if (!sourceEntity.position) return

    sourceEntity.Relationships.forEach((relationship, index) => {
      // Find target entity
      const targetEntity = entities.find(
        (e) => e.Name === relationship.TargetEntity || e.Name === relationship.TargetEntity.split(".").pop(),
      )

      if (!targetEntity?.position) return

      // Find field positions
      const sourceFieldIndex = sourceEntity.Fields.findIndex((f) => f.Name === relationship.SourceField)
      const targetFieldIndex = targetEntity.Fields.findIndex((f) => f.Name === relationship.TargetField)

      if (sourceFieldIndex === -1 || targetFieldIndex === -1) return

      // Calculate exact positions
      const sourceY = sourceEntity.position.y + 70 + sourceFieldIndex * 28 + 14
      const targetY = targetEntity.position.y + 70 + targetFieldIndex * 28 + 14

      // Always connect from right edge to left edge
      const sourceX = sourceEntity.position.x + 320
      const targetX = targetEntity.position.x

      const cardinalityInfo = getCardinalityInfo(relationship.Cardinality)
      const key = `${sourceEntity.Name}-${relationship.SourceField}-${index}`

      connections.push(
        <g key={key}>
          {/* Connection line */}
          <line
            x1={sourceX}
            y1={sourceY}
            x2={targetX}
            y2={targetY}
            stroke="#475569" /* slate-700 */
            strokeWidth="2"
            className="dark:stroke-[#64748b] /* slate-500 */"
          />

          {/* Source cardinality */}
          <text
            x={sourceX - 20}
            y={sourceY - 5}
            fontSize="16"
            fontWeight="bold"
            fill="#000000" /* black */
            textAnchor="middle"
            className="dark:fill-[#ffffff] /* white */"
          >
            {cardinalityInfo.source.symbol}
          </text>

          {/* Target cardinality */}
          <text
            x={targetX + 20}
            y={targetY - 5}
            fontSize="16"
            fontWeight="bold"
            fill="#000000" /* black */
            textAnchor="middle"
            className="dark:fill-[#ffffff] /* white */"
          >
            {cardinalityInfo.target.symbol}
          </text>
        </g>,
      )
    })
  })

  return <>{connections}</>
}
