import type { Project, DataSource } from "src/types/database";

export const dataSource1: DataSource = {
    Id: 1,
    "Name": "ChinookDemoDb",
    "Type": "RelationalDatabase",
    "Provider": "SQLite",
    "ConnectionString": "Data Source=C:\\Users\\<USER>\\AppData\\Roaming\\LINQPad\\ChinookDemoDb.sqlite",
    "Entities": [
        {
            "Name": "Album",
            "Fields": [
                {
                    "Name": "AlbumId",
                    "NativeType": "INTEGER",
                    "IsPrimaryKey": true
                },
                {
                    "Name": "Title",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 160
                },
                {
                    "Name": "ArtistId",
                    "NativeType": "INTEGER"
                }
            ],
            "KeyConstraints": [
                {
                    "Columns": [
                        "AlbumId"
                    ],
                    "ReferencedColumns": []
                },
                {
                    "KeyType": "Foreign",
                    "Columns": [
                        "ArtistId"
                    ],
                    "ReferencedEntity": "Artist",
                    "ReferencedColumns": [
                        "ArtistId"
                    ]
                }
            ],
            "Relationships": [
                {
                    "SourceEntity": "Album",
                    "SourceField": "ArtistId",
                    "TargetEntity": "Artist",
                    "TargetField": "ArtistId",
                    "Cardinality": "ManyToOne"
                },
                {
                    "SourceEntity": "Album",
                    "SourceField": "AlbumId",
                    "TargetEntity": "Track",
                    "TargetField": "AlbumId",
                    "Cardinality": "OneToMany"
                }
            ],
            "Data": []
        },
        {
            "Name": "Artist",
            "Fields": [
                {
                    "Name": "ArtistId",
                    "NativeType": "INTEGER",
                    "IsPrimaryKey": true
                },
                {
                    "Name": "Name",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 120,
                    "IsNullable": true
                }
            ],
            "KeyConstraints": [
                {
                    "Columns": [
                        "ArtistId"
                    ],
                    "ReferencedColumns": []
                }
            ],
            "Relationships": [
                {
                    "SourceEntity": "Artist",
                    "SourceField": "ArtistId",
                    "TargetEntity": "Album",
                    "TargetField": "ArtistId",
                    "Cardinality": "OneToMany"
                }
            ],
            "Data": []
        },
        {
            "Name": "Customer",
            "Fields": [
                {
                    "Name": "CustomerId",
                    "NativeType": "INTEGER",
                    "IsPrimaryKey": true
                },
                {
                    "Name": "FirstName",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 40
                },
                {
                    "Name": "LastName",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 20
                },
                {
                    "Name": "Company",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 80,
                    "IsNullable": true
                },
                {
                    "Name": "Address",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 70,
                    "IsNullable": true
                },
                {
                    "Name": "City",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 40,
                    "IsNullable": true
                },
                {
                    "Name": "State",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 40,
                    "IsNullable": true
                },
                {
                    "Name": "Country",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 40,
                    "IsNullable": true
                },
                {
                    "Name": "PostalCode",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 10,
                    "IsNullable": true
                },
                {
                    "Name": "Phone",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 24,
                    "IsNullable": true
                },
                {
                    "Name": "Fax",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 24,
                    "IsNullable": true
                },
                {
                    "Name": "Email",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 60
                },
                {
                    "Name": "SupportRepId",
                    "NativeType": "INTEGER",
                    "IsNullable": true
                }
            ],
            "KeyConstraints": [
                {
                    "Columns": [
                        "CustomerId"
                    ],
                    "ReferencedColumns": []
                },
                {
                    "KeyType": "Foreign",
                    "Columns": [
                        "SupportRepId"
                    ],
                    "ReferencedEntity": "Employee",
                    "ReferencedColumns": [
                        "EmployeeId"
                    ]
                }
            ],
            "Relationships": [
                {
                    "SourceEntity": "Customer",
                    "SourceField": "SupportRepId",
                    "TargetEntity": "Employee",
                    "TargetField": "EmployeeId",
                    "Cardinality": "ManyToOne"
                },
                {
                    "SourceEntity": "Customer",
                    "SourceField": "CustomerId",
                    "TargetEntity": "Invoice",
                    "TargetField": "CustomerId",
                    "Cardinality": "OneToMany"
                }
            ],
            "Data": []
        },
        {
            "Name": "Employee",
            "Fields": [
                {
                    "Name": "EmployeeId",
                    "NativeType": "INTEGER",
                    "IsPrimaryKey": true
                },
                {
                    "Name": "LastName",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 20
                },
                {
                    "Name": "FirstName",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 20
                },
                {
                    "Name": "Title",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 30,
                    "IsNullable": true
                },
                {
                    "Name": "ReportsTo",
                    "NativeType": "INTEGER",
                    "IsNullable": true
                },
                {
                    "Name": "BirthDate",
                    "Type": "DateTime",
                    "NativeType": "DATETIME",
                    "IsNullable": true
                },
                {
                    "Name": "HireDate",
                    "Type": "DateTime",
                    "NativeType": "DATETIME",
                    "IsNullable": true
                },
                {
                    "Name": "Address",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 70,
                    "IsNullable": true
                },
                {
                    "Name": "City",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 40,
                    "IsNullable": true
                },
                {
                    "Name": "State",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 40,
                    "IsNullable": true
                },
                {
                    "Name": "Country",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 40,
                    "IsNullable": true
                },
                {
                    "Name": "PostalCode",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 10,
                    "IsNullable": true
                },
                {
                    "Name": "Phone",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 24,
                    "IsNullable": true
                },
                {
                    "Name": "Fax",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 24,
                    "IsNullable": true
                },
                {
                    "Name": "Email",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 60,
                    "IsNullable": true
                }
            ],
            "KeyConstraints": [
                {
                    "Columns": [
                        "EmployeeId"
                    ],
                    "ReferencedColumns": []
                },
                {
                    "KeyType": "Foreign",
                    "Columns": [
                        "ReportsTo"
                    ],
                    "ReferencedEntity": "Employee",
                    "ReferencedColumns": [
                        "EmployeeId"
                    ]
                }
            ],
            "Relationships": [
                {
                    "SourceEntity": "Employee",
                    "SourceField": "EmployeeId",
                    "TargetEntity": "Customer",
                    "TargetField": "SupportRepId",
                    "Cardinality": "OneToMany"
                },
                {
                    "SourceEntity": "Employee",
                    "SourceField": "ReportsTo",
                    "TargetEntity": "Employee",
                    "TargetField": "EmployeeId",
                    "Cardinality": "ManyToOne"
                },
                {
                    "SourceEntity": "Employee",
                    "SourceField": "EmployeeId",
                    "TargetEntity": "Employee",
                    "TargetField": "ReportsTo",
                    "Cardinality": "OneToMany"
                }
            ],
            "Data": []
        },
        {
            "Name": "Genre",
            "Fields": [
                {
                    "Name": "GenreId",
                    "NativeType": "INTEGER",
                    "IsPrimaryKey": true
                },
                {
                    "Name": "Name",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 120,
                    "IsNullable": true
                }
            ],
            "KeyConstraints": [
                {
                    "Columns": [
                        "GenreId"
                    ],
                    "ReferencedColumns": []
                }
            ],
            "Relationships": [
                {
                    "SourceEntity": "Genre",
                    "SourceField": "GenreId",
                    "TargetEntity": "Track",
                    "TargetField": "GenreId",
                    "Cardinality": "OneToMany"
                }
            ],
            "Data": []
        },
        {
            "Name": "Invoice",
            "Fields": [
                {
                    "Name": "InvoiceId",
                    "NativeType": "INTEGER",
                    "IsPrimaryKey": true
                },
                {
                    "Name": "CustomerId",
                    "NativeType": "INTEGER"
                },
                {
                    "Name": "InvoiceDate",
                    "Type": "DateTime",
                    "NativeType": "DATETIME"
                },
                {
                    "Name": "BillingAddress",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 70,
                    "IsNullable": true
                },
                {
                    "Name": "BillingCity",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 40,
                    "IsNullable": true
                },
                {
                    "Name": "BillingState",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 40,
                    "IsNullable": true
                },
                {
                    "Name": "BillingCountry",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 40,
                    "IsNullable": true
                },
                {
                    "Name": "BillingPostalCode",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 10,
                    "IsNullable": true
                },
                {
                    "Name": "Total",
                    "Type": "Decimal",
                    "NativeType": "NUMERIC(10,2)"
                }
            ],
            "KeyConstraints": [
                {
                    "Columns": [
                        "InvoiceId"
                    ],
                    "ReferencedColumns": []
                },
                {
                    "KeyType": "Foreign",
                    "Columns": [
                        "CustomerId"
                    ],
                    "ReferencedEntity": "Customer",
                    "ReferencedColumns": [
                        "CustomerId"
                    ]
                }
            ],
            "Relationships": [
                {
                    "SourceEntity": "Invoice",
                    "SourceField": "CustomerId",
                    "TargetEntity": "Customer",
                    "TargetField": "CustomerId",
                    "Cardinality": "ManyToOne"
                },
                {
                    "SourceEntity": "Invoice",
                    "SourceField": "InvoiceId",
                    "TargetEntity": "InvoiceLine",
                    "TargetField": "InvoiceId",
                    "Cardinality": "OneToMany"
                }
            ],
            "Data": []
        },
        {
            "Name": "InvoiceLine",
            "Fields": [
                {
                    "Name": "InvoiceLineId",
                    "NativeType": "INTEGER",
                    "IsPrimaryKey": true
                },
                {
                    "Name": "InvoiceId",
                    "NativeType": "INTEGER"
                },
                {
                    "Name": "TrackId",
                    "NativeType": "INTEGER"
                },
                {
                    "Name": "UnitPrice",
                    "Type": "Decimal",
                    "NativeType": "NUMERIC(10,2)"
                },
                {
                    "Name": "Quantity",
                    "NativeType": "INTEGER"
                }
            ],
            "KeyConstraints": [
                {
                    "Columns": [
                        "InvoiceLineId"
                    ],
                    "ReferencedColumns": []
                },
                {
                    "KeyType": "Foreign",
                    "Columns": [
                        "TrackId"
                    ],
                    "ReferencedEntity": "Track",
                    "ReferencedColumns": [
                        "TrackId"
                    ]
                },
                {
                    "KeyType": "Foreign",
                    "Columns": [
                        "InvoiceId"
                    ],
                    "ReferencedEntity": "Invoice",
                    "ReferencedColumns": [
                        "InvoiceId"
                    ]
                }
            ],
            "Relationships": [
                {
                    "SourceEntity": "InvoiceLine",
                    "SourceField": "TrackId",
                    "TargetEntity": "Track",
                    "TargetField": "TrackId",
                    "Cardinality": "ManyToOne"
                },
                {
                    "SourceEntity": "InvoiceLine",
                    "SourceField": "InvoiceId",
                    "TargetEntity": "Invoice",
                    "TargetField": "InvoiceId",
                    "Cardinality": "ManyToOne"
                }
            ],
            "Data": []
        },
        {
            "Name": "MediaType",
            "Fields": [
                {
                    "Name": "MediaTypeId",
                    "NativeType": "INTEGER",
                    "IsPrimaryKey": true
                },
                {
                    "Name": "Name",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 120,
                    "IsNullable": true
                }
            ],
            "KeyConstraints": [
                {
                    "Columns": [
                        "MediaTypeId"
                    ],
                    "ReferencedColumns": []
                }
            ],
            "Relationships": [
                {
                    "SourceEntity": "MediaType",
                    "SourceField": "MediaTypeId",
                    "TargetEntity": "Track",
                    "TargetField": "MediaTypeId",
                    "Cardinality": "OneToMany"
                }
            ],
            "Data": []
        },
        {
            "Name": "Playlist",
            "Fields": [
                {
                    "Name": "PlaylistId",
                    "NativeType": "INTEGER",
                    "IsPrimaryKey": true
                },
                {
                    "Name": "Name",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 120,
                    "IsNullable": true
                }
            ],
            "KeyConstraints": [
                {
                    "Columns": [
                        "PlaylistId"
                    ],
                    "ReferencedColumns": []
                }
            ],
            "Relationships": [
                {
                    "SourceEntity": "Playlist",
                    "SourceField": "PlaylistId",
                    "TargetEntity": "PlaylistTrack",
                    "TargetField": "PlaylistId",
                    "Cardinality": "OneToMany"
                }
            ],
            "Data": []
        },
        {
            "Name": "PlaylistTrack",
            "Fields": [
                {
                    "Name": "PlaylistId",
                    "NativeType": "INTEGER",
                    "IsPrimaryKey": true
                },
                {
                    "Name": "TrackId",
                    "NativeType": "INTEGER",
                    "IsPrimaryKey": true
                }
            ],
            "KeyConstraints": [
                {
                    "Columns": [
                        "PlaylistId",
                        "TrackId"
                    ],
                    "ReferencedColumns": []
                },
                {
                    "KeyType": "Foreign",
                    "Columns": [
                        "TrackId"
                    ],
                    "ReferencedEntity": "Track",
                    "ReferencedColumns": [
                        "TrackId"
                    ]
                },
                {
                    "KeyType": "Foreign",
                    "Columns": [
                        "PlaylistId"
                    ],
                    "ReferencedEntity": "Playlist",
                    "ReferencedColumns": [
                        "PlaylistId"
                    ]
                }
            ],
            "Relationships": [
                {
                    "SourceEntity": "PlaylistTrack",
                    "SourceField": "TrackId",
                    "TargetEntity": "Track",
                    "TargetField": "TrackId",
                    "Cardinality": "ManyToOne"
                },
                {
                    "SourceEntity": "PlaylistTrack",
                    "SourceField": "PlaylistId",
                    "TargetEntity": "Playlist",
                    "TargetField": "PlaylistId",
                    "Cardinality": "ManyToOne"
                }
            ],
            "Data": []
        },
        {
            "Name": "Track",
            "Fields": [
                {
                    "Name": "TrackId",
                    "NativeType": "INTEGER",
                    "IsPrimaryKey": true
                },
                {
                    "Name": "Name",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 200
                },
                {
                    "Name": "AlbumId",
                    "NativeType": "INTEGER",
                    "IsNullable": true
                },
                {
                    "Name": "MediaTypeId",
                    "NativeType": "INTEGER"
                },
                {
                    "Name": "GenreId",
                    "NativeType": "INTEGER",
                    "IsNullable": true
                },
                {
                    "Name": "Composer",
                    "Type": "String",
                    "NativeType": "NVARCHAR",
                    "MaxLength": 220,
                    "IsNullable": true
                },
                {
                    "Name": "Milliseconds",
                    "NativeType": "INTEGER"
                },
                {
                    "Name": "Bytes",
                    "NativeType": "INTEGER",
                    "IsNullable": true
                },
                {
                    "Name": "UnitPrice",
                    "Type": "Decimal",
                    "NativeType": "NUMERIC(10,2)"
                }
            ],
            "KeyConstraints": [
                {
                    "Columns": [
                        "TrackId"
                    ],
                    "ReferencedColumns": []
                },
                {
                    "KeyType": "Foreign",
                    "Columns": [
                        "MediaTypeId"
                    ],
                    "ReferencedEntity": "MediaType",
                    "ReferencedColumns": [
                        "MediaTypeId"
                    ]
                },
                {
                    "KeyType": "Foreign",
                    "Columns": [
                        "GenreId"
                    ],
                    "ReferencedEntity": "Genre",
                    "ReferencedColumns": [
                        "GenreId"
                    ]
                },
                {
                    "KeyType": "Foreign",
                    "Columns": [
                        "AlbumId"
                    ],
                    "ReferencedEntity": "Album",
                    "ReferencedColumns": [
                        "AlbumId"
                    ]
                }
            ],
            "Relationships": [
                {
                    "SourceEntity": "Track",
                    "SourceField": "TrackId",
                    "TargetEntity": "InvoiceLine",
                    "TargetField": "TrackId",
                    "Cardinality": "OneToMany"
                },
                {
                    "SourceEntity": "Track",
                    "SourceField": "TrackId",
                    "TargetEntity": "PlaylistTrack",
                    "TargetField": "TrackId",
                    "Cardinality": "OneToMany"
                },
                {
                    "SourceEntity": "Track",
                    "SourceField": "MediaTypeId",
                    "TargetEntity": "MediaType",
                    "TargetField": "MediaTypeId",
                    "Cardinality": "ManyToOne"
                },
                {
                    "SourceEntity": "Track",
                    "SourceField": "GenreId",
                    "TargetEntity": "Genre",
                    "TargetField": "GenreId",
                    "Cardinality": "ManyToOne"
                },
                {
                    "SourceEntity": "Track",
                    "SourceField": "AlbumId",
                    "TargetEntity": "Album",
                    "TargetField": "AlbumId",
                    "Cardinality": "ManyToOne"
                }
            ],
            "Data": []
        }
    ]
}

export const dataSource2: DataSource = {
    Id: 2,
    "Name": "ExcelFile1",
    "Type": "File",
    "Provider": "Excel",
    "ConnectionString": "C:\\Users\\<USER>\\OneDrive - Aubrant Digital\\DataSource3.xlsx",
    "Entities": [
        {
            "Name": "Financial",
            "Fields": [
                {
                    "Name": "Id",
                    "Type": "Decimal",
                    "NativeType": "ExcelGeneral",
                    "IsPrimaryKey": true,
                    "IsIdentity": true
                },
                {
                    "Name": "Segment",
                    "Type": "String",
                    "NativeType": "ExcelGeneral"
                },
                {
                    "Name": "Country",
                    "Type": "String",
                    "NativeType": "ExcelGeneral"
                },
                {
                    "Name": "Product",
                    "Type": "String",
                    "NativeType": "ExcelGeneral"
                },
                {
                    "Name": "Discount Band",
                    "Type": "String",
                    "NativeType": "ExcelGeneral"
                },
                {
                    "Name": "Units Sold",
                    "Type": "Decimal",
                    "NativeType": "ExcelGeneral"
                },
                {
                    "Name": "Manufacturing Price",
                    "Type": "Decimal",
                    "NativeType": "ExcelGeneral"
                },
                {
                    "Name": "Sale Price",
                    "Type": "Decimal",
                    "NativeType": "ExcelGeneral"
                },
                {
                    "Name": "Gross Sales",
                    "Type": "Decimal",
                    "NativeType": "ExcelGeneral"
                },
                {
                    "Name": "Discounts",
                    "Type": "Decimal",
                    "NativeType": "ExcelGeneral"
                },
                {
                    "Name": " Sales",
                    "Type": "Decimal",
                    "NativeType": "ExcelGeneral"
                },
                {
                    "Name": "COGS",
                    "Type": "Decimal",
                    "NativeType": "ExcelGeneral"
                },
                {
                    "Name": "Profit",
                    "Type": "Decimal",
                    "NativeType": "ExcelGeneral"
                },
                {
                    "Name": "Date",
                    "Type": "DateTime",
                    "NativeType": "ExcelGeneral"
                },
                {
                    "Name": "Month Number",
                    "Type": "Decimal",
                    "NativeType": "ExcelGeneral"
                },
                {
                    "Name": "Month Name",
                    "Type": "String",
                    "NativeType": "ExcelGeneral"
                },
                {
                    "Name": "Year",
                    "Type": "String",
                    "NativeType": "ExcelGeneral"
                }
            ],
            "KeyConstraints": [
                {
                    "Columns": [
                        "Id"
                    ],
                    "ReferencedColumns": []
                }
            ],
            "Relationships":
            [
                {
                    "SourceEntity": "Financial",
                    "SourceField": "Product",
                    "TargetEntity": "Sales Orders",
                    "TargetField": "Item",
                    "Cardinality": "OneToMany"
                }
            ],
            "Data": []
        },
        {
            "Name": "Sales Orders",
            "Fields": [
                {
                    "Name": "OrderId",
                    "Type": "Decimal",
                    "NativeType": "ExcelGeneral",
                    "IsPrimaryKey": true,
                    "IsIdentity": true
                },
                {
                    "Name": "OrderDate",
                    "Type": "DateTime",
                    "NativeType": "ExcelGeneral"
                },
                {
                    "Name": "Region",
                    "Type": "String",
                    "NativeType": "ExcelGeneral"
                },
                {
                    "Name": "Rep",
                    "Type": "String",
                    "NativeType": "ExcelGeneral"
                },
                {
                    "Name": "Item",
                    "Type": "String",
                    "NativeType": "ExcelGeneral"
                },
                {
                    "Name": "Units",
                    "Type": "Decimal",
                    "NativeType": "ExcelGeneral"
                },
                {
                    "Name": "Unit Cost",
                    "Type": "Decimal",
                    "NativeType": "ExcelGeneral"
                },
                {
                    "Name": "Total",
                    "Type": "Decimal",
                    "NativeType": "ExcelGeneral"
                }
            ],
            "KeyConstraints": [
                {
                    "Columns": [
                        "OrderId"
                    ],
                    "ReferencedColumns": []
                }
            ],
            "Relationships":
            [
                {
                    "SourceEntity": "Album",
                    "SourceField": "ArtistId",
                    "TargetEntity": "Artist",
                    "TargetField": "ArtistId",
                    "Cardinality": "ManyToOne"
                }
            ],
            "Data": []
        }
    ]
}

export const sampleProject: Project = {
    Id: 1,
    Name: "Project 1",
    Description: "Test Data Generator Project 1",
    Author: "Heiner Morales",
    CreatedDate: "2025-01-01",
    DataSources: [
        dataSource1,
        dataSource2
    ]
}