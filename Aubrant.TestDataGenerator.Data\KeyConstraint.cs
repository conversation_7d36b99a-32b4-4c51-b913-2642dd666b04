using Aubrant.TestDataGenerator.Data.Enums;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Aubrant.TestDataGenerator.Data
{
    /// <summary>
    /// Represents a key constraint (Primary or Foreign) within an entity.
    /// </summary>
    public class KeyConstraint
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        public int EntityId { get; set; }

        /// <summary>
        /// Gets or sets the type of the key (Primary or Foreign).
        /// </summary>
        [JsonConverter(typeof(StringEnumConverter))]
        public KeyType KeyType { get; set; }

        /// <summary>
        /// Gets or sets the list of column names that form this key constraint.
        /// </summary>
        public List<string> Columns { get; set; } = new List<string>();
        
        /// <summary>
        /// Gets or sets the name of the entity that this key references (for foreign keys).
        /// </summary>
        public string? ReferencedEntity { get; set;}
        
        /// <summary>
        /// Gets or sets the list of column names in the referenced entity (for foreign keys).
        /// </summary>
        public List<string> ReferencedColumns { get; set; } = new List<string>();

        [ForeignKey("EntityId")]
        public virtual Entity? Entity { get; set; }
    }
}