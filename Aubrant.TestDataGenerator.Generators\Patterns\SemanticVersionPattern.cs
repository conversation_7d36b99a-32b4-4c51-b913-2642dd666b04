using Aubrant.TestDataGenerator.Core.Patterns;

namespace Aubrant.TestDataGenerator.Generators.Patterns
{
    public class SemanticVersionPattern : BasePattern
    {
        public SemanticVersionPattern() : base(
            name: "Semantic Version",
            description: "Software version in MAJOR.MINOR.PATCH format. May include leading zeros.",
            pattern: "{MAJOR}.{MINOR}.{PATCH}",
            tokens: new List<Token>
            {
                new DigitsToken("MAJOR", "Major version", 1, (0, 5)),
                new DigitsToken("MINOR", "Minor version", 2, (0, 20)),
                new DigitsToken("PATCH", "Patch version", 3, (0, 100))
            })
        { }
    }
}
