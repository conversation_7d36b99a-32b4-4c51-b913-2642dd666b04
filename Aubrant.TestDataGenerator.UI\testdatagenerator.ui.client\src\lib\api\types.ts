// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  success: boolean
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  error?: string
}

// Project Types
export interface Project {
  id: string
  name: string
  dataSources: Array<{
    id: string
    name: string
    type: string
    provider: string
  }>
  owner: string
  creationDate: string
  lastExecutionDate: string
}

export interface CreateProjectRequest {
  name: string
  version: string
  author: string
  description: string
  dataSources: DataSource[]
  schemaSelection: {
    selectedTables: string[]
  }
}

// Data Source Types
export interface DataSource {
  id: string
  name: string
  description: string
  type: string
  provider: string
  connectionProperties: ConnectionProperties
  schemas?: Schema[]
  collections?: Collection[]
}

export interface ConnectionProperties {
  // File properties
  path?: string
  // Relational DB properties
  server?: string
  port?: string
  userId?: string
  password?: string
  database?: string
  connectionString?: string
  // NoSQL properties
  host?: string
  cluster?: string
  username?: string
  authDatabase?: string
  ssl?: boolean
  uri?: string
  endpoint?: string
  region?: string
}

export interface Schema {
  name: string
  tables: DatabaseTable[]
}

export interface Collection {
  name: string
  fields: TableColumn[]
  relationships: TableRelationship[]
}

export interface DatabaseTable {
  name: string
  schema: string
  columns: TableColumn[]
  relationships: TableRelationship[]
}

export interface TableColumn {
  name: string
  type: string
  isPrimaryKey: boolean
  isNotNull: boolean
}

export interface TableRelationship {
  type: "OneToMany" | "ManyToOne" | "OneToOne" | "ManyToMany"
  targetTable?: string
  foreignKey?: string
  targetCollection?: string
}

// Test Connection Types
export interface TestConnectionRequest {
  type: string
  provider: string
  connectionProperties: ConnectionProperties
}

export interface TestConnectionResponse {
  success: boolean
  message: string
  canAccessSchema: boolean
  schemaObjects?: {
    schemas?: Schema[]
    collections?: Collection[]
  }
}

// Schema Discovery Types
export interface SchemaDiscoveryRequest {
  dataSourceId: string
  connectionProperties: ConnectionProperties
}

export interface SchemaDiscoveryResponse {
  success: boolean
  dataSource: DataSource
  error?: string
}
