using Bogus;
using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Functions;

namespace Aubrant.TestDataGenerator.Generators.Functions{
    /// <summary>
    /// A generator function that produces a random job title.
    /// </summary>
    public class JobTitleFunction : BaseFunction
    {
        public override string Name => "JobTitle";
        public override string Description => "Generates a random job title.";
        public override DataType ReturnType => DataType.String;

        /// <summary>
        /// Initializes a new instance of the JobTitleFunction class.
        /// </summary>
        public JobTitleFunction() : base("Business")
        {
            Parameters.Add(new Parameter("locale", "The output locale.", DataType.String, new DataValue("en")));
        }

        /// <summary>
        /// Generates a random job title using the Bogus library.
        /// </summary>
        public override DataValue Generate(DataGeneratorContext context)
        {
            string locale = Parameters["locale"].Value?.ToString() ?? "en";
            var faker = new Faker(locale);
            string jobTitle = faker.Name.JobTitle();
            return new DataValue(DataType.String, jobTitle);
        }
    }
}
