using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.Data.Enums;
using Aubrant.TestDataGenerator.SchemaProfiler.Common;
using Aubrant.TestDataGenerator.SchemaProfiler.Logging;
using System.Data;
using System.Globalization;

namespace Aubrant.TestDataGenerator.SchemaProfiler.Extractors
{
    /// <summary>
    /// Extracts schema and loads sample data from CSV files.
    /// </summary>
    public class CsvSchemaExtractor : IDataSourceSchemaExtractor
    {
        private readonly int _sampleSize;
        private readonly ILogger _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="CsvSchemaExtractor"/> class.
        /// </summary>
        /// <param name="options">The schema extraction options.</param>
        /// <param name="logger">Optional. An ILogger instance for logging. If null, a NullLogger will be used.</param>
        public CsvSchemaExtractor(SchemaExtractionOptions? options = null, ILogger? logger = null)
        {
            options = options ?? new SchemaExtractionOptions();
            _sampleSize = options.SampleSize;
            _logger = logger ?? new NullLogger();
            _logger.LogInfo($"CsvSchemaExtractor initialized with SampleSize: {_sampleSize}");
        }

        /// <summary>
        /// Extracts the schema from a CSV file asynchronously.
        /// </summary>
        /// <param name="filePath">The path to the CSV file.</param>
        /// <param name="dataSourceName">The name to assign to the data source.</param>
        /// <returns>A Task representing the asynchronous operation, returning the extracted DataSource schema.</returns>
        public async Task<DataSource> ExtractAsync(string filePath, string? dataSourceName)
        {
            _logger.LogInfo($"Extracting schema from CSV: {filePath}");
            var entityName = Path.GetFileName(filePath);
            var entity = new Entity { Name = entityName };

            var dataSource = new DataSource
            {
                Type = DataSourceType.File,
                Provider = DataSourceProvider.CSV,
                Name = dataSourceName ?? entityName,
                ConnectionString = filePath
            };

            try
            {
                using (var reader = new StreamReader(filePath))
                using (var csv = new CsvHelper.CsvReader(reader, System.Globalization.CultureInfo.InvariantCulture))
                {
                    await csv.ReadAsync();
                    csv.ReadHeader();
                    string[]? headers = csv.Context.Reader.HeaderRecord;

                    if (headers == null || headers.Length == 0)
                    {
                        _logger.LogError($"CSV file {filePath} is empty or has no headers.");
                        throw new InvalidOperationException("CSV is empty or has no headers.");
                    }

                    _logger.LogInfo($"Reading {_sampleSize} sample records from {filePath}");
                    var sampleRecords = await ReadSampleRecordsAsync(csv, _sampleSize);

                    InferFieldsAndPrimaryKeys(entity, sampleRecords, headers);
                    entity.Data = PopulateDataTable(entity, sampleRecords, headers);
                }
                _logger.LogInfo($"Schema extracted for {entity.Name}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error extracting schema from CSV file {filePath}", ex);
                throw;
            }

            dataSource.Entities.Add(entity);
            return dataSource;
        }

        /// <summary>
        /// Loads sample data into the provided DataSource (not applicable for CSV as data is loaded during schema extraction).
        /// </summary>
        /// <param name="dataSource">The DataSource object to populate with sample data.</param>
        /// <param name="connectionString">The connection string (not used for CSV).</param>
        /// <param name="sampleSize">The number of sample rows to load (not used for CSV).</param>
        /// <returns>A Task representing the asynchronous operation.</returns>
        public Task LoadSampleDataAsync(DataSource dataSource, string connectionString, int? sampleSize = null)
        {
            _logger.LogInfo($"LoadSampleDataAsync not applicable for CSV (data loaded during schema extraction).");
            return Task.CompletedTask;
        }

        /// <summary>
        /// Reads a specified number of sample records from a CSV file.
        /// </summary>
        /// <param name="csv">The CsvReader instance.</param>
        /// <param name="maxSampleRows">The maximum number of rows to read.</param>
        /// <returns>A list of string arrays, each representing a row of data.</returns>
        private async Task<List<string[]>> ReadSampleRecordsAsync(CsvHelper.CsvReader csv, int maxSampleRows)
        {
            var sampleRecords = new List<string[]>();
            while (await csv.ReadAsync() && sampleRecords.Count < maxSampleRows)
            {
                if (csv.Parser.Record != null)
                {
                    sampleRecords.Add(csv.Parser.Record);
                }
            }
            return sampleRecords;
        }

        /// <summary>
        /// Infers fields and primary keys for an entity based on sample CSV records.
        /// </summary>
        /// <param name="entity">The entity to populate with inferred fields.</param>
        /// <param name="sampleRecords">Sample data records from the CSV.</param>
        /// <param name="headers">The column headers from the CSV.</param>
        private void InferFieldsAndPrimaryKeys(Entity entity, List<string[]> sampleRecords, string[] headers)
        {
            _logger.LogInfo($"Inferring fields and primary keys for entity {entity.Name}");
            for (int i = 0; i < headers.Length; i++)
            {
                string header = headers[i];
                var field = new Field { Name = header, IsNullable = false };

                (DataType inferredType, bool isNullable) = TypeInferrer.InferColumnType(sampleRecords, i);
                field.Type = inferredType;
                field.NativeType = inferredType.ToString();
                field.IsNullable = isNullable;

                if (header.EndsWith("Id", StringComparison.OrdinalIgnoreCase) && field.Type == DataType.Integer)
                {
                    bool isUnique = sampleRecords.Select(row => row[i]).Distinct().Count() == sampleRecords.Count;
                    bool isSequential = TypeInferrer.IsSequential(sampleRecords, i);
                    field.IsPrimaryKey = isUnique;
                    field.IsIdentity = isUnique && isSequential;
                    if (isUnique)
                    {
                        _logger.LogInfo($"Inferred primary key: {field.Name} (IsIdentity: {field.IsIdentity})");
                        entity.KeyConstraints.Add(new KeyConstraint
                        {
                            KeyType = KeyType.Primary,
                            Columns = new List<string> { header }
                        });
                    }
                }
                entity.Fields.Add(field);
            }
        }

        /// <summary>
        /// Populates a DataTable with sample records for a given entity.
        /// </summary>
        /// <param name="entity">The entity whose DataTable is to be populated.</param>
        /// <param name="sampleRecords">The sample data records.</param>
        /// <param name="headers">The column headers.</param>
        /// <returns>A DataTable containing the sample data.</returns>
        private DataTable PopulateDataTable(Entity entity, List<string[]> sampleRecords, string[] headers)
        {
            _logger.LogInfo($"Populating DataTable for entity {entity.Name}");
            var dataTable = new DataTable(entity.Name);
            foreach (var field in entity.Fields)
            {
                dataTable.Columns.Add(new DataColumn(field.Name, DataTypeMapper.ToSystemType(field.Type)));
            }

            foreach (var record in sampleRecords)
            {
                var row = dataTable.NewRow();
                for (int j = 0; j < headers.Length; j++)
                {
                    row[headers[j]] = ConvertValue(record[j], entity.Fields[j].Type);
                }
                dataTable.Rows.Add(row);
            }
            return dataTable;
        }

        

        /// <summary>
        /// Converts a string value to the specified DataType.
        /// </summary>
        /// <param name="value">The string value to convert.</param>
        /// <param name="dataType">The target DataType.</param>
        /// <returns>The converted object, or DBNull.Value if conversion fails or value is null/empty.</returns>
        private object ConvertValue(string value, DataType dataType)
        {
            if (string.IsNullOrEmpty(value)) return DBNull.Value;
            try
            {
                return dataType switch
                {
                    DataType.Integer => int.TryParse(value, NumberStyles.None, CultureInfo.InvariantCulture, out int intResult) ? intResult : throw new FormatException($"Cannot convert '{value}' to Integer."),
                    DataType.Decimal => decimal.TryParse(value, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal decResult) ? decResult : throw new FormatException($"Cannot convert '{value}' to Decimal."),
                    DataType.Boolean => bool.TryParse(value, out bool boolResult) ? boolResult : (value == "1" ? true : value == "0" ? false : throw new FormatException($"Cannot convert '{value}' to Boolean.")),
                    DataType.DateTime => DateTime.TryParse(value, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime dtResult) ? dtResult : throw new FormatException($"Cannot convert '{value}' to DateTime."),
                    _ => value
                };
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Failed to convert CSV value '{value}' to DataType.{dataType}. Error: {ex.Message}");
                return DBNull.Value;
            }
        }
    }
}