<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <SpaRoot>..\testdatagenerator.ui.client</SpaRoot>
    <SpaProxyLaunchCommand>npm run dev</SpaProxyLaunchCommand>
    <SpaProxyServerUrl>https://localhost:53181</SpaProxyServerUrl>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="HotChocolate.AspNetCore" Version="16.0.0-p.6.14" />
    <PackageReference Include="HotChocolate.Data.EntityFramework" Version="16.0.0-p.6.14" />
    <PackageReference Include="Microsoft.AspNetCore.SpaProxy">
      <Version>10.0.0-preview.6.25358.103</Version>
    </PackageReference>
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />
  </ItemGroup>

  <ItemGroup>
    
    <ProjectReference Include="..\Aubrant.TestDataGenerator.UI.Client\Aubrant.TestDataGenerator.UI.Client.esproj">
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
    <ProjectReference Include="..\..\Aubrant.TestDataGenerator.Data\Aubrant.TestDataGenerator.Data.csproj" />
  </ItemGroup>

</Project>
