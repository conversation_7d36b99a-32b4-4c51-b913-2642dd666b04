using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Data.Enums;
using System.Globalization;

namespace Aubrant.TestDataGenerator.SchemaProfiler.Common
{
    /// <summary>
    /// Provides helper methods for inferring data types and properties from sample data.
    /// </summary>
    public static class TypeInferrer
    {
        /// <summary>
        /// Infers the data type and nullability of a column based on a list of string arrays (e.g., from CSV).
        /// </summary>
        /// <param name="sampleRows">A list of string arrays representing sample rows.</param>
        /// <param name="columnIndex">The index of the column to infer.</param>
        /// <returns>A tuple containing the inferred DataType and a boolean indicating if the column is nullable.</returns>
        public static (DataType, bool) InferColumnType(List<string[]> sampleRows, int columnIndex)
        {
            bool isNullable = sampleRows.Any(row => string.IsNullOrEmpty(row[columnIndex]?.Trim()));
            var nonNullValues = sampleRows.Select(row => row[columnIndex]?.Trim()).Where(v => !string.IsNullOrEmpty(v)).Select(v => v!).Distinct().ToList();

            if (!nonNullValues.Any()) return (DataType.String, true);
            if (IsBoolean(nonNullValues)) return (DataType.Boolean, isNullable);
            if (IsInteger(nonNullValues)) return (DataType.Integer, isNullable);
            if (IsDecimal(nonNullValues)) return (DataType.Decimal, isNullable);
            if (IsDateTime(nonNullValues)) return (DataType.DateTime, isNullable);

            return (DataType.String, isNullable);
        }

        /// <summary>
        /// Infers the data type and nullability of a column based on a list of object arrays (e.g., from Excel).
        /// </summary>
        /// <param name="sampleRecords">A list of object arrays representing sample records.</param>
        /// <param name="columnIndex">The index of the column to infer.</param>
        /// <returns>A tuple containing the inferred DataType and a boolean indicating if the column is nullable.</returns>
        public static (DataType, bool) InferColumnType(List<object[]> sampleRecords, int columnIndex)
        {
            bool isNullable = false;
            var types = new HashSet<DataType>();
            foreach (var record in sampleRecords)
            {
                if (columnIndex >= record.Length) continue;
                var value = record[columnIndex];
                if (value == null || value == DBNull.Value) { isNullable = true; continue; }
                if (value is int or long or short or byte) types.Add(DataType.Integer);
                else if (value is decimal or double or float) types.Add(DataType.Decimal);
                else if (value is bool) types.Add(DataType.Boolean);
                else if (value is DateTime) types.Add(DataType.DateTime);
                else types.Add(DataType.String);
            }
            if (types.Count == 0) return (DataType.String, true);
            if (types.Count == 1) return (types.First(), isNullable);
            if (types.Contains(DataType.String)) return (DataType.String, isNullable);
            if (types.Contains(DataType.Decimal)) return (DataType.Decimal, isNullable);
            return (DataType.Integer, isNullable);
        }

        /// <summary>
        /// Determines if all values in a list can be interpreted as booleans.
        /// </summary>
        /// <param name="values">The list of string values.</param>
        /// <returns>True if all values are booleans, false otherwise.</returns>
        private static bool IsBoolean(List<string> values)
        {
            var validBooleans = new HashSet<string>(StringComparer.OrdinalIgnoreCase) { "true", "false", "1", "0" };
            return values.All(v => validBooleans.Contains(v));
        }

        /// <summary>
        /// Determines if all values in a list can be interpreted as integers.
        /// </summary>
        /// <param name="values">The list of string values.</param>
        /// <returns>True if all values are integers, false otherwise.</returns>
        private static bool IsInteger(List<string> values)
        {
            return values.All(v => long.TryParse(v, NumberStyles.Any, CultureInfo.InvariantCulture, out _));
        }

        /// <summary>
        /// Determines if all values in a list can be interpreted as decimals.
        /// </summary>
        /// <param name="values">The list of string values.</param>
        /// <returns>True if all values are decimals, false otherwise.</returns>
        private static bool IsDecimal(List<string> values)
        {
            return values.All(v => decimal.TryParse(v, NumberStyles.Any, CultureInfo.InvariantCulture, out _));
        }

        /// <summary>
        /// Determines if all values in a list can be interpreted as DateTime objects.
        /// </summary>
        /// <param name="values">The list of string values.</param>
        /// <returns>True if all values are DateTime objects, false otherwise.</returns>
        private static bool IsDateTime(List<string> values)
        {
            return values.All(v => DateTime.TryParse(v, CultureInfo.InvariantCulture, DateTimeStyles.None, out _));
        }

        /// <summary>
        /// Determines if the integer values in a column are sequential (e.g., 1, 2, 3...).
        /// </summary>
        /// <param name="sampleRows">A list of string arrays representing sample rows.</param>
        /// <param name="columnIndex">The index of the column to check.</param>
        /// <returns>True if the values are sequential, false otherwise.</returns>
        public static bool IsSequential(List<string[]> sampleRows, int columnIndex)
        {
            var values = sampleRows.Select(row => row[columnIndex]).Where(v => !string.IsNullOrEmpty(v)).Select(v => int.TryParse(v, out int result) ? result : (int?)null).Where(v => v.HasValue).Select(v => v.Value).OrderBy(v => v).ToList();
            if (values.Count <= 1) return true;
            for (int i = 1; i < values.Count; i++)
            {
                if (values[i] != values[i - 1] + 1) return false;
            }
            return true;
        }

        /// <summary>
        /// Determines if the integer values in a column are sequential (e.g., 1, 2, 3...).
        /// </summary>
        /// <param name="sampleRecords">A list of object arrays representing sample records.</param>
        /// <param name="columnIndex">The index of the column to check.</param>
        /// <returns>True if the values are sequential, false otherwise.</returns>
        public static bool IsSequential(List<object[]> sampleRecords, int columnIndex)
        {
            var values = sampleRecords.Select(row => row[columnIndex]).Where(v => v != null && v != DBNull.Value).Select(v =>
            {
                if (v is int i) return i;
                if (v is long l) return (int)l;
                if (v is short s) return (int)s;
                if (v is byte b) return (int)b;
                return (int?)null;
            }).Where(v => v.HasValue).Select(v => v.Value).OrderBy(v => v).ToList();

            if (values.Count <= 1) return true;
            for (int i = 1; i < values.Count; i++)
            {
                if (values[i] != values[i - 1] + 1) return false;
            }
            return true;
        }
    }
}