﻿namespace Aubrant.TestDataGenerator.Core.Enums
{
    /// <summary>
    /// Basic Data Types supported by the Generators, either in parameters or data fields
    /// </summary>
    [Flags]
    public enum DataType
    {
        Integer = 1, // Integer values, such as whole numbers
        Decimal = 2, // Decimal values, such as monetary amounts or precise measurements
        Number = Integer | Decimal,
        DateTime = 4, // Date and time values, such as timestamps or dates
        Boolean = 8, // Boolean values, true or false
        String  = 16, // String data, such as names or text
        Any = Integer | Decimal | DateTime | Boolean | String,
        Binary  = 32, // Binary data, such as images or files
        Unsupported = 64 // Unsupported or unknown data types
    }
}