﻿using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Functions;

namespace Aubrant.TestDataGenerator.Generators.Functions{
    /// <summary>
    /// A generator function that produces random decimal values following a Normal (Gaussian) distribution.
    /// It uses the Box-Muller transform.
    /// </summary>
    public class NormalDistributionFunction : BaseFunction
    {
        private bool _hasDeviate;
        private double _storedDeviate;

        public override string Name => "Normal Distribution";
        public override string Description => "Generates random numbers from a Normal (Gaussian) distribution.";
        public override DataType ReturnType => DataType.Decimal;

        /// <summary>
        /// Initializes a new instance of the NormalDistributionGenerator class.
        /// Defines parameters for Mean, Standard Deviation, and optional Min/Max for truncation.
        /// </summary>
        public NormalDistributionFunction() : base("Statistical")
        {
            Parameters.Add(new Parameter("Mean", "The mean of the distribution.", DataType.Decimal, new DataValue(0.0M))); // Default mean 0
            Parameters.Add(new Parameter("StdDev", "The standard deviation of the distribution.", DataType.Decimal, new DataValue(1.0M))); // Default standard deviation 1 (for standard normal)
            Parameters.Add(new Parameter("Min", "The minimum value for truncation.", DataType.Decimal, null)); // Optional min for truncation
            Parameters.Add(new Parameter("Max", "The maximum value for truncation.", DataType.Decimal, null)); // Optional max for truncation
        }

        /// <summary>
        /// Generates a random decimal value from a Normal distribution based on the configured Mean and Standard Deviation.
        /// Uses the Box-Muller transform for efficiency in generating pairs of normal deviates.
        /// If Min and Max parameters are set, the generated value will be truncated to fit within that range.
        /// </summary>
        /// <returns>A DataValue containing the normally distributed (and potentially truncated) decimal number.</returns>
        /// <exception cref="InvalidOperationException">Thrown if Mean, StdDev, Min, or Max parameters are missing or invalid.</exception>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if StdDev is non-positive or if Min is greater than Max.</exception>
        public override DataValue Generate(DataGeneratorContext context)
        {
            double mean = Parameters["Mean"].Value?.ToDecimal()?.ToDouble() ??
                          throw new InvalidOperationException("Mean parameter is missing or invalid.");
            double stdDev = Parameters["StdDev"].Value?.ToDecimal()?.ToDouble() ??
                            throw new InvalidOperationException("Standard Deviation parameter is missing or invalid.");

            decimal? minTruncate = Parameters["Min"].Value?.ToDecimal();
            decimal? maxTruncate = Parameters["Max"].Value?.ToDecimal();

            if (stdDev <= 0)
            {
                throw new ArgumentOutOfRangeException(nameof(stdDev), "Standard Deviation must be positive for Normal distribution.");
            }

            // Validate truncation bounds if both are provided
            if (minTruncate.HasValue && maxTruncate.HasValue && minTruncate.Value > maxTruncate.Value)
            {
                throw new ArgumentOutOfRangeException(nameof(minTruncate), "Min truncation value cannot be greater than Max truncation value.");
            }

            double u1, u2, randStdNormal;

            if (_hasDeviate)
            {
                _hasDeviate = false;
                randStdNormal = _storedDeviate;
            }
            else
            {
                // Generate two uniform random numbers
                u1 = 1.0 - context.Random.NextDouble(); // uniform(0,1], avoid log(0)
                u2 = 1.0 - context.Random.NextDouble(); // uniform(0,1], avoid log(0)

                // Apply Box-Muller transform to get two standard normal deviates
                randStdNormal = Math.Sqrt(-2.0 * Math.Log(u1)) * Math.Sin(2.0 * Math.PI * u2);
                _storedDeviate = Math.Sqrt(-2.0 * Math.Log(u1)) * Math.Cos(2.0 * Math.PI * u2); // Store the second deviate
                _hasDeviate = true;
            }

            // Scale and shift to the desired mean and standard deviation
            double result = mean + stdDev * randStdNormal;
            decimal finalResult = (decimal)result;

            // Apply truncation if minTruncate and/or maxTruncate are set
            if (minTruncate.HasValue)
            {
                finalResult = Math.Max(finalResult, minTruncate.Value);
            }
            if (maxTruncate.HasValue)
            {
                finalResult = Math.Min(finalResult, maxTruncate.Value);
            }

            return new DataValue(DataType.Decimal, finalResult);
        }
    }

    // Extension method to convert decimal to double safely.
    public static class DecimalExtensions
    {
        public static double ToDouble(this decimal d)
        {
            return (double)d;
        }
    }
}
