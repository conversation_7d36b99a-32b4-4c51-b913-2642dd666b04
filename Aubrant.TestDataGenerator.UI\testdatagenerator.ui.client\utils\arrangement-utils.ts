import type { Entity } from "src/types/database"
import { findEntityByName } from "./relationship-utils"
import { findNonOverlappingPosition } from "./layout-utils"

export const arrangeLeftRight = (
  entities: Entity[],
  getEntityDimensions: (entity: Entity) => { width: number; height: number },
  setEntities: (entities: Entity[]) => void,
) => {
  const entityMap = new Map<string, Entity>()
  entities.forEach((entity) => entityMap.set(entity.Name, entity))

  // Build dependency graph based on relationships
  const dependencies = new Map<string, Set<string>>()
  const dependents = new Map<string, Set<string>>()

  entities.forEach((entity) => {
    dependencies.set(entity.Name, new Set())
    dependents.set(entity.Name, new Set())
  })

  // Analyze relationships to build dependency graph
  entities.forEach((entity) => {
    entity.Relationships.forEach((rel) => {
      const targetEntity = findEntityByName(entities, rel.TargetEntity)
      if (targetEntity && targetEntity !== entity) {
        if (rel.Cardinality === "ManyToOne") {
          dependencies.get(entity.Name)?.add(targetEntity.Name)
          dependents.get(targetEntity.Name)?.add(entity.Name)
        } else if (rel.Cardinality === "OneToMany") {
          dependencies.get(targetEntity.Name)?.add(entity.Name)
          dependents.get(entity.Name)?.add(targetEntity.Name)
        }
      }
    })
  })

  // Topological sort to determine levels
  const levels: string[][] = []
  const visited = new Set<string>()
  const visiting = new Set<string>()

  const visit = (entityName: string): number => {
    if (visiting.has(entityName)) return 0
    if (visited.has(entityName)) {
      for (let i = 0; i < levels.length; i++) {
        if (levels[i].includes(entityName)) return i
      }
      return 0
    }

    visiting.add(entityName)
    let maxLevel = 0

    const deps = dependencies.get(entityName) || new Set()
    deps.forEach((dep) => {
      if (entityMap.has(dep)) {
        maxLevel = Math.max(maxLevel, visit(dep) + 1)
      }
    })

    visiting.delete(entityName)
    visited.add(entityName)

    if (!levels[maxLevel]) levels[maxLevel] = []
    if (!levels[maxLevel].includes(entityName)) {
      levels[maxLevel].push(entityName)
    }

    return maxLevel
  }

  entities.forEach((entity) => {
    if (!visited.has(entity.Name)) {
      visit(entity.Name)
    }
  })

  // Position entities with proper spacing
  const placedEntities: Array<{ position: { x: number; y: number }; size: { width: number; height: number } }> = []
  const newEntities = entities.map((entity) => {
    const entitySize = getEntityDimensions(entity)
    let levelIndex = 0
    let positionInLevel = 0

    for (let i = 0; i < levels.length; i++) {
      const pos = levels[i].indexOf(entity.Name)
      if (pos !== -1) {
        levelIndex = i
        positionInLevel = pos
        break
      }
    }

    // Calculate position with dynamic spacing based on entity height
    const levelX = levelIndex * 500 + 100 // Increased horizontal spacing
    const baseY = 100 + positionInLevel * (entitySize.height + 100) // Dynamic vertical spacing

    const preferredPosition = { x: levelX, y: baseY }
    const finalPosition = findNonOverlappingPosition(preferredPosition, entitySize, placedEntities, 80)

    placedEntities.push({ position: finalPosition, size: entitySize })

    return {
      ...entity,
      position: finalPosition,
    }
  })

  setEntities(newEntities)
}

export const arrangeSnowflake = (
  entities: Entity[],
  getEntityDimensions: (entity: Entity) => { width: number; height: number },
  setEntities: (entities: Entity[]) => void,
) => {
  // Calculate connection count for each entity
  const connectionCounts = new Map<string, number>()

  entities.forEach((entity) => {
    const entityKey = `${entity.Schema}.${entity.Name}`
    connectionCounts.set(entityKey, entity.Relationships.length)
  })

  // Sort entities by connection count
  const sortedEntities = [...entities].sort((a, b) => {
    const aKey = `${a.Schema}.${a.Name}`
    const bKey = `${b.Schema}.${b.Name}`
    return (connectionCounts.get(bKey) || 0) - (connectionCounts.get(aKey) || 0)
  })

  // Position entities in concentric circles with collision detection
  const center = { x: 800, y: 400 }
  const placedEntities: Array<{ position: { x: number; y: number }; size: { width: number; height: number } }> = []
  const newEntities: Entity[] = []

  sortedEntities.forEach((entity, index) => {
    const entitySize = getEntityDimensions(entity)

    if (index === 0) {
      // Most connected entity in center
      const position = { x: center.x - entitySize.width / 2, y: center.y - entitySize.height / 2 }
      placedEntities.push({ position, size: entitySize })
      newEntities.push({
        ...entity,
        position,
      })
    } else {
      // Arrange others in circles
      const ring = Math.ceil(Math.sqrt(index))
      const entitiesInRing = Math.max(6, Math.floor(2 * Math.PI * ring * 1.5)) // More entities per ring
      const angleStep = (2 * Math.PI) / entitiesInRing
      const angle = ((index - 1) % entitiesInRing) * angleStep
      const radius = ring * 300 + 200 // Increased radius for larger entities

      const preferredPosition = {
        x: center.x + Math.cos(angle) * radius - entitySize.width / 2,
        y: center.y + Math.sin(angle) * radius - entitySize.height / 2,
      }

      const finalPosition = findNonOverlappingPosition(preferredPosition, entitySize, placedEntities)
      placedEntities.push({ position: finalPosition, size: entitySize })

      newEntities.push({
        ...entity,
        position: finalPosition,
      })
    }
  })

  setEntities(newEntities)
}

export const arrangeCompact = (
  entities: Entity[],
  getEntityDimensions: (entity: Entity) => { width: number; height: number },
  setEntities: (entities: Entity[]) => void,
) => {
  const sortedEntities = [...entities].sort((a, b) => {
    const sizeA = getEntityDimensions(a)
    const sizeB = getEntityDimensions(b)
    return sizeB.width * sizeB.height - sizeA.width * sizeA.height
  })

  const placedEntities: Array<{ position: { x: number; y: number }; size: { width: number; height: number } }> = []
  const newEntities: Entity[] = []

  const totalEntities = sortedEntities.length
  const cols = Math.ceil(Math.sqrt(totalEntities * 1.2))

  let currentRow = 0
  let currentCol = 0
  let maxHeightInRow = 0
  let currentY = 100

  sortedEntities.forEach((entity) => {
    const entitySize = getEntityDimensions(entity)

    const baseX = currentCol * 500 + 100 // Increased spacing
    const baseY = currentY

    const preferredPosition = { x: baseX, y: baseY }
    const finalPosition = findNonOverlappingPosition(preferredPosition, entitySize, placedEntities, 50)

    placedEntities.push({ position: finalPosition, size: entitySize })
    maxHeightInRow = Math.max(maxHeightInRow, entitySize.height)

    newEntities.push({
      ...entity,
      position: finalPosition,
    })

    currentCol++
    if (currentCol >= cols) {
      currentCol = 0
      currentRow++
      currentY += maxHeightInRow + 80 // Increased row spacing
      maxHeightInRow = 0
    }
  })

  setEntities(newEntities)
}