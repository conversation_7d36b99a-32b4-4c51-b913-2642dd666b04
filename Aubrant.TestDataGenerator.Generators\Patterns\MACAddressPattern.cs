
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Patterns;

namespace Aubrant.TestDataGenerator.Generators.Patterns
{
    public class MACAddressPattern : BasePattern
    {
        public MACAddressPattern() : base(
            name: "MAC Address",
            description: "Standard 48-bit MAC address",
            pattern: "{H2}:{H2}:{H2}:{H2}:{H2}:{H2}",
            tokens: new List<Token>
            {
                new HexToken("H2", "Two-digit hex segment", 2, CaseOption.Upper)
            })
        { }
    }
}
