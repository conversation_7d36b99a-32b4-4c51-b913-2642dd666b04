using Aubrant.TestDataGenerator.Core;

namespace Aubrant.TestDataGenerator.Core.Utils.Models
{
    /// <summary>
    /// Defines the contract for applying a relative offset to a DataValue.
    /// </summary>
    public interface IOffsetCalculator
    {
        /// <summary>
        /// Applies a random offset to a base value within a specified range.
        /// </summary>
        /// <param name="baseValue">The base DataValue to which the offset is applied.</param>
        /// <param name="minOffset">The string representation of the minimum offset.</param>
        /// <param name="maxOffset">The string representation of the maximum offset.</param>
        /// <param name="random">The random number generator to use.</param>
        /// <returns>A new DataValue with the offset applied.</returns>
        DataValue ApplyOffset(DataValue baseValue, string minOffset, string maxOffset, Random random);
    }
}
