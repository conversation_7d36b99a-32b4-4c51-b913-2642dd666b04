using Aubrant.TestDataGenerator.Data;

namespace Aubrant.TestDataGenerator.SchemaProfiler.Extractors
{
    /// <summary>
    /// Defines the interface for data source schema extraction and sample data loading.
    /// </summary>
    public interface IDataSourceSchemaExtractor
    {
        /// <summary>
        /// Extracts the schema from a data source asynchronously.
        /// </summary>
        /// <param name="connectionString">The connection string or file path for the data source.</param>
        /// <param name="dataSourceName">The name to assign to the data source. If null, a name will be inferred.</param>
        /// <returns>A Task representing the asynchronous operation, returning the extracted DataSource schema.</returns>
        Task<DataSource> ExtractAsync(string connectionString, string dataSourceName);
        /// <summary>
        /// Loads sample data into the provided DataSource asynchronously.
        /// </summary>
        /// <param name="dataSource">The DataSource object to populate with sample data.</param>
        /// <param name="connectionString">The connection string or file path for the data source.</param>
        /// <param name="sampleSize">The number of sample rows to load for each entity.</param>
        /// <returns>A Task representing the asynchronous operation.</returns>
        Task LoadSampleDataAsync(DataSource dataSource, string connectionString, int? sampleSize = null);
    }
}