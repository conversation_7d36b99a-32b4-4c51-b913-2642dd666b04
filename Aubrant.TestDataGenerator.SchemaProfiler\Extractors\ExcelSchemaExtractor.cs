using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.Data.Enums;
using Aubrant.TestDataGenerator.SchemaProfiler.Common;
using Aubrant.TestDataGenerator.SchemaProfiler.Exceptions;
using Aubrant.TestDataGenerator.SchemaProfiler.Logging;
using ExcelDataReader;
using System.Data;

namespace Aubrant.TestDataGenerator.SchemaProfiler.Extractors
{
    /// <summary>
    /// Extracts schema and loads sample data from Excel files.
    /// </summary>
    public class ExcelSchemaExtractor : IDataSourceSchemaExtractor
    {
        private readonly int _sampleSize;
        private readonly ILogger _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="ExcelSchemaExtractor"/> class.
        /// </summary>
        /// <param name="options">The schema extraction options.</param>
        /// <param name="logger">Optional. An ILogger instance for logging. If null, a NullLogger will be used.</param>
        public ExcelSchemaExtractor(SchemaExtractionOptions? options = null, ILogger? logger = null)
        {
            System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);
            options = options ?? new SchemaExtractionOptions();
            _sampleSize = options.SampleSize;
            _logger = logger ?? new NullLogger();
            _logger.LogInfo($"ExcelSchemaExtractor initialized with SampleSize: {_sampleSize}");
        }

        /// <summary>
        /// Extracts the schema from an Excel file asynchronously.
        /// </summary>
        /// <param name="connectionString">The path to the Excel file.</param>
        /// <param name="dataSourceName">The name to assign to the data source.</param>
        /// <returns>A Task representing the asynchronous operation, returning the extracted DataSource schema.</returns>
        public async Task<DataSource> ExtractAsync(string connectionString, string? dataSourceName)
        {
            _logger.LogInfo($"Extracting schema from Excel: {connectionString}");
            var dataSource = new DataSource { Name = dataSourceName ?? Path.GetFileNameWithoutExtension(connectionString), Type = DataSourceType.File, Provider = DataSourceProvider.Excel, ConnectionString = connectionString };
            try
            {
                await Task.Run(() =>
                {
                    using (var stream = File.Open(connectionString, FileMode.Open, FileAccess.Read, FileShare.Read))
                    using (var reader = ExcelReaderFactory.CreateReader(stream))
                    {
                        do
                        {
                            if (reader.VisibleState != "visible" || reader.FieldCount == 0 || !reader.Read()) continue;
                            var entity = new Entity { Name = reader.Name };
                            var headers = new List<string>();
                            for (int i = 0; i < reader.FieldCount; i++) headers.Add(reader.GetValue(i)?.ToString() ?? $"Column_{i + 1}");

                            var sampleRecords = new List<object[]>();
                            int rowsRead = 0;
                            while (rowsRead < _sampleSize && reader.Read())
                            {
                                var rowData = new object[headers.Count];
                                for (int i = 0; i < headers.Count; i++) rowData[i] = reader.GetValue(i);
                                sampleRecords.Add(rowData);
                                rowsRead++;
                            }

                            if (sampleRecords.Any())
                            {
                                InferFields(entity, headers, sampleRecords);
                                dataSource.Entities.Add(entity);
                            }
                        } while (reader.NextResult());
                    }
                });
                _logger.LogInfo($"Schema extracted for {dataSource.Name}");
                return dataSource; // Explicit return here
            }
            catch (FileNotFoundException ex)
            {
                _logger.LogError($"The Excel file was not found at '{connectionString}'.", ex);
                throw new InvalidDataSourceException($"The Excel file was not found at '{connectionString}'.", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError($"An error occurred while reading the Excel file '{connectionString}'.", ex);
                throw new InvalidDataSourceException($"An error occurred while reading the Excel file '{connectionString}'.", ex);
            }
        }

        /// <summary>
        /// Loads sample data into the provided DataSource from an Excel file asynchronously.
        /// </summary>
        /// <param name="dataSource">The DataSource object to populate with sample data.</param>
        /// <param name="connectionString">The path to the Excel file.</param>
        /// <param name="sampleSize">The number of sample rows to load for each entity.</param>
        /// <returns>A Task representing the asynchronous operation.</returns>
        public async Task LoadSampleDataAsync(DataSource dataSource, string connectionString, int? sampleSize = null)
        {
            sampleSize = sampleSize ?? _sampleSize;
            _logger.LogInfo($"Loading sample data for Excel: {dataSource.Name} (sample size: {sampleSize})");
            try
            {
                await Task.Run(() =>
                {
                    using (var stream = File.Open(connectionString, FileMode.Open, FileAccess.Read, FileShare.Read))
                    using (var reader = ExcelReaderFactory.CreateReader(stream))
                    {
                        var entityDict = dataSource.Entities.ToDictionary(e => e.Name ?? string.Empty);
                        do
                        {
                            if (!entityDict.TryGetValue(reader.Name ?? string.Empty, out var entity) || !reader.Read()) continue;
                            entity.Data = new DataTable(entity.Name);
                            foreach (var field in entity.Fields) entity.Data.Columns.Add(new DataColumn(field.Name, DataTypeMapper.ToSystemType(field.Type)));

                            int rowsLoaded = 0;
                            while (rowsLoaded < sampleSize && reader.Read())
                            {
                                var dataRow = entity.Data.NewRow();
                                for (int i = 0; i < entity.Fields.Count; i++)
                                {
                                    var field = entity.Fields[i];
                                    dataRow[field.Name!] = ConvertValue(i < reader.FieldCount ? reader.GetValue(i) : null, field.Type);
                                }
                                entity.Data.Rows.Add(dataRow);
                                rowsLoaded++;
                            }
                        } while (reader.NextResult());
                    }
                });
                _logger.LogInfo($"Sample data loaded for {dataSource.Name}");
            }
            catch (FileNotFoundException ex)
            {
                _logger.LogError($"The Excel file was not found at '{connectionString}'.", ex);
                throw new InvalidDataSourceException($"The Excel file was not found at '{connectionString}'.", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError($"An error occurred while loading data from the Excel file '{connectionString}'.", ex);
                throw new InvalidDataSourceException($"An error occurred while loading data from the Excel file '{connectionString}'.", ex);
            }
        }

        /// <summary>
        /// Infers fields and primary keys for an entity based on sample Excel records.
        /// </summary>
        /// <param name="entity">The entity to populate with inferred fields.</param>
        /// <param name="headers">The column headers from the Excel sheet.</param>
        /// <param name="sampleRecords">Sample data records from the Excel sheet.</param>
        private void InferFields(Entity entity, List<string> headers, List<object[]> sampleRecords)
        {
            _logger.LogInfo($"Inferring fields and primary keys for entity {entity.Name}");
            for (int i = 0; i < headers.Count; i++)
            {
                var field = new Field { Name = headers[i], IsNullable = false };
                (DataType inferredType, bool isNullable) = TypeInferrer.InferColumnType(sampleRecords, i);
                field.Type = inferredType;
                field.NativeType = "ExcelGeneral";
                field.IsNullable = isNullable;

                if (field.Name!.EndsWith("Id", StringComparison.OrdinalIgnoreCase))
                {
                    var nonNullValues = sampleRecords.Select(row => row[i]).Where(v => v != null && v != DBNull.Value).ToList();
                    bool isUnique = nonNullValues.Distinct().Count() == nonNullValues.Count;
                    field.IsPrimaryKey = isUnique;
                    if (isUnique)
                    {
                        field.IsIdentity = TypeInferrer.IsSequential(sampleRecords, i);
                        _logger.LogInfo($"Inferred primary key: {field.Name} (IsIdentity: {field.IsIdentity})");
                        entity.KeyConstraints.Add(new KeyConstraint
                        {
                            KeyType = KeyType.Primary,
                            Columns = new List<string> { field.Name }
                        });
                    }
                }
                entity.Fields.Add(field);
            }
        }

        

        /// <summary>
        /// Converts an object value to the specified DataType.
        /// </summary>
        /// <param name="value">The object value to convert.</param>
        /// <param name="dataType">The target DataType.</param>
        /// <returns>The converted object, or DBNull.Value if conversion fails or value is null/empty.</returns>
        private object ConvertValue(object? value, DataType dataType)
        {
            if (value == null || value == DBNull.Value) return DBNull.Value;
            try
            {
                return dataType switch
                {
                    DataType.Integer => Convert.ToInt64(value),
                    DataType.Decimal => Convert.ToDecimal(value),
                    DataType.Boolean => Convert.ToBoolean(value),
                    DataType.DateTime => Convert.ToDateTime(value),
                    _ => value.ToString()
                };
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Failed to convert Excel value '{value}' to DataType.{dataType}. Error: {ex.Message}");
                return DBNull.Value;
            }
        }
    }
}