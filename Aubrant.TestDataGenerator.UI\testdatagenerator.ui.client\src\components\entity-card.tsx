import type React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckSquare, Clock, Database, Hash, Key, Type } from "lucide-react"
import type { Entity, Field } from "../types/database";

interface EntityCardProps {
    entity: Entity
    isSelected: boolean
    isActive: boolean
    selectedField: Field | null
    onEntityClick: (entity: Entity, e: React.MouseEvent) => void
    onFieldClick: (field: Field, entity: Entity) => void
    onMouseDown: (entityName: string, e: React.MouseEvent) => void
}

export const EntityCard: React.FC<EntityCardProps> = ({
    entity,
    isSelected,
    isActive,
    selectedField,
    onEntityClick,
    onFieldClick,
    onMouseDown,
}) => {
    const getFieldIcon = (field: Field) => {
        if (field.IsPrimaryKey) return <Key className="w-3 h-3 text-yellow-700" />
        if (field.Type === "String") return <Type className="w-3 h-3 text-blue-700" />
        if (field.Type === "Integer" || field.Type === "Decimal") return <Hash className="w-3 h-3 text-green-700" />
        if (field.Type === "DateTime") return <Clock className="w-3 h-3 text-orange-700" />
        if (field.Type === "Boolean") return <CheckSquare className="w-3 h-3 text-purple-700" />
        return <Database className="w-3 h-3 text-gray-700" />
    }

    return (
        <Card
            className={`absolute w-80 cursor-move shadow-lg border-2 z-10 select-none ${isSelected ? "border-primary" : "border-border"} ${isActive ? "ring-2 ring-blue-500" : ""}
                }`}
            style={{
                left: entity.position?.x || 0,
                top: entity.position?.y || 0,
            }}
            onClick={(e) => onEntityClick(entity, e)}
        >
            <CardHeader
                className={`pb-2 transition-colors select-none ${isActive
                        ? "bg-blue-100 dark:bg-blue-900/30"
                        : "bg-slate-50 dark:bg-slate-800/50 hover:bg-slate-100 dark:hover:bg-slate-800"
                    }`}
                onMouseDown={(e) => onMouseDown(entity.Name, e)}
            >
                <CardTitle className="text-lg flex items-center gap-2">
                    <Database className="w-5 h-5" />
                    {entity.Schema ? `${entity.Schema}.${entity.Name}` : entity.Name}
                </CardTitle>
                {entity.dataSourceName && (
                    <p className="text-xs text-muted-foreground -mt-1">
                        {entity.dataSourceName}
                    </p>
                )}
            </CardHeader>
            <CardContent className="pt-0">
                <div className="space-y-1">
                    {entity.Fields.map((field) => (
                        <div
                            key={field.Name}
                            className={`flex items-center gap-2 p-1 rounded text-sm cursor-pointer hover:bg-muted/50 ${selectedField?.Name === field.Name && isActive ? "bg-primary/10" : ""
                                }`}
                            onClick={(e) => {
                                e.stopPropagation()
                                onFieldClick(field, entity)
                            }}
                        >
                            {getFieldIcon(field)}
                            <span className={field.IsPrimaryKey ? "font-semibold" : ""}>{field.Name}</span>
                            <span className="text-muted-foreground">
                                {field.Type || field.NativeType}
                                {field.MaxLength && `(${field.MaxLength})`}
                            </span>
                            <div className="flex gap-1 ml-auto">
                                {field.IsPrimaryKey && (
                                    <Badge variant="secondary" className="text-xs">
                                        PK
                                    </Badge>
                                )}
                                {field.IsUnique && (
                                    <Badge variant="outline" className="text-xs">
                                        UQ
                                    </Badge>
                                )}
                                {field.IsNullable && (
                                    <Badge variant="outline" className="text-xs">
                                        NULL
                                    </Badge>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            </CardContent>
        </Card>
    )
}
