using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.UI.Server.GraphQL;

namespace Aubrant.TestDataGenerator.UI.Server.Mutations
{
    [ExtendObjectType(typeof(Mutation))]
    public class ProjectMutations
    {
        public async Task<Project> CreateProject(string name, string? description, string? author, [Service] DatabaseContext context)
        {
            var project = new Project
            {
                Name = name,
                Description = description,
                Author = author,
                CreatedDate = System.DateTime.UtcNow
            };

            context.Projects.Add(project);
            await context.SaveChangesAsync();

            return project;
        }

        public async Task<Project> UpdateProject(int id, string? name, string? description, string? author, [Service] DatabaseContext context)
        {
            var project = await context.Projects.FindAsync(id);

            if (project == null)
            {
                throw new System.Exception("Project not found.");
            }

            if (name != null)
            {
                project.Name = name;
            }

            if (description != null)
            {
                project.Description = description;
            }

            if (author != null)
            {
                project.Author = author;
            }

            await context.SaveChangesAsync();

            return project;
        }

        public async Task<bool> DeleteProject(int id, [Service] DatabaseContext context)
        {
            var project = await context.Projects.FindAsync(id);

            if (project == null)
            {
                return false;
            }

            context.Projects.Remove(project);
            await context.SaveChangesAsync();

            return true;
        }
    }
}
