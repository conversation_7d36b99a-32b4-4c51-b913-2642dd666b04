using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Interfaces;
using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.Data.Enums;
using Aubrant.TestDataGenerator.Generators;
using Aubrant.TestDataGenerator.Generators.Functions;
using Aubrant.TestDataGenerator.Generators.Patterns;
using Aubrant.TestDataGenerator.Generators.Scripts;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Text.Json;

namespace Aubrant.TestDataGenerator.Tests
{
    public class DataGenerators
    {
        private readonly DataSet data = new DataSet();
        private Project? project;

        private AIProvider AiProvider = AIProvider.Google; // Options: Google, Ollama, Mistral
        private string AiModel = "gemini-2.5-flash-lite";
        private string AiApiKey = "AIzaSyCH1WoqEbjsJCX1I6ccE1haEl-msQX4mvk";
        private int numberOfRecords = 3;
        private bool _enableAIGeneration = false;

        public DataGenerators()
        {
            // Constructor for the test class. No special initialization needed here for now.
        }

        private async Task LoadProjectData()
        {
            using (var db = new DatabaseContext())
            {
                project = await db.Projects
                    .Include(p => p.DataSources)
                        .ThenInclude(ds => ds.Entities)
                            .ThenInclude(e => e.Fields)
                                .ThenInclude(f => f.FieldSetting)
                    .FirstOrDefaultAsync(p => p.Name == "Test Data Generator - Project 1");

                Assert.NotNull(project);
                Assert.NotEmpty(project.DataSources);
                Assert.All(project.DataSources, ds => Assert.NotEmpty(ds.Entities));
                Assert.All(project.DataSources.SelectMany(ds => ds.Entities), e => Assert.NotEmpty(e.Fields));
                Assert.All(project.DataSources.SelectMany(ds => ds.Entities).SelectMany(e => e.Fields), f => Assert.NotNull(f.FieldSetting));
            }
        }

        public async Task CreateDataSet()
        {
            Assert.NotNull(project);

            foreach (var dataSource in project.DataSources)
            {
                foreach (var entity in dataSource.Entities)
                {
                    var table = new DataTable(entity.Name, entity.Schema);
                    var primaryKeys = new List<DataColumn>();

                    foreach (var field in entity.Fields)
                    {
                        var column = new DataColumn(field.Name)
                        {
                            DataType = MapToSystemType(field.Type),
                            AllowDBNull = field.IsNullable,
                            Unique = field.IsUnique,
                            AutoIncrement = field.IsIdentity,
                        };

                        if (field.Type == DataType.String && field.MaxLength.HasValue && field.MaxLength > 0)
                        {
                            column.MaxLength = field.MaxLength.Value;
                        }

                        if (field.IsPrimaryKey)
                        {
                            primaryKeys.Add(column);
                        }

                        table.Columns.Add(column);
                    }

                    if (primaryKeys.Count > 0)
                    {
                        table.PrimaryKey = primaryKeys.ToArray();
                    }

                    entity.Data = table;
                    data.Tables.Add(table);
                }
            }
        }

        private Type MapToSystemType(DataType dataType)
        {
            return dataType switch
            {
                DataType.Integer => typeof(long),
                DataType.Decimal => typeof(decimal),
                DataType.DateTime => typeof(DateTime),
                DataType.Boolean => typeof(bool),
                DataType.String => typeof(string),
                DataType.Binary => typeof(byte[]),
                _ => typeof(string),
            };
        }

        public async Task ConfigureDataGenerators()
        {
            // Use the class-level 'project' field
            Assert.NotNull(project);

            foreach (var field in project.DataSources.SelectMany(ds => ds.Entities).SelectMany(e => e.Fields))
            {
                if (field.FieldSetting != null && field.FieldSetting.Type != GeneratorType.None)
                {
                    var settings = string.IsNullOrEmpty(field.FieldSetting.Settings)
                        ? new Dictionary<string, object>()
                        : JsonSerializer.Deserialize<Dictionary<string, object>>(field.FieldSetting.Settings);

                    field.DataGenerator = CreateGenerator(field.FieldSetting, settings);
                    Assert.NotNull(field.DataGenerator);
                }
            }

            // Assertions to ensure generators are correctly configured
            var employeeIdField = project.DataSources.SelectMany(ds => ds.Entities).SelectMany(e => e.Fields)
                .FirstOrDefault(f => f.Entity.Name == "Employee" && f.Name == "Id");

            Assert.NotNull(employeeIdField);
            Assert.NotNull(employeeIdField.DataGenerator);
            Assert.IsType<AutoIncrementGenerator>(employeeIdField.DataGenerator);

            var titleOfCourtesyField = project.DataSources.SelectMany(ds => ds.Entities).SelectMany(e => e.Fields)
                .FirstOrDefault(f => f.Entity.Name == "Employee" && f.Name == "TitleOfCourtesy");

            Assert.NotNull(titleOfCourtesyField);
            Assert.NotNull(titleOfCourtesyField.DataGenerator);
            Assert.IsType<ChoiceGenerator>(titleOfCourtesyField.DataGenerator);
            Assert.True(titleOfCourtesyField.DataGenerator is ChoiceGenerator choiceGenerator && choiceGenerator.Options.Any(), "ChoiceGenerator should have options defined.");
        }

        private IGenerator CreateGenerator(FieldSetting fieldSetting, Dictionary<string, object> settings)
        {
            IGenerator generator = fieldSetting.Type switch
            {
                GeneratorType.AutoIncrement => new AutoIncrementGenerator(),
                GeneratorType.Random => new RandomGenerator(),
                GeneratorType.AI => _enableAIGeneration ? new AIGenerator() : new ConstantGenerator { Value = new DataValue("[AI Generation Disabled]") },
                GeneratorType.Script => new ScriptGenerator(),
                GeneratorType.Choice => new ChoiceGenerator(),
                GeneratorType.Constant => new ConstantGenerator(),
                GeneratorType.Relative => new RelativeGenerator(),
                GeneratorType.Pattern => CreatePatternGenerator(fieldSetting, settings),
                GeneratorType.Function => CreateFunctionGenerator(fieldSetting, settings),
                _ => new ConstantGenerator { Value = new DataValue($"[{fieldSetting.Type} not implemented]") },
            };

            // RuntimeFunction is configured via its constructor, so we skip Parse.
            // Other generators, including registered functions returned by CreateFunctionGenerator, use Parse.
            if (generator is not RuntimeFunction)
            {
                generator.Parse(fieldSetting.Field.Type, settings);
            }

            return generator;
        }

        private IGenerator CreatePatternGenerator(FieldSetting fieldSetting, Dictionary<string, object> settings)
        {
            if (!string.IsNullOrEmpty(fieldSetting.Name) && PatternRegistry.TryCreatePattern(fieldSetting.Name, out var patternInstance))
            {
                if (patternInstance is IGenerator generator)
                {
                    generator.Parse(DataType.String, settings);
                }
                return patternInstance;
            }
            else
            {
                var runtimePattern = new RuntimePattern();
                runtimePattern.Parse(DataType.String, settings);
                return runtimePattern;
            }
        }

        private IGenerator CreateFunctionGenerator(FieldSetting fieldSetting, Dictionary<string, object> settings)
        {
            if (!string.IsNullOrEmpty(fieldSetting.Name) && fieldSetting.Name != "Runtime" && FunctionRegistry.TryCreateFunction(fieldSetting.Name, new DataGeneratorContext(), out var functionInstance))
            {
                return functionInstance;
            }
            else
            {
                settings.TryGetValue("Type", out var typeObj);
                Enum.TryParse<RuntimeType>((typeObj as JsonElement?)?.GetString(), true, out var type);

                settings.TryGetValue("Script", out var scriptObj);
                var script = (scriptObj as JsonElement?)?.GetString();

                settings.TryGetValue("Prompt", out var promptObj);
                var prompt = (promptObj as JsonElement?)?.GetString();

                settings.TryGetValue("AIProvider", out var providerObj);
                Enum.TryParse<AIProvider>((providerObj as JsonElement?)?.GetString(), true, out var aiProvider);

                settings.TryGetValue("AIModel", out var modelObj);
                var aiModel = (modelObj as JsonElement?)?.GetString();

                settings.TryGetValue("APIKey", out var keyObj);
                var apiKey = (keyObj as JsonElement?)?.GetString();

                return new RuntimeFunction(type, script, prompt, aiProvider, aiModel, apiKey);
            }
        }

        [Fact]
        public async Task GenerateAndSaveData()
        {
            await LoadProjectData();
            await CreateDataSet();
            await ConfigureDataGenerators();

            // Use the class-level 'project' field
            Assert.NotNull(project);

            foreach (var entity in project.DataSources.SelectMany(ds => ds.Entities))
            {
                var context = new DataGeneratorContext { Random = new Random() };

                for (int i = 0; i < numberOfRecords; i++)
                {
                    var row = entity.Data.NewRow();
                    context.RowIndex = i;
                    context.Row = row;

                    foreach (var field in entity.Fields)
                    {
                        try
                        {
                            if (field.DataGenerator != null)
                            {
                                DataValue generatedValue = field.DataGenerator switch
                                {
                                    ICompiledGenerator compiledGenerator => compiledGenerator.Generate(),
                                    IRuntimeGenerator runtimeGenerator => runtimeGenerator.Generate(context),
                                    _ => new DataValue(DataType.String, "Error: Unknown generator type"),
                                };
                                row[field.Name] = generatedValue.Value;
                            }
                        }
                        catch
                        {
                            // Logging can be added here
                        }

                    }
                    entity.Data.Rows.Add(row);
                }
            }

            var employeeTable = data.Tables["Employee"];
            Assert.NotNull(employeeTable);
            Assert.Equal(numberOfRecords, employeeTable.Rows.Count);
        }
    }
}