﻿using System.Text.Json;

namespace Aubrant.TestDataGenerator.Generators.Scripts
{
    public static class ScriptExtensions
    {
        public static string ToJsonString<T>(this T[] array) => JsonSerializer.Serialize(array, options: new JsonSerializerOptions { DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.Never });

        public static string ToJsonString(this object obj) => JsonSerializer.Serialize(obj);

        public static string RandomElement<T>(this IEnumerable<T> elements)
        {
            var array = elements.ToArray();
            if (array.Length == 0) return string.Empty;
            var random = new Random();
            return array[random.Next(array.Length)].ToString();
        }
    }
}
