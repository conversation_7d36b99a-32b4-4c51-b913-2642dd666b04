using Bogus;
using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Functions;

namespace Aubrant.TestDataGenerator.Generators.Functions
{
    /// <summary>
    /// A generator function that produces realistic street addresses based on the specified country, optional state/city/postal code, and output locale.
    /// </summary>
    public class AddressFunction : BaseFunction
    {
        public override string Name => "Address";
        public override string Description => "Generates a realistic street address based on the specified country, optional state/city/postal code, and output locale.";
        public override DataType ReturnType => DataType.String;

        /// <summary>
        /// Initializes a new instance of the AddressFunction class.
        /// Defines 'country', 'locale', and optional 'state', 'city', 'postalCode' parameters.
        /// </summary>
        public AddressFunction() : base("Location")
        {
            Parameters.Add(new Parameter("country", "The country to generate the address in.", DataType.String, new DataValue("US"))); // Default country is US
            Parameters.Add(new Parameter("locale", "The output locale.", DataType.String, new DataValue("en")));  // Default output locale is English
            Parameters.Add(new Parameter("state", "The state to generate the address in.", DataType.String, null)); // Optional state parameter
            Parameters.Add(new Parameter("city", "The city to generate the address in.", DataType.String, null)); // Optional city parameter
            Parameters.Add(new Parameter("postalCode", "The postal code to generate the address in.", DataType.String, null)); // Optional postalCode parameter
        }

        /// <summary>
        /// Generates a random street address using the Bogus library, considering the specified country, optional state/city/postal code, and output locale.
        /// </summary>
        /// <returns>A DataValue containing the generated address as a string.</returns>
        /// <exception cref="InvalidOperationException">Thrown if any parameter is missing or invalid.</exception>
        public override DataValue Generate(DataGeneratorContext context)
        {
            string countryInput = Parameters["country"].Value?.ToString() ?? throw new InvalidOperationException("Country parameter is missing or invalid.");
            string outputLocale = Parameters["locale"].Value?.ToString() ?? throw new InvalidOperationException("Locale parameter is missing or invalid.");
            string? stateInput = Parameters["state"].Value?.ToString();
            string? cityInput = Parameters["city"].Value?.ToString();
            string? postalCodeInput = Parameters["postalCode"].Value?.ToString();

            // Bogus.NET's Address.FullAddress() or similar methods are generally locale-dependent.
            // We'll use the outputLocale for the Faker instance.
            var faker = new Faker(outputLocale);

            // While Bogus.NET can generate addresses, it doesn't easily allow constraining by specific state/city/postal code
            // from our curated data without custom logic. For now, we'll rely on Bogus's general address generation
            // based on the outputLocale, which is influenced by the country's address formats.
            // If more precise control is needed, we'd have to build custom address generation logic
            // that combines street names, house numbers, etc., from our GeoDataRepository.

            try
            {
                // Bogus.NET's Address.FullAddress() provides a complete address.
                // We can't directly use our GeoDataRepository for full address generation
                // without significant custom logic to combine parts.
                string address = faker.Address.FullAddress();
                return new DataValue(DataType.String, address);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error generating address for country '{countryInput}' with locale '{outputLocale}': {ex.Message}", ex);
            }
        }
    }
}
