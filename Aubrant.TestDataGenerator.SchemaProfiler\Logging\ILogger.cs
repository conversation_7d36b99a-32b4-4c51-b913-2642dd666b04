using Aubrant.TestDataGenerator.SchemaProfiler.Enums;

namespace Aubrant.TestDataGenerator.SchemaProfiler.Logging
{
    /// <summary>
    /// Defines the interface for logging messages with different verbosity levels.
    /// </summary>
    public interface ILogger
    {
        /// <summary>
        /// Gets the logging levels that are currently verbose (will be outputted).
        /// </summary>
        LogLevel VerboseLogLevels { get; }
        /// <summary>
        /// Logs an informational message.
        /// </summary>
        /// <param name="message">The message to log.</param>
        void LogInfo(string message);
        /// <summary>
        /// Logs a warning message.
        /// </summary>
        /// <param name="message">The message to log.</param>
        void LogWarning(string message);
        /// <summary>
        /// Logs an error message, optionally with an exception.
        /// </summary>
        /// <param name="message">The message to log.</param>
        /// <param name="ex">The exception to log (optional).</param>
        void LogError(string message, Exception? ex = null);
    }
}