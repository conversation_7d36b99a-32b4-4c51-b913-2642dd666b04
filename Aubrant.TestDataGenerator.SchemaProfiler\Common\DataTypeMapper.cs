using Aubrant.TestDataGenerator.Core.Enums;

namespace Aubrant.TestDataGenerator.SchemaProfiler.Common
{
    /// <summary>
    /// Provides utility methods for mapping custom DataType enum to .NET System.Type.
    /// </summary>
    public static class DataTypeMapper
    {
        /// <summary>
        /// Converts a custom DataType enum value to its corresponding .NET System.Type.
        /// </summary>
        /// <param name="dataType">The custom DataType enum value.</param>
        /// <returns>The corresponding .NET System.Type.</returns>
        public static Type ToSystemType(DataType dataType)
        {
            return dataType switch
            {
                DataType.Integer => typeof(long),
                DataType.Decimal => typeof(decimal),
                DataType.Boolean => typeof(bool),
                DataType.DateTime => typeof(DateTime),
                DataType.Binary => typeof(byte[]),
                _ => typeof(string)
            };
        }
    }
}