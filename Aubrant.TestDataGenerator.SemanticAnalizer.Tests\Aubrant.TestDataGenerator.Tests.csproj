﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<IsPackable>false</IsPackable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="coverlet.collector" Version="6.0.4">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="LINQPad.Runtime" Version="8.3.7" />
		<PackageReference Include="Microsoft.Data.Sqlite.Core" Version="9.0.8" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
		<PackageReference Include="SQLitePCLRaw.bundle_green" Version="2.1.11" />
		<PackageReference Include="SQLitePCLRaw.core" Version="3.0.1" />
		<PackageReference Include="xunit" Version="2.9.3" />
		<PackageReference Include="xunit.runner.visualstudio" Version="3.1.3">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
		<Using Include="Xunit" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Aubrant.TestDataGenerator.SemanticAnalizer\Aubrant.TestDataGenerator.SemanticAnalizer.csproj" />
		<ProjectReference Include="..\Aubrant.TestDataGenerator.SchemaProfiler\Aubrant.TestDataGenerator.SchemaProfiler.csproj" />
		<ProjectReference Include="..\Aubrant.TestDataGenerator.Data\Aubrant.TestDataGenerator.Data.csproj" />
	</ItemGroup>

</Project>