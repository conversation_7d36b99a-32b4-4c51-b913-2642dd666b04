using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.Data.Enums;
using Aubrant.TestDataGenerator.SchemaProfiler.Common;
using Aubrant.TestDataGenerator.SchemaProfiler.Logging;
using MongoDB.Bson;
using MongoDB.Driver;
using System.Data;

namespace Aubrant.TestDataGenerator.SchemaProfiler.Extractors
{
    /// <summary>
    /// Extracts schema and loads sample data from MongoDB databases.
    /// </summary>
    public class MongoDbSchemaExtractor : IDataSourceSchemaExtractor
    {
        private readonly ILogger _logger;
        private readonly int _sampleSize;

        /// <summary>
        /// Initializes a new instance of the <see cref="MongoDbSchemaExtractor"/> class.
        /// </summary>
        /// <param name="logger">Optional. An ILogger instance for logging. If null, a NullLogger will be used.</param>
        public MongoDbSchemaExtractor(SchemaExtractionOptions? options = null, ILogger? logger = null)
        {
            options = options ?? new SchemaExtractionOptions();
            _sampleSize = options.SampleSize;
            _logger = logger ?? new NullLogger();
            _logger.LogInfo($"MongoDbSchemaExtractor initialized with SampleSize: {_sampleSize}");
        }

        /// <summary>
        /// Extracts the schema from a MongoDB database asynchronously.
        /// </summary>
        /// <param name="connectionString">The connection string for the MongoDB database.</param>
        /// <param name="dataSourceName">The name of the database to extract schema from.</param>
        /// <returns>A Task representing the asynchronous operation, returning the extracted DataSource schema.</returns>
        public async Task<DataSource> ExtractAsync(string connectionString, string? dataSourceName = null)
        {
            _logger.LogInfo($"Extracting schema from MongoDB: {connectionString}");
            var dataSource = new DataSource { Name = dataSourceName, Type = DataSourceType.NoSQL, Provider = DataSourceProvider.MongoDB, ConnectionString = connectionString };
            try
            {
                var client = new MongoClient(connectionString);
                var database = client.GetDatabase(dataSourceName);
                _logger.LogInfo($"Connected to MongoDB database: {database.DatabaseNamespace.DatabaseName}");
                var collections = await database.ListCollectionNames().ToListAsync();
                _logger.LogInfo($"Found {collections.Count} collections.");

                // 1. FIRST PASS: Basic schema extraction
                foreach (var collectionName in collections)
                {
                    _logger.LogInfo($"Extracting schema for collection: {collectionName}");
                    var entity = new Entity { Name = collectionName };
                    var collection = database.GetCollection<BsonDocument>(collectionName);
                    var documents = await collection.Find(Builders<BsonDocument>.Filter.Empty).Limit(100).ToListAsync();

                    if (documents.Any())
                    {
                        var fieldData = new Dictionary<string, (List<BsonType> types, bool isNullable, List<int> lengths)>();
                        foreach (var doc in documents)
                        {
                            foreach (var element in doc.Elements)
                            {
                                if (!fieldData.ContainsKey(element.Name)) fieldData[element.Name] = (new List<BsonType>(), false, new List<int>());
                                fieldData[element.Name].types.Add(element.Value.BsonType);
                                if (element.Value.IsBsonNull) fieldData[element.Name] = (fieldData[element.Name].types, true, fieldData[element.Name].lengths);
                                if (element.Value.IsString) fieldData[element.Name].lengths.Add(element.Value.AsString.Length);
                            }
                        }

                        foreach (var (fieldName, data) in fieldData)
                        {
                            var distinctTypes = data.types.Distinct().ToList();
                            var finalType = distinctTypes.Count > 1 ? (distinctTypes.Any(t => t == BsonType.String) ? BsonType.String : distinctTypes.First(t => t != BsonType.Null)) : distinctTypes.FirstOrDefault();
                            entity.Fields.Add(new Field { Name = fieldName, Type = MapBsonTypeToDataType(finalType), NativeType = finalType.ToString(), IsNullable = data.isNullable, IsPrimaryKey = fieldName == "_id", IsIdentity = fieldName == "_id", MaxLength = data.lengths.Any() ? data.lengths.Max() : null });
                        }

                        if (entity.Fields.Any(f => f.IsPrimaryKey)) entity.KeyConstraints.Add(new KeyConstraint { KeyType = KeyType.Primary, Columns = new List<string> { "_id" } });
                    }
                    dataSource.Entities.Add(entity);
                }

                // 2. SECOND PASS: Relationship inference
                _logger.LogInfo("Starting relationship inference phase...");
                var entitiesDict = dataSource.Entities.ToDictionary(e => e.Name ?? string.Empty, StringComparer.OrdinalIgnoreCase);

                foreach (var sourceEntity in dataSource.Entities)
                {
                    foreach (var sourceField in sourceEntity.Fields)
                    {
                        if (sourceField.Name == null || !sourceField.Name.EndsWith("_id", StringComparison.OrdinalIgnoreCase) || sourceField.Name.Equals("_id", StringComparison.OrdinalIgnoreCase))
                        {
                            continue;
                        }

                        string potentialTargetName = sourceField.Name.Substring(0, sourceField.Name.Length - 3);
                        var targetEntity = entitiesDict.Values.FirstOrDefault(e =>
                            e.Name != null && (e.Name.Equals(potentialTargetName, StringComparison.OrdinalIgnoreCase) ||
                            e.Name.Equals(potentialTargetName + "s", StringComparison.OrdinalIgnoreCase)));

                        if (targetEntity != null)
                        {
                            var targetPkField = targetEntity.Fields.FirstOrDefault(f => f.IsPrimaryKey);
                            if (targetPkField == null) continue;

                            if (sourceField.NativeType == targetPkField.NativeType)
                            {
                                _logger.LogInfo($"Type match found for potential relationship: {sourceEntity.Name}.{sourceField.Name} ({sourceField.NativeType}) -> {targetEntity.Name}.{targetPkField.Name} ({targetPkField.NativeType}). Validating with data...");

                                var sourceCollection = database.GetCollection<BsonDocument>(sourceEntity.Name);
                                var sampleIds = await sourceCollection.Find(new BsonDocument())
                                                                .Project(Builders<BsonDocument>.Projection.Include(sourceField.Name))
                                                                .Limit(10)
                                                                .ToListAsync();

                                var valuesToValidate = sampleIds.Where(doc => doc.Contains(sourceField.Name) && !doc[sourceField.Name].IsBsonNull)
                                                                .Select(doc => doc[sourceField.Name])
                                                                .ToList();

                                if (valuesToValidate.Any())
                                {
                                    var targetCollection = database.GetCollection<BsonDocument>(targetEntity.Name);
                                    var filter = Builders<BsonDocument>.Filter.In(targetPkField.Name, valuesToValidate);
                                    long matchCount = await targetCollection.CountDocumentsAsync(filter);

                                    if (matchCount > 0)
                                    {
                                        _logger.LogInfo($"Relationship CONFIRMED between {sourceEntity.Name} and {targetEntity.Name}");
                                        
                                        sourceEntity.Relationships.Add(new Relationship
                                        {
                                            SourceEntityId = sourceEntity.Id,
                                            TargetEntity = targetEntity.Name,
                                            SourceField = sourceField.Name,
                                            TargetField = "_id",
                                            Cardinality = Cardinality.ManyToOne
                                        });
                                        
                                        sourceEntity.KeyConstraints.Add(new KeyConstraint
                                        {
                                            KeyType = KeyType.Foreign,
                                            Columns = new List<string> { sourceField.Name },
                                            ReferencedEntity = targetEntity.Name,
                                            ReferencedColumns = new List<string> { "_id" }
                                        });

                                        targetEntity.Relationships.Add(new Relationship
                                        {
                                            SourceEntityId = targetEntity.Id,
                                            TargetEntity = sourceEntity.Name,
                                            SourceField = "_id",
                                            TargetField = sourceField.Name,
                                            Cardinality = Cardinality.OneToMany
                                        });
                                    }
                                }
                            }
                        }
                    }
                }
                _logger.LogInfo($"Schema extracted for {dataSource.Name}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error extracting schema from MongoDB database {connectionString}", ex);
                throw;
            }
            
            return dataSource;
        }

        /// <summary>
        /// Loads sample data into the provided DataSource from a MongoDB database asynchronously.
        /// </summary>
        /// <param name="dataSource">The DataSource object to populate with sample data.</param>
        /// <param name="connectionString">The connection string for the MongoDB database.</param>
        /// <param name="sampleSize">The number of sample documents to load for each entity.</param>
        /// <returns>A Task representing the asynchronous operation.</returns>
        public async Task LoadSampleDataAsync(DataSource dataSource, string connectionString, int? sampleSize = null)
        {
            sampleSize = sampleSize ?? _sampleSize;
            _logger.LogInfo($"Loading sample data for MongoDB: {dataSource.Name} (sample size: {sampleSize})");
            try
            {
                var client = new MongoClient(connectionString);
                var database = client.GetDatabase(dataSource.Name);
                foreach (var entity in dataSource.Entities)
                {
                    _logger.LogInfo($"Loading sample data for entity: {entity.Name}");
                    var collection = database.GetCollection<BsonDocument>(entity.Name);
                    var documents = await collection.Find(Builders<BsonDocument>.Filter.Empty).Limit(sampleSize.Value).ToListAsync();
                    entity.Data = new DataTable(entity.Name);
                    foreach (var field in entity.Fields)
                    {
                        entity.Data.Columns.Add(new DataColumn(field.Name!, DataTypeMapper.ToSystemType(field.Type)));
                    }
                    foreach (var doc in documents)
                    {
                        var row = entity.Data.NewRow();
                        foreach (var field in entity.Fields)
                        {
                            row[field.Name!] = doc.Contains(field.Name!) && !doc[field.Name!].IsBsonNull ? ConvertBsonValue(doc[field.Name!], field.Type) : DBNull.Value;
                        }
                        entity.Data.Rows.Add(row);
                    }
                }
                _logger.LogInfo($"Sample data loaded for {dataSource.Name}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error loading sample data from MongoDB database {connectionString}", ex);
                throw;
            }
        }

        /// <summary>
        /// Maps a BSON type to a custom DataType enum value.
        /// </summary>
        /// <param name="bsonType">The BSON type.</param>
        /// <returns>The corresponding DataType enum value.</returns>
        private DataType MapBsonTypeToDataType(BsonType bsonType) => bsonType switch
        {
            BsonType.Int32 or BsonType.Int64 => DataType.Integer,
            BsonType.Decimal128 or BsonType.Double => DataType.Decimal,
            BsonType.String or BsonType.ObjectId or BsonType.RegularExpression or BsonType.Array or BsonType.Document => DataType.String,
            BsonType.DateTime => DataType.DateTime,
            BsonType.Boolean => DataType.Boolean,
            BsonType.Binary => DataType.Binary,
            _ => DataType.Unsupported
        };

        /// <summary>
        /// Converts a BSON value to a .NET object based on the specified DataType.
        /// </summary>
        /// <param name="value">The BSON value to convert.</param>
        /// <param name="dataType">The target DataType.</param>
        /// <returns>The converted .NET object.</returns>
        private object ConvertBsonValue(BsonValue value, DataType dataType)
        {
            try
            {
                return dataType switch
                {
                    DataType.Integer => value.AsInt32,
                    DataType.Decimal => value.AsDecimal,
                    DataType.Boolean => value.AsBoolean,
                    DataType.DateTime => value.ToUniversalTime(),
                    _ => value.ToString()
                };
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Failed to convert BSON value '{value}' to DataType.{dataType}. Error: {ex.Message}");
                return DBNull.Value;
            }
        }
    }
}