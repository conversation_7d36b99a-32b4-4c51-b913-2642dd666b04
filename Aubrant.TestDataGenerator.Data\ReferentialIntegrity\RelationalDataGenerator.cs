﻿using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Interfaces;
using Aubrant.TestDataGenerator.Data.Enums;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Aubrant.TestDataGenerator.Data.ReferentialIntegrity
{
    /// <summary>
    /// Main implementation of relational data generator
    /// </summary>
    public class RelationalDataGenerator : IRelationalDataGenerator
    {
        private readonly IDependencyGraphBuilder _dependencyGraphBuilder;
        private readonly IReferenceDataManager _referenceDataManager;

        public RelationalDataGenerator(
            IDependencyGraphBuilder? dependencyGraphBuilder = null,
            IReferenceDataManager? referenceDataManager = null)
        {
            _dependencyGraphBuilder = dependencyGraphBuilder ?? new DependencyGraphBuilder();
            _referenceDataManager = referenceDataManager ?? new InMemoryReferenceDataManager();
        }

        public async Task<RelationalGenerationResult> GenerateAsync(DataSource dataSource, RelationalGenerationOptions options)
        {
            var result = new RelationalGenerationResult();
            var startTime = DateTime.UtcNow;

            try
            {
                // Step 1: Build dependency graph
                var dependencyGraph = _dependencyGraphBuilder.BuildDependencyGraph(dataSource);
                result.DependencyGraph = dependencyGraph;

                // Step 2: Validate the graph
                var validationResult = _dependencyGraphBuilder.ValidateGraph(dependencyGraph);
                if (!validationResult.IsValid)
                {
                    result.Success = false;
                    result.ErrorMessage = $"Dependency graph validation failed: {string.Join(", ", validationResult.Errors)}";
                    return result;
                }

                // Step 3: Create generation context
                var context = new RelationalGenerationContext
                {
                    DependencyGraph = dependencyGraph,
                    ReferenceDataManager = _referenceDataManager,
                    Options = options,
                    Random = options.RandomSeed.HasValue ? new Random(options.RandomSeed.Value) : new Random(),
                    SessionId = Guid.NewGuid()
                };

                // Step 4: Clear any existing reference data
                _referenceDataManager.ClearAllReferences();

                // Step 5: Generate data in dependency order
                await GenerateDataInDependencyOrder(context);

                // Step 6: Handle circular references
                await HandleCircularReferences(context);

                // Step 7: Validate integrity if requested
                if (options.ValidateIntegrityAfterGeneration)
                {
                    result.ValidationResult = ValidateGeneratedData(context);
                }

                result.GeneratedTables = context.GeneratedTables;
                result.Statistics = context.Statistics;
                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                result.TotalDuration = DateTime.UtcNow - startTime;
            }

            return result;
        }

        public async Task<DataTable> GenerateEntityDataAsync(Entity entity, int rowCount, RelationalGenerationContext context)
        {
            var entityStartTime = DateTime.UtcNow;

            try
            {
                // Create DataTable structure
                var dataTable = CreateDataTableStructure(entity);

                // Generate rows
                for (int i = 0; i < rowCount; i++)
                {
                    var row = dataTable.NewRow();
                    var generatorContext = new DataGeneratorContext
                    {
                        RowIndex = i,
                        Row = row,
                        Random = context.Random,
                        ReferenceDataManager = context.ReferenceDataManager,
                        CurrentEntityName = entity.Name,
                        TotalRows = rowCount,
                        SessionId = context.SessionId
                    };

                    await GenerateRowData(entity, row, generatorContext);
                    dataTable.Rows.Add(row);
                }

                // Register primary key values for reference by other entities
                RegisterPrimaryKeyValues(entity, dataTable, context);

                // Update statistics
                context.Statistics.EntitiesProcessed++;
                context.Statistics.TotalRowsGenerated += rowCount;
                context.Statistics.EntityGenerationTimes[entity.Name!] = DateTime.UtcNow - entityStartTime;

                return dataTable;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to generate data for entity '{entity.Name}': {ex.Message}", ex);
            }
        }

        public RelationalValidationResult ValidateDataSource(DataSource dataSource)
        {
            var result = new RelationalValidationResult { IsValid = true };

            try
            {
                // Build and validate dependency graph
                var dependencyGraph = _dependencyGraphBuilder.BuildDependencyGraph(dataSource);
                var graphValidation = _dependencyGraphBuilder.ValidateGraph(dependencyGraph);

                result.Errors.AddRange(graphValidation.Errors);
                result.Warnings.AddRange(graphValidation.Warnings);

                if (!graphValidation.IsValid)
                {
                    result.IsValid = false;
                }

                // Validate entity configurations
                foreach (var entity in dataSource.Entities)
                {
                    ValidateEntityConfiguration(entity, result);
                }

                // Validate foreign key configurations
                ValidateForeignKeyConfigurations(dataSource, result);
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Errors.Add($"Validation failed: {ex.Message}");
            }

            return result;
        }

        private async Task GenerateDataInDependencyOrder(RelationalGenerationContext context)
        {
            var dependencyLevels = context.DependencyGraph.TopologicalOrder
                .GroupBy(n => n.DependencyLevel)
                .OrderBy(g => g.Key)
                .ToList();

            foreach (var level in dependencyLevels)
            {
                var entitiesAtLevel = level.Where(n => !n.IsInCircularReference).ToList();

                if (entitiesAtLevel.Any())
                {
                    // Generate entities at this level in parallel
                    var tasks = entitiesAtLevel.Select(async node =>
                    {
                        var entity = node.Entity;
                        var rowCount = context.Options.GetRowCount(entity.Name!);

                        context.EntitiesInProgress.Add(entity.Name!);
                        try
                        {
                            var dataTable = await GenerateEntityDataAsync(entity, rowCount, context);
                            context.GeneratedTables[entity.Name!] = dataTable;
                            context.GeneratedEntities.Add(entity.Name!);
                        }
                        finally
                        {
                            context.EntitiesInProgress.Remove(entity.Name!);
                        }
                    });

                    await Task.WhenAll(tasks);
                }
            }
        }

        private async Task HandleCircularReferences(RelationalGenerationContext context)
        {
            foreach (var circularGroup in context.DependencyGraph.CircularReferences)
            {
                await HandleCircularReferenceGroup(circularGroup, context);
                context.Statistics.CircularReferencesHandled++;
            }
        }

        private async Task HandleCircularReferenceGroup(CircularReferenceGroup group, RelationalGenerationContext context)
        {
            switch (group.Strategy)
            {
                case CircularReferenceStrategy.NullFirstThenUpdate:
                    await HandleNullFirstThenUpdate(group, context);
                    break;

                case CircularReferenceStrategy.SubsetFirst:
                    await HandleSubsetFirst(group, context);
                    break;

                case CircularReferenceStrategy.DeferredAssignment:
                    await HandleDeferredAssignment(group, context);
                    break;
            }
        }

        private async Task HandleNullFirstThenUpdate(CircularReferenceGroup group, RelationalGenerationContext context)
        {
            // Generate entities with null foreign keys first
            foreach (var entityName in group.EntityNames)
            {
                if (!context.GeneratedEntities.Contains(entityName))
                {
                    var node = context.DependencyGraph.Nodes[entityName];
                    var entity = node.Entity;
                    var rowCount = context.Options.GetRowCount(entityName);

                    // Temporarily set foreign keys to null
                    var originalSettings = TemporarilySetForeignKeysToNull(entity, group);

                    try
                    {
                        var dataTable = await GenerateEntityDataAsync(entity, rowCount, context);
                        context.GeneratedTables[entityName] = dataTable;
                        context.GeneratedEntities.Add(entityName);
                    }
                    finally
                    {
                        // Restore original settings
                        RestoreForeignKeySettings(entity, originalSettings);
                    }
                }
            }

            // Update foreign keys with actual references
            await UpdateCircularForeignKeys(group, context);
        }

        private async Task HandleSubsetFirst(CircularReferenceGroup group, RelationalGenerationContext context)
        {
            // Generate a subset of the first entity, then generate others, then complete the first
            var firstEntityName = group.EntityNames.First();
            var node = context.DependencyGraph.Nodes[firstEntityName];
            var entity = node.Entity;
            var totalRowCount = context.Options.GetRowCount(firstEntityName);
            var subsetRowCount = Math.Max(1, totalRowCount / 10); // Generate 10% first

            // Generate subset
            var subsetTable = await GenerateEntityDataAsync(entity, subsetRowCount, context);
            context.GeneratedTables[firstEntityName] = subsetTable;
            context.GeneratedEntities.Add(firstEntityName);

            // Generate other entities in the group
            foreach (var entityName in group.EntityNames.Skip(1))
            {
                if (!context.GeneratedEntities.Contains(entityName))
                {
                    var otherNode = context.DependencyGraph.Nodes[entityName];
                    var otherEntity = otherNode.Entity;
                    var rowCount = context.Options.GetRowCount(entityName);

                    var dataTable = await GenerateEntityDataAsync(otherEntity, rowCount, context);
                    context.GeneratedTables[entityName] = dataTable;
                    context.GeneratedEntities.Add(entityName);
                }
            }

            // Complete the first entity
            var remainingRows = totalRowCount - subsetRowCount;
            if (remainingRows > 0)
            {
                var additionalTable = await GenerateEntityDataAsync(entity, remainingRows, context);

                // Merge tables
                foreach (DataRow row in additionalTable.Rows)
                {
                    subsetTable.ImportRow(row);
                }
            }
        }

        private async Task HandleDeferredAssignment(CircularReferenceGroup group, RelationalGenerationContext context)
        {
            // Generate all entities with placeholder foreign keys, then assign real values
            var placeholderTables = new Dictionary<string, DataTable>();

            foreach (var entityName in group.EntityNames)
            {
                if (!context.GeneratedEntities.Contains(entityName))
                {
                    var node = context.DependencyGraph.Nodes[entityName];
                    var entity = node.Entity;
                    var rowCount = context.Options.GetRowCount(entityName);

                    var dataTable = await GenerateEntityDataAsync(entity, rowCount, context);
                    placeholderTables[entityName] = dataTable;
                    context.GeneratedTables[entityName] = dataTable;
                    context.GeneratedEntities.Add(entityName);
                }
            }

            // Assign real foreign key values
            await AssignDeferredForeignKeys(group, placeholderTables, context);
        }

        private DataTable CreateDataTableStructure(Entity entity)
        {
            var dataTable = new DataTable(entity.Name);

            foreach (var field in entity.Fields)
            {
                var column = new DataColumn(field.Name)
                {
                    DataType = GetSystemType(field.Type),
                    AllowDBNull = field.IsNullable
                };

                if (field.MaxLength.HasValue && field.Type == DataType.String)
                {
                    column.MaxLength = field.MaxLength.Value;
                }

                dataTable.Columns.Add(column);
            }

            return dataTable;
        }

        private Type GetSystemType(DataType dataType)
        {
            return dataType switch
            {
                DataType.String => typeof(string),
                DataType.Integer => typeof(int),
                DataType.Decimal => typeof(decimal),
                DataType.DateTime => typeof(DateTime),
                DataType.Boolean => typeof(bool),
                _ => typeof(object)
            };
        }

        private async Task GenerateRowData(Entity entity, DataRow row, DataGeneratorContext context)
        {
            foreach (var field in entity.Fields)
            {
                context.CurrentFieldName = field.Name;

                if (field.FieldSetting != null)
                {
                    var value = await GenerateFieldValue(field, context);
                    row[field.Name!] = value?.Value ?? DBNull.Value;

                    // Track foreign key generation (statistics tracking would be handled by the context)
                    // Note: Statistics tracking removed for now to fix compilation
                }
                else
                {
                    // Default value for fields without generators
                    row[field.Name!] = GetDefaultValue(field.Type);
                }
            }
        }

        private async Task<DataValue?> GenerateFieldValue(Field field, DataGeneratorContext context)
        {
            if (field.FieldSetting == null) return null;

            try
            {
                return field.FieldSetting.Type switch
                {
                    GeneratorType.AutoIncrement => GenerateAutoIncrement(field, context),
                    GeneratorType.Reference => GenerateReference(field, context),
                    GeneratorType.Random => GenerateRandom(field, context),
                    GeneratorType.Choice => GenerateChoice(field, context),
                    GeneratorType.Function => await GenerateFunction(field, context),
                    _ => new DataValue(TestDataGenerator.Core.Enums.DataType.Any, GetDefaultValue(field.Type))
                };
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to generate value for field '{field.Name}': {ex.Message}", ex);
            }
        }

        private DataValue GenerateAutoIncrement(Field field, DataGeneratorContext context)
        {
            // Simple auto-increment: start from 1 and increment by row index
            return new DataValue(context.RowIndex + 1);
        }

        private DataValue GenerateReference(Field field, DataGeneratorContext context)
        {
            if (field.FieldSetting?.Settings == null)
                return new DataValue(DBNull.Value);

            try
            {
                // Parse reference settings (this would be more sophisticated in practice)
                var settings = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(field.FieldSetting.Settings);
                if (settings == null) return new DataValue(DBNull.Value);

                var targetEntity = settings.GetValueOrDefault("TargetEntity")?.ToString();
                var targetField = settings.GetValueOrDefault("TargetField")?.ToString();

                if (string.IsNullOrEmpty(targetEntity) || string.IsNullOrEmpty(targetField))
                    return new DataValue(DBNull.Value);

                // Use reference data manager directly instead of ReferenceGenerator
                var referenceValue = context.ReferenceDataManager?.GetRandomReference(targetEntity, targetField, context.Random);
                return referenceValue ?? new DataValue(DBNull.Value);
            }
            catch
            {
                return new DataValue(DBNull.Value);
            }
        }

        private DataValue GenerateRandom(Field field, DataGeneratorContext context)
        {
            // Simple random generation based on field type
            return field.Type switch
            {
                DataType.Integer => new DataValue(context.Random.Next(1, 1000)),
                DataType.Decimal => new DataValue((decimal)(context.Random.NextDouble() * 1000)),
                DataType.String => new DataValue($"Random_{context.Random.Next(1000, 9999)}"),
                DataType.DateTime => new DataValue(DateTime.Now.AddDays(context.Random.Next(-365, 365))),
                DataType.Boolean => new DataValue(context.Random.Next(2) == 1),
                _ => new DataValue("Default")
            };
        }

        private DataValue GenerateChoice(Field field, DataGeneratorContext context)
        {
            // Placeholder for choice generation
            return GenerateRandom(field, context);
        }

        private async Task<DataValue> GenerateFunction(Field field, DataGeneratorContext context)
        {
            // Placeholder for function generation
            return GenerateRandom(field, context);
        }

        private object GetDefaultValue(DataType dataType)
        {
            return dataType switch
            {
                DataType.String => string.Empty,
                DataType.Integer => 0,
                DataType.Decimal => 0m,
                DataType.DateTime => DateTime.Now,
                DataType.Boolean => false,
                _ => DBNull.Value
            };
        }

        private void RegisterPrimaryKeyValues(Entity entity, DataTable dataTable, RelationalGenerationContext context)
        {
            var primaryKeyFields = entity.Fields.Where(f => f.IsPrimaryKey).ToList();

            foreach (var pkField in primaryKeyFields)
            {
                var values = dataTable.AsEnumerable()
                    .Select(row => new DataValue(TestDataGenerator.Core.Enums.DataType.Any, row[pkField.Name!]))
                    .ToList();

                context.ReferenceDataManager.RegisterPrimaryKeyPool(entity.Name!, pkField.Name!, values);
            }
        }

        private Dictionary<Field, FieldSetting?> TemporarilySetForeignKeysToNull(Entity entity, CircularReferenceGroup group)
        {
            var originalSettings = new Dictionary<Field, FieldSetting?>();

            foreach (var relationship in group.CircularRelationships)
            {
                if (relationship.SourceField.Entity.Name == entity.Name)
                {
                    originalSettings[relationship.SourceField] = relationship.SourceField.FieldSetting;

                    // Set to null generator temporarily
                    relationship.SourceField.FieldSetting = new FieldSetting
                    {
                        Type = GeneratorType.Choice,
                        Settings = "{\"Values\": [null], \"Probabilities\": [100]}"
                    };
                }
            }

            return originalSettings;
        }

        private void RestoreForeignKeySettings(Entity entity, Dictionary<Field, FieldSetting?> originalSettings)
        {
            foreach (var kvp in originalSettings)
            {
                kvp.Key.FieldSetting = kvp.Value;
            }
        }

        private async Task UpdateCircularForeignKeys(CircularReferenceGroup group, RelationalGenerationContext context)
        {
            // Implementation for updating foreign keys after initial null generation
            // This would involve updating the generated tables with proper foreign key values
        }

        private async Task AssignDeferredForeignKeys(CircularReferenceGroup group, Dictionary<string, DataTable> tables, RelationalGenerationContext context)
        {
            // Implementation for assigning deferred foreign keys
            // This would involve updating placeholder values with real foreign key references
        }

        private void ValidateEntityConfiguration(Entity entity, RelationalValidationResult result)
        {
            // Validate that the entity has proper field configurations
            if (!entity.Fields.Any())
            {
                result.Warnings.Add($"Entity '{entity.Name}' has no fields defined");
            }

            var primaryKeyFields = entity.Fields.Where(f => f.IsPrimaryKey).ToList();
            if (!primaryKeyFields.Any())
            {
                result.Warnings.Add($"Entity '{entity.Name}' has no primary key defined");
            }
        }

        private void ValidateForeignKeyConfigurations(DataSource dataSource, RelationalValidationResult result)
        {
            foreach (var entity in dataSource.Entities)
            {
                foreach (var keyConstraint in entity.KeyConstraints.Where(kc => kc.KeyType == KeyType.Foreign))
                {
                    if (keyConstraint.ReferencedEntity != null)
                    {
                        var referencedEntity = dataSource.Entities.FirstOrDefault(e => e.Name == keyConstraint.ReferencedEntity);
                        if (referencedEntity == null)
                        {
                            result.Errors.Add($"Entity '{entity.Name}' references non-existent entity '{keyConstraint.ReferencedEntity}'");
                            result.IsValid = false;
                        }
                    }
                }
            }
        }

        private RelationalValidationResult ValidateGeneratedData(RelationalGenerationContext context)
        {
            var result = new RelationalValidationResult { IsValid = true };

            // Validate referential integrity across generated tables
            foreach (var node in context.DependencyGraph.Nodes.Values)
            {
                foreach (var relationship in node.ForeignKeyRelationships)
                {
                    ValidateRelationshipIntegrity(relationship, context, result);
                }
            }

            return result;
        }

        private void ValidateRelationshipIntegrity(ForeignKeyRelationship relationship, RelationalGenerationContext context, RelationalValidationResult result)
        {
            var sourceEntityName = relationship.SourceField.Entity.Name!;
            var targetEntityName = relationship.TargetEntityName;

            if (!context.GeneratedTables.TryGetValue(sourceEntityName, out var sourceTable) ||
                !context.GeneratedTables.TryGetValue(targetEntityName, out var targetTable))
            {
                return; // Skip validation if tables not found
            }

            var sourceFieldName = relationship.SourceField.Name!;
            var targetFieldName = relationship.TargetFieldName;

            var targetValues = new HashSet<object>(
                targetTable.AsEnumerable()
                    .Select(row => row[targetFieldName])
                    .Where(val => val != DBNull.Value)
            );

            var violations = 0;
            var orphanedRecords = new List<OrphanedRecord>();

            for (int i = 0; i < sourceTable.Rows.Count; i++)
            {
                var foreignKeyValue = sourceTable.Rows[i][sourceFieldName];

                if (foreignKeyValue != DBNull.Value && !targetValues.Contains(foreignKeyValue))
                {
                    violations++;
                    orphanedRecords.Add(new OrphanedRecord
                    {
                        EntityName = sourceEntityName,
                        RowIndex = i,
                        ForeignKeyField = sourceFieldName,
                        ForeignKeyValue = foreignKeyValue,
                        TargetEntity = targetEntityName
                    });
                }
            }

            if (violations > 0)
            {
                result.IsValid = false;
                result.IntegrityViolations.Add(new IntegrityViolation
                {
                    SourceEntity = sourceEntityName,
                    SourceField = sourceFieldName,
                    TargetEntity = targetEntityName,
                    TargetField = targetFieldName,
                    ViolationCount = violations,
                    Description = $"Found {violations} foreign key references that don't exist in target table"
                });

                result.OrphanedRecords.AddRange(orphanedRecords);
            }
        }
    }
}
