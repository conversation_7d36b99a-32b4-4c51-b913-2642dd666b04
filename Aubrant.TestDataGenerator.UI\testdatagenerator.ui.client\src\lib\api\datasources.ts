import type {
  ApiResponse,
  DataSource,
  TestConnectionRequest,
  TestConnectionResponse,
  SchemaDiscoveryRequest,
  SchemaDiscoveryResponse,
  TableRelationship,
} from "./types"

// Mock data - same as current static data
const mockDataSources = [
  {
    id: "ds-sql-server",
    name: "Main Database",
    description: "Primary SQL Server database",
    type: "Relational Database",
    provider: "SQL Server",
    connectionProperties: {
      server: "localhost",
      port: "1433",
      userId: "sa",
      database: "TestDB",
    },
    schemas: [
      {
        name: "dbo",
        tables: [
          {
            name: "Album",
            schema: "dbo",
            columns: [
              { name: "AlbumId", type: "INTEGER", isPrimaryKey: true, isNotNull: true },
              { name: "Title", type: "NVARCHAR(160)", isPrimaryKey: false, isNotNull: true },
              { name: "ArtistId", type: "INTEGER", isPrimaryKey: false, isNotNull: true },
            ],
            relationships: [
              { type: "ManyToOne" as TableRelationship['type'], targetTable: "Artist", foreignKey: "ArtistId" },
              { type: "OneToMany" as TableRelationship['type'], targetTable: "Track", foreignKey: "AlbumId" },
            ],
          },
          // ... more tables
        ],
      },
      // ... more schemas
    ],
  },
  {
    id: "ds-mongodb",
    name: "Analytics Database",
    description: "MongoDB analytics database",
    type: "NoSQL Database",
    provider: "MongoDB",
    connectionProperties: {
      host: "localhost",
      port: "27017",
      database: "analytics",
    },
    collections: [
      {
        name: "users",
        fields: [
          { name: "_id", type: "ObjectId", isPrimaryKey: true, isNotNull: true },
          { name: "username", type: "String", isPrimaryKey: false, isNotNull: true },
          { name: "email", type: "String", isPrimaryKey: false, isNotNull: true },
          // ... more fields
        ],
        relationships: [
          { type: "OneToMany" as TableRelationship['type'], targetCollection: "sessions", foreignKey: "userId" },
          // ... more relationships
        ],
      },
      // ... more collections
    ],
  },
]

export class DataSourcesApi {
  /**
   * Test connection to a data source
   */
  async testConnection(request: TestConnectionRequest): Promise<TestConnectionResponse> {
    // TODO: Replace with real API call
    // return apiClient.post<TestConnectionResponse>('/datasources/test-connection', request)

    await this.delay(2000) // Simulate connection test time

    // Mock different responses based on provider
    const mockResponses: Record<string, TestConnectionResponse> = {
      "SQL Server": {
        success: true,
        message: "Successfully connected to SQL Server database",
        canAccessSchema: true,
        schemaObjects: {
          schemas: mockDataSources.find((ds) => ds.provider === "SQL Server")?.schemas,
        },
      },
      MongoDB: {
        success: true,
        message: "Successfully connected to MongoDB database",
        canAccessSchema: true,
        schemaObjects: {
          collections: mockDataSources.find((ds) => ds.provider === "MongoDB")?.collections,
        },
      },
      MySQL: {
        success: true,
        message: "Successfully connected to MySQL database",
        canAccessSchema: false, // Simulate limited permissions
      },
    }

    // Simulate occasional failures
    if (Math.random() < 0.1) {
      return {
        success: false,
        message: "Connection failed: Unable to connect to database server",
        canAccessSchema: false,
      }
    }

    return (
      mockResponses[request.provider] || {
        success: true,
        message: "Connection successful",
        canAccessSchema: true,
      }
    )
  }

  /**
   * Discover schema objects for a data source
   */
  async discoverSchema(request: SchemaDiscoveryRequest): Promise<SchemaDiscoveryResponse> {
    // TODO: Replace with real API call
    // return apiClient.post<SchemaDiscoveryResponse>('/datasources/discover-schema', request)

    await this.delay(3000) // Simulate schema discovery time

    const mockDataSource = mockDataSources.find((ds) => ds.id === request.dataSourceId)

    if (!mockDataSource) {
      return {
        success: false,
        error: "Data source not found",
      } as SchemaDiscoveryResponse
    }

    return {
      success: true,
      dataSource: mockDataSource as DataSource,
    }
  }

  /**
   * Get available data source providers
   */
  async getProviders(): Promise<ApiResponse<Record<string, any>>> {
    // TODO: Replace with real API call
    // return apiClient.get<ApiResponse<Record<string, any>>>('/datasources/providers')

    await this.delay(200)

    const providers = {
      File: [
        { name: "Excel", supported: true, features: ["read", "write"] },
        { name: "CSV", supported: true, features: ["read", "write"] },
        { name: "XML", supported: true, features: ["read"] },
        { name: "JSON", supported: true, features: ["read", "write"] },
        { name: "YAML", supported: false, features: [] },
      ],
      "Relational Database": [
        { name: "SQL Server", supported: true, features: ["schema_discovery", "relationships"] },
        { name: "Oracle", supported: true, features: ["schema_discovery", "relationships"] },
        { name: "MySQL", supported: true, features: ["schema_discovery", "relationships"] },
        { name: "SQLite", supported: true, features: ["schema_discovery"] },
        { name: "PostgreSQL", supported: true, features: ["schema_discovery", "relationships"] },
      ],
      "NoSQL Database": [
        { name: "MongoDB", supported: true, features: ["schema_discovery", "relationships"] },
        { name: "Elastic Search", supported: false, features: [] },
        { name: "Neo4J", supported: false, features: [] },
      ],
    }

    return {
      success: true,
      data: providers,
    }
  }

  /**
   * Validate data source configuration
   */
  async validateConfiguration(
    dataSource: Partial<DataSource>,
  ): Promise<ApiResponse<{ isValid: boolean; errors: string[] }>> {
    // TODO: Replace with real API call
    // return apiClient.post<ApiResponse<{ isValid: boolean; errors: string[] }>>('/datasources/validate', dataSource)

    await this.delay(500)

    const errors: string[] = []

    if (!dataSource.name?.trim()) {
      errors.push("Data source name is required")
    }

    if (!dataSource.type) {
      errors.push("Data source type is required")
    }

    if (!dataSource.provider) {
      errors.push("Provider is required")
    }

    // Validate connection properties based on type
    if (dataSource.type === "Relational Database") {
      if (!dataSource.connectionProperties?.server) {
        errors.push("Server is required for relational databases")
      }
      if (!dataSource.connectionProperties?.database) {
        errors.push("Database name is required")
      }
    }

    if (dataSource.type === "NoSQL Database" && dataSource.provider === "MongoDB") {
      if (!dataSource.connectionProperties?.host) {
        errors.push("Host is required for MongoDB")
      }
    }

    return {
      success: true,
      data: {
        isValid: errors.length === 0,
        errors,
      },
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }
}

export const dataSourcesApi = new DataSourcesApi()
