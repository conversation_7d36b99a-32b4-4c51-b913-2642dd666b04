using Bogus;
using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Functions;

namespace Aubrant.TestDataGenerator.Generators.Functions{
    /// <summary>
    /// A generator function that produces a random department name.
    /// </summary>
    public class DepartmentFunction : BaseFunction
    {
        public override string Name => "Department";
        public override string Description => "Generates a random department name.";
        public override DataType ReturnType => DataType.String;

        /// <summary>
        /// Initializes a new instance of the DepartmentFunction class.
        /// </summary>
        public DepartmentFunction() : base("Business")
        {
            Parameters.Add(new Parameter("locale", "The output locale.", DataType.String, new DataValue("en")));
        }

        /// <summary>
        /// Generates a random department name using the Bogus library.
        /// </summary>
        public override DataValue Generate(DataGeneratorContext context)
        {
            string locale = Parameters["locale"].Value?.ToString() ?? "en";
            var faker = new Faker(locale);
            string department = faker.Commerce.Department();
            return new DataValue(DataType.String, department);
        }
    }
}
