import type { ReactNode } from "react";
import {
    FileSpreadsheet,
    Code,
    Database,
    Network,
    Search,
} from "lucide-react";

export const DATABASE_TYPE_GROUPS = [
    {
        label: "File Types",
        types: ["Excel", "CSV", "JSON", "XML", "YAML"],
    },
    {
        label: "Relational Databases",
        types: ["SQL Server", "Oracle", "MySQL", "SQLite", "PostgreSQL"],
    },
    {
        label: "NoSQL Databases",
        types: ["MongoDB", "Elastic Search", "Neo4J"],
    },
];

export const getDatabaseTypeIcon = (provider: string): ReactNode => {
    switch (provider) {
        case "PostgreSQL":
            return <Database className="h-4 w-4 text-blue-800" />;
        case "MySQL":
            return <Database className="h-4 w-4 text-blue-700" />;
        case "Oracle":
            return <Database className="h-4 w-4 text-red-700" />;
        case "SQL Server":
            return <Database className="h-4 w-4 text-red-600" />;
        case "SQLite":
            return <Database className="h-4 w-4 text-gray-600" />;
        case "MongoDB":
            return <Database className="h-4 w-4 text-green-700" />;
        case "Neo4J":
            return <Network className="h-4 w-4 text-cyan-600" />;
        case "Elastic Search":
            return <Search className="h-4 w-4 text-yellow-700" />;
        case "Excel":
            return <FileSpreadsheet className="h-4 w-4 text-green-600" />;
        case "CSV":
            return <FileSpreadsheet className="h-4 w-4 text-blue-600" />;
        case "JSON":
            return <Code className="h-4 w-4 text-yellow-600" />;
        case "XML":
            return <Code className="h-4 w-4 text-orange-600" />;
        case "YAML":
            return <Code className="h-4 w-4 text-purple-600" />;
        default:
            return <Database className="h-4 w-4 text-gray-500" />;
    }
};
