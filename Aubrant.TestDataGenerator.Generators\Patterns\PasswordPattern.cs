using Aubrant.TestDataGenerator.Core.Patterns;

namespace Aubrant.TestDataGenerator.Generators.Patterns
{
    public class PasswordPattern : BasePattern
    {
        public PasswordPattern() : base(
            name: "Simple Password",
            description: "Generates a 12-character password with an optional 4 extra characters.",
            pattern: "{P12}{PLUS4}",
            tokens: new List<Token>
            {
                new AlphaNumToken("P12", "Base 12 alphanumeric characters", 12),
                new OptionalToken("PLUS4", "Optional 4 extra characters", "{P4}", new AlphaNumToken("P4", "4 extra alphanumeric characters", 4))
            })
        { }
    }
}
