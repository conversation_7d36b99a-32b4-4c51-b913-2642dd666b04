using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Functions;
using Aubrant.TestDataGenerator.Generators.Functions;
using System.Data;
using System.Dynamic;

namespace Aubrant.TestDataGenerator.Generators.Scripts
{
    /// <summary>
    /// Represents the global variables available to a C# script during execution.
    /// </summary>
    public class ScriptGlobals
    {
        /// <summary>
        /// Gets the parameters passed to the function, accessible as dynamic properties.
        /// </summary>
        public dynamic Parameter { get; private set; }

        /// <summary>
        /// Gets the fields of the current data row, accessible as dynamic properties.
        /// </summary>
        public dynamic Fields { get; private set; }

        /// <summary>
        /// Gets the built-in functions, accessible as dynamic properties.
        /// </summary>
        public dynamic Functions { get; private set; }

        /// <summary>
        /// Gets the random number generator.
        /// </summary>
        public Random Random { get; private set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="ScriptGlobals"/> class.
        /// </summary>
        /// <param name="context">The data generator context.</param>
        /// <param name="parameters">The collection of parameters to expose to the script.</param>
        public ScriptGlobals(DataGeneratorContext context, ParameterCollection parameters)
        {
            Parameter = new ExpandoObject();
            var parameterDict = (IDictionary<string, object>)Parameter;

            if (parameters != null)
            {
                foreach (var param in parameters)
                {
                    parameterDict[param.Name] = param.Value?.Value;
                }
            }

            Fields = new ExpandoObject();
            var fieldsDict = (IDictionary<string, object>)Fields;

            if (context.Row != null)
            {
                foreach (DataColumn col in context.Row.Table.Columns)
                {
                    fieldsDict[col.ColumnName] = context.Row[col];
                }
            }

            Functions = new ExpandoObject();
            var functionsDict = (IDictionary<string, object>)Functions;

            foreach (var functionEntry in FunctionRegistry.Functions)
            {
                var functionName = functionEntry.Key;
                var functionType = functionEntry.Value;

                // Create a delegate that can be invoked from the script
                functionsDict[functionName] = new Func<DataValue>(() =>
                {
                    if (FunctionRegistry.TryCreateFunction(functionName, context, out BaseFunction? funcInstance))
                    {
                        return funcInstance!.Generate(context);
                    }
                    throw new InvalidOperationException($"Could not create instance of function {functionName}.");
                });
            }

            Random = context.Random ?? new Random();
        }
    }
}
