using Aubrant.TestDataGenerator.Core.Interfaces;
using HotChocolate;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Aubrant.TestDataGenerator.Data
{
    /// <summary>
    /// Represents a field (column) within an entity.
    /// </summary>
    public class Field
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int EntityId { get; set; }

        /// <summary>
        /// Gets or sets the name of the field.
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// Gets or sets the inferred data type of the field.
        /// </summary>
        [JsonConverter(typeof(StringEnumConverter))]
        public Core.Enums.DataType Type { get; set; }

        /// <summary>
        /// Gets or sets the native data type name from the source system.
        /// </summary>
        public string? NativeType { get; set; }

        /// <summary>
        /// Gets or sets the maximum length of the field, if applicable.
        /// </summary>
        public int? MaxLength { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the field can contain null values.
        /// </summary>
        [Required]
        public bool IsNullable { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the field is part of the primary key.
        /// </summary>
        [Required]
        public bool IsPrimaryKey { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the field is an identity (auto-incrementing) column.
        /// </summary>
        [Required]
        public bool IsIdentity { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the field has a unique constraint.
        /// </summary>
        [Required]
        public bool IsUnique { get; set; }

        [ForeignKey("EntityId")]
        public virtual Entity? Entity { get; set; }

        public virtual FieldSetting? FieldSetting { get; set; }

        [NotMapped]
        [GraphQLIgnore]
        public IGenerator DataGenerator { get; set; }
    }
}