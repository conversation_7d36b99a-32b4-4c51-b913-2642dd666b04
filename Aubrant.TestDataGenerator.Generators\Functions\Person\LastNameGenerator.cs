﻿using Bogus;
using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Functions;

namespace Aubrant.TestDataGenerator.Generators.Functions
{
    /// <summary>
    /// A generator function that produces realistic last names based on a specified locale.
    /// It utilizes the Bogus library for name generation.
    /// </summary>
    public class LastNameFunction : BaseFunction
    {
        public override string Name => "LastName";
        public override string Description => "Generates a realistic last name based on `locale`.";
        public override DataType ReturnType => DataType.String;

        /// <summary>
        /// Initializes a new instance of the LastNameGenerator class.
        /// Defines a 'locale' parameter for language-specific last name generation.
        /// </summary>
        public LastNameFunction() : base("Person")
        {
            Parameters.Add(new Parameter("locale", "The output locale.", DataType.String, new DataValue("en"))); // Default locale is English
        }

        /// <summary>
        /// Generates a random last name using the Bogus library, considering the specified locale.
        /// </summary>
        /// <returns>A DataValue containing the generated last name as a string.</returns>
        /// <exception cref="InvalidOperationException">Thrown if the 'locale' parameter is missing or invalid.</exception>
        public override DataValue Generate(DataGeneratorContext context)
        {
            string locale = Parameters["locale"].Value?.ToString() ??
                            throw new InvalidOperationException("Locale parameter is missing or invalid.");

            // Bogus can throw FormatException if locale is invalid
            try
            {
                var faker = new Faker(locale);
                return new DataValue(DataType.String, faker.Name.LastName());
            }
            catch (FormatException ex)
            {
                throw new InvalidOperationException($"Invalid locale '{locale}' provided for LastNameGenerator.", ex);
            }
        }
    }
}
