using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.Data.Enums;
using Aubrant.TestDataGenerator.UI.Server.GraphQL;

namespace Aubrant.TestDataGenerator.UI.Server.Mutations
{
    [ExtendObjectType(typeof(Mutation))]
    public class RelationshipMutations
    {
        public async Task<Relationship> CreateRelationship(int sourceEntityId, string? sourceEntity, string? sourceField, string? targetEntity, string? targetField, Cardinality cardinality, [Service] DatabaseContext context)
        {
            var relationship = new Relationship
            {
                SourceEntityId = sourceEntityId,
                SourceField = sourceField,
                TargetEntity = targetEntity,
                TargetField = targetField,
                Cardinality = cardinality
            };

            context.Relationships.Add(relationship);
            await context.SaveChangesAsync();

            return relationship;
        }

        public async Task<Relationship> UpdateRelationship(int id, string? sourceEntity, string? sourceField, string? targetEntity, string? targetField, Cardinality? cardinality, [Service] DatabaseContext context)
        {
            var relationship = await context.Relationships.FindAsync(id);

            if (relationship == null)
            {
                throw new System.Exception("Relationship not found.");
            }

            if (sourceField != null)
            {
                relationship.SourceField = sourceField;
            }

            if (targetEntity != null)
            {
                relationship.TargetEntity = targetEntity;
            }

            if (targetField != null)
            {
                relationship.TargetField = targetField;
            }

            if (cardinality.HasValue)
            {
                relationship.Cardinality = cardinality.Value;
            }

            await context.SaveChangesAsync();

            return relationship;
        }

        public async Task<bool> DeleteRelationship(int id, [Service] DatabaseContext context)
        {
            var relationship = await context.Relationships.FindAsync(id);

            if (relationship == null)
            {
                return false;
            }

            context.Relationships.Remove(relationship);
            await context.SaveChangesAsync();

            return true;
        }
    }
}
