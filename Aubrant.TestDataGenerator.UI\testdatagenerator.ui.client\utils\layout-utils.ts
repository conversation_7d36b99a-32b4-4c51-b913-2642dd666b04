export const wouldOverlap = (
  posA: { x: number; y: number },
  sizeA: { width: number; height: number },
  posB: { x: number; y: number },
  sizeB: { width: number; height: number },
  margin: number,
) => {
  return (
    posA.x < posB.x + sizeB.width + margin &&
    posA.x + sizeA.width + margin > posB.x &&
    posA.y < posB.y + sizeB.height + margin &&
    posA.y + sizeA.height + margin > posB.y
  )
}

export const findNonOverlappingPosition = (
  preferredPos: { x: number; y: number },
  entitySize: { width: number; height: number },
  existingEntities: Array<{ position: { x: number; y: number }; size: { width: number; height: number } }>,
  margin = 60, // Increased margin
) => {
  let position = { ...preferredPos }
  let attempts = 0
  const maxAttempts = 200 // Increased attempts

  while (attempts < maxAttempts) {
    const hasOverlap = existingEntities.some((existing) =>
      wouldOverlap(position, entitySize, existing.position, existing.size, margin),
    )

    if (!hasOverlap) {
      return position
    }

    // Try different positions in a spiral pattern with larger steps
    const angle = (attempts * 0.3) % (2 * Math.PI)
    const distance = Math.floor(attempts / 6) * 120 + 80
    position = {
      x: preferredPos.x + Math.cos(angle) * distance,
      y: preferredPos.y + Math.sin(angle) * distance,
    }
    attempts++
  }

  return position
}
