export interface Project {
    Id: number
    Name: string
    Description?: string
    Author: string
    CreatedDate: string
    LastExecutionDate?: Date
    DataSources: DataSource[]
}

export interface DataSource {
    Id: number
    Name: string
    Type: string
    Provider: string
    ConnectionString?: string
    Entities?: Entity[]
}

export interface Entity {
    Name: string
    Schema?: string
    Fields: Field[]
    Relationships: Relationship[]
    KeyConstraints: KeyConstraint[]
    Data: any[]
    position?: { x: number; y: number }
    dataSourceName?: string
}

export interface Field {
  Name: string
  Type?: string
  NativeType: string
  MaxLength?: number
  IsPrimaryKey?: boolean
  IsIdentity?: boolean
  IsNullable?: boolean
  IsUnique?: boolean
}

export interface Relationship {
  SourceEntity: string
  SourceField: string
  TargetEntity: string
  TargetField: string
  Cardinality: string
}

export interface KeyConstraint {
  Columns: string[]
  ReferencedColumns: string[]
  KeyType?: string
  ReferencedEntity?: string
}

export interface CardinalityInfo {
  source: { type: "one" | "many"; symbol: string }
  target: { type: "one" | "many"; symbol: string }
}

export interface ConnectionPoint {
  x: number
  y: number
  fieldIndex: number
}

export interface ProcessedRelationship {
  relationship: Relationship
  sourceEntity: Entity
  targetEntity: Entity
  sourcePath: string
  targetPath: string
  sourcePoint: ConnectionPoint
  targetPoint: ConnectionPoint
  cardinality: CardinalityInfo
  isSelected: boolean
}
