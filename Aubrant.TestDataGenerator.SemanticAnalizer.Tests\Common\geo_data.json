[{"Code": "US", "Name": "United States", "States": [{"Code": "AL", "Name": "Alabama", "Cities": [{"Name": "Huntsville", "CenterLatitude": 34.7304, "CenterLongitude": -86.5861, "RadiusKm": 30, "PostalCodePatterns": ["358##"]}, {"Name": "Birmingham", "CenterLatitude": 33.52066, "CenterLongitude": -86.80249, "RadiusKm": 30, "PostalCodePatterns": ["352##"]}, {"Name": "<PERSON>", "CenterLatitude": 32.36681, "CenterLongitude": -86.29997, "RadiusKm": 25, "PostalCodePatterns": ["361##"]}, {"Name": "Mobile", "CenterLatitude": 30.695366, "CenterLongitude": -88.039894, "RadiusKm": 25, "PostalCodePatterns": ["366##"]}, {"Name": "Tuscaloosa", "CenterLatitude": 33.20984, "CenterLongitude": -87.56917, "RadiusKm": 20, "PostalCodePatterns": ["354##"]}], "Counties": [], "PostalCodePatterns": ["350##", "351##", "352##", "354##", "355##", "356##", "357##", "358##", "359##", "360##", "361##", "362##", "363##", "364##", "365##", "366##", "367##", "368##", "369##"], "CenterLatitude": 32.7794, "CenterLongitude": -86.8287, "RadiusKm": 250}, {"Code": "AK", "Name": "Alaska", "Cities": [{"Name": "Anchorage", "CenterLatitude": 61.218, "CenterLongitude": -149.9, "RadiusKm": 50, "PostalCodePatterns": ["995##"]}, {"Name": "Juneau", "CenterLatitude": 58.302, "CenterLongitude": -134.42, "RadiusKm": 50, "PostalCodePatterns": ["998##"]}, {"Name": "Fairbanks", "CenterLatitude": 64.838, "CenterLongitude": -147.716, "RadiusKm": 50, "PostalCodePatterns": ["997##"]}, {"Name": "Eagle River", "CenterLatitude": 61.321, "CenterLongitude": -149.568, "RadiusKm": 10, "PostalCodePatterns": ["995##"]}, {"Name": "Badger", "CenterLatitude": 64.8, "CenterLongitude": -147.533, "RadiusKm": 10, "PostalCodePatterns": ["997##"]}], "Counties": [], "PostalCodePatterns": ["995##", "996##", "997##", "998##", "999##"], "CenterLatitude": 64, "CenterLongitude": -152, "RadiusKm": 500}, {"Code": "AR", "Name": "Arkansas", "Cities": [{"Name": "Little Rock", "CenterLatitude": 34.7465, "CenterLongitude": -92.2896, "RadiusKm": 30, "PostalCodePatterns": ["722##"]}, {"Name": "Fayetteville", "CenterLatitude": 36.0626, "CenterLongitude": -94.1574, "RadiusKm": 25, "PostalCodePatterns": ["727##"]}, {"Name": "Fort Smith", "CenterLatitude": 35.3859, "CenterLongitude": -94.3986, "RadiusKm": 25, "PostalCodePatterns": ["729##"]}, {"Name": "Springdale", "CenterLatitude": 36.1867, "CenterLongitude": -94.1288, "RadiusKm": 20, "PostalCodePatterns": ["727##"]}, {"Name": "Jonesboro", "CenterLatitude": 35.8423, "CenterLongitude": -90.7043, "RadiusKm": 20, "PostalCodePatterns": ["724##"]}, {"Name": "<PERSON>", "CenterLatitude": 36.332, "CenterLongitude": -94.1185, "RadiusKm": 20, "PostalCodePatterns": ["727##"]}, {"Name": "<PERSON>", "CenterLatitude": 35.0887, "CenterLongitude": -92.4421, "RadiusKm": 15, "PostalCodePatterns": ["720##"]}, {"Name": "North Little Rock", "CenterLatitude": 34.7695, "CenterLongitude": -92.2671, "RadiusKm": 20, "PostalCodePatterns": ["721##"]}, {"Name": "Bentonville", "CenterLatitude": 36.3729, "CenterLongitude": -94.2088, "RadiusKm": 15, "PostalCodePatterns": ["727##"]}, {"Name": "Pine Bluff", "CenterLatitude": 34.2284, "CenterLongitude": -92.0032, "RadiusKm": 15, "PostalCodePatterns": ["716##"]}], "Counties": [], "PostalCodePatterns": ["716##", "717##", "718##", "719##", "720##", "721##", "722##", "723##", "724##", "725##", "726##", "727##", "728##", "729##"], "CenterLatitude": 34.815, "CenterLongitude": -92.3016, "RadiusKm": 200}, {"Code": "AZ", "Name": "Arizona", "Cities": [{"Name": "Phoenix", "CenterLatitude": 33.4484, "CenterLongitude": -112.074, "RadiusKm": 50, "PostalCodePatterns": ["850##", "853##"]}, {"Name": "Tucson", "CenterLatitude": 32.2217, "CenterLongitude": -110.9265, "RadiusKm": 40, "PostalCodePatterns": ["857##"]}, {"Name": "Mesa", "CenterLatitude": 33.4152, "CenterLongitude": -111.8315, "RadiusKm": 30, "PostalCodePatterns": ["852##"]}, {"Name": "<PERSON>", "CenterLatitude": 33.3062, "CenterLongitude": -111.8413, "RadiusKm": 25, "PostalCodePatterns": ["852##"]}, {"Name": "Glendale", "CenterLatitude": 33.5387, "CenterLongitude": -112.186, "RadiusKm": 25, "PostalCodePatterns": ["853##"]}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 33.5092, "CenterLongitude": -111.899, "RadiusKm": 25, "PostalCodePatterns": ["852##"]}, {"Name": "<PERSON>", "CenterLatitude": 33.3528, "CenterLongitude": -111.789, "RadiusKm": 25, "PostalCodePatterns": ["852##"]}, {"Name": "Tempe", "CenterLatitude": 33.4148, "CenterLongitude": -111.9093, "RadiusKm": 25, "PostalCodePatterns": ["852##"]}, {"Name": "Peoria", "CenterLatitude": 33.5809, "CenterLongitude": -112.2371, "RadiusKm": 25, "PostalCodePatterns": ["853##"]}], "Counties": [], "PostalCodePatterns": ["850##", "852##", "853##", "855##", "856##", "857##", "859##", "860##", "863##", "864##", "865##"], "CenterLatitude": 34.048927, "CenterLongitude": -111.093735, "RadiusKm": 300}, {"Code": "CA", "Name": "California", "Cities": [{"Name": "Los Angeles", "CenterLatitude": 34.05, "CenterLongitude": -118.25, "RadiusKm": 70, "PostalCodePatterns": ["900##", "902##", "903##", "904##", "905##"]}, {"Name": "San Francisco", "CenterLatitude": 37.774, "CenterLongitude": -122.431, "RadiusKm": 40, "PostalCodePatterns": ["941##"]}, {"Name": "San Diego", "CenterLatitude": 32.716, "CenterLongitude": -117.165, "RadiusKm": 40, "PostalCodePatterns": ["921##"]}, {"Name": "San Jose", "CenterLatitude": 37.339, "CenterLongitude": -121.895, "RadiusKm": 35, "PostalCodePatterns": ["951##"]}, {"Name": "Fresno", "CenterLatitude": 36.748, "CenterLongitude": -119.772, "RadiusKm": 25, "PostalCodePatterns": ["937##"]}, {"Name": "Sacramento", "CenterLatitude": 38.582, "CenterLongitude": -121.494, "RadiusKm": 30, "PostalCodePatterns": ["958##"]}, {"Name": "Long Beach", "CenterLatitude": 33.767, "CenterLongitude": -118.189, "RadiusKm": 30, "PostalCodePatterns": ["908##"]}, {"Name": "Oakland", "CenterLatitude": 37.804, "CenterLongitude": -122.271, "RadiusKm": 25, "PostalCodePatterns": ["946##"]}, {"Name": "Bakersfield", "CenterLatitude": 35.373, "CenterLongitude": -119.019, "RadiusKm": 20, "PostalCodePatterns": ["933##"]}, {"Name": "Anaheim", "CenterLatitude": 33.835, "CenterLongitude": -117.915, "RadiusKm": 25, "PostalCodePatterns": ["928##"]}, {"Name": "Riverside", "CenterLatitude": 33.953, "CenterLongitude": -117.396, "RadiusKm": 25, "PostalCodePatterns": ["925##"]}, {"Name": "Santa Ana", "CenterLatitude": 33.746, "CenterLongitude": -117.868, "RadiusKm": 25, "PostalCodePatterns": ["927##"]}], "Counties": ["Los Angeles County", "Orange County", "San Diego County"]}, {"Code": "CO", "Name": "Colorado", "Cities": [{"Name": "Denver", "CenterLatitude": 39.739, "CenterLongitude": -104.985, "RadiusKm": 40, "PostalCodePatterns": ["802##"]}, {"Name": "Colorado Springs", "CenterLatitude": 38.834, "CenterLongitude": -104.821, "RadiusKm": 30, "PostalCodePatterns": ["809##"]}, {"Name": "Aurora", "CenterLatitude": 39.729, "CenterLongitude": -104.832, "RadiusKm": 25, "PostalCodePatterns": ["800##"]}, {"Name": "Fort Collins", "CenterLatitude": 40.585, "CenterLongitude": -105.084, "RadiusKm": 20, "PostalCodePatterns": ["805##"]}, {"Name": "Lakewood", "CenterLatitude": 39.705, "CenterLongitude": -105.081, "RadiusKm": 20, "PostalCodePatterns": ["802##"]}, {"Name": "<PERSON>", "CenterLatitude": 39.868, "CenterLongitude": -104.972, "RadiusKm": 20, "PostalCodePatterns": ["802##"]}, {"Name": "Westminster", "CenterLatitude": 39.837, "CenterLongitude": -105.037, "RadiusKm": 20, "PostalCodePatterns": ["800##"]}, {"Name": "Arvada", "CenterLatitude": 39.803, "CenterLongitude": -105.087, "RadiusKm": 15, "PostalCodePatterns": ["800##"]}, {"Name": "Centennial", "CenterLatitude": 39.579, "CenterLongitude": -104.877, "RadiusKm": 15, "PostalCodePatterns": ["801##"]}, {"Name": "Pueblo", "CenterLatitude": 38.254, "CenterLongitude": -104.609, "RadiusKm": 20, "PostalCodePatterns": ["810##"]}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 40.423, "CenterLongitude": -104.709, "RadiusKm": 15, "PostalCodePatterns": ["806##"]}, {"Name": "Boulder", "CenterLatitude": 40.015, "CenterLongitude": -105.271, "RadiusKm": 20, "PostalCodePatterns": ["803##"]}], "Counties": [], "PostalCodePatterns": ["800##", "801##", "802##", "803##", "804##", "805##", "806##", "807##", "808##", "809##", "810##", "811##", "812##", "813##", "814##", "815##", "816##"], "CenterLatitude": 39.113014, "CenterLongitude": -105.358887, "RadiusKm": 250}, {"Code": "CT", "Name": "Connecticut", "Cities": [{"Name": "Bridgeport", "CenterLatitude": 41.18639, "CenterLongitude": -73.195557, "RadiusKm": 25, "PostalCodePatterns": ["066##"]}, {"Name": "New Haven", "CenterLatitude": 41.308273, "CenterLongitude": -72.927887, "RadiusKm": 25, "PostalCodePatterns": ["065##"]}, {"Name": "Hartford", "CenterLatitude": 41.76371, "CenterLongitude": -72.685097, "RadiusKm": 25, "PostalCodePatterns": ["061##"]}, {"Name": "Stamford", "CenterLatitude": 41.053429, "CenterLongitude": -73.538734, "RadiusKm": 25, "PostalCodePatterns": ["069##"]}, {"Name": "Waterbury", "CenterLatitude": 41.55611, "CenterLongitude": -73.041389, "RadiusKm": 20, "PostalCodePatterns": ["067##"]}], "Counties": [], "PostalCodePatterns": ["060##", "061##", "062##", "063##", "064##", "065##", "066##", "067##", "068##", "069##"], "CenterLatitude": 41.6219, "CenterLongitude": -72.7273, "RadiusKm": 80}, {"Code": "DE", "Name": "Delaware", "Cities": [{"Name": "Wilmington", "CenterLatitude": 39.7458, "CenterLongitude": -75.5469, "RadiusKm": 30, "PostalCodePatterns": ["198##"]}, {"Name": "Dover", "CenterLatitude": 39.1581, "CenterLongitude": -75.5247, "RadiusKm": 20, "PostalCodePatterns": ["199##"]}, {"Name": "Rehoboth Beach", "CenterLatitude": 38.7208, "CenterLongitude": -75.0764, "RadiusKm": 10, "PostalCodePatterns": ["199##"]}, {"Name": "Newark", "CenterLatitude": 39.6836, "CenterLongitude": -75.75, "RadiusKm": 15, "PostalCodePatterns": ["197##"]}], "Counties": [], "PostalCodePatterns": ["197##", "198##", "199##"], "CenterLatitude": 38.98, "CenterLongitude": -75.51, "RadiusKm": 50}, {"Code": "FL", "Name": "Florida", "Cities": [{"Name": "Jacksonville", "CenterLatitude": 30.332184, "CenterLongitude": -81.655647, "RadiusKm": 40, "PostalCodePatterns": ["322##"]}, {"Name": "Miami", "CenterLatitude": 25.774, "CenterLongitude": -80.194, "RadiusKm": 50, "PostalCodePatterns": ["331##"]}, {"Name": "Tampa", "CenterLatitude": 27.948, "CenterLongitude": -82.458, "RadiusKm": 40, "PostalCodePatterns": ["336##"]}, {"Name": "Orlando", "CenterLatitude": 28.538, "CenterLongitude": -81.379, "RadiusKm": 35, "PostalCodePatterns": ["328##"]}, {"Name": "St. Petersburg", "CenterLatitude": 27.771, "CenterLongitude": -82.679, "RadiusKm": 30, "PostalCodePatterns": ["337##"]}, {"Name": "Tallahassee", "CenterLatitude": 30.46, "CenterLongitude": -84.28, "RadiusKm": 20, "PostalCodePatterns": ["323##"]}, {"Name": "Hialeah", "CenterLatitude": 26.011, "CenterLongitude": -80.149, "RadiusKm": 20, "PostalCodePatterns": ["330##"]}, {"Name": "Port St. Lucie", "CenterLatitude": 27.3, "CenterLongitude": -80.4, "RadiusKm": 15, "PostalCodePatterns": ["349##"]}, {"Name": "Cape Coral", "CenterLatitude": 26.6, "CenterLongitude": -82, "RadiusKm": 15, "PostalCodePatterns": ["339##"]}, {"Name": "Fort Lauderdale", "CenterLatitude": 26.1, "CenterLongitude": -80.1, "RadiusKm": 30, "PostalCodePatterns": ["333##"]}, {"Name": "Gainesville", "CenterLatitude": 29.7, "CenterLongitude": -82.3, "RadiusKm": 15, "PostalCodePatterns": ["326##"]}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 27.3, "CenterLongitude": -82.5, "RadiusKm": 15, "PostalCodePatterns": ["342##"]}, {"Name": "Daytona Beach", "CenterLatitude": 29.2, "CenterLongitude": -81.1, "RadiusKm": 15, "PostalCodePatterns": ["321##"]}, {"Name": "Melbourne", "CenterLatitude": 28.1, "CenterLongitude": -80.6, "RadiusKm": 15, "PostalCodePatterns": ["329##"]}, {"Name": "Naples", "CenterLatitude": 26.1, "CenterLongitude": -81.8, "RadiusKm": 10, "PostalCodePatterns": ["341##"]}, {"Name": "West Palm Beach", "CenterLatitude": 26.7, "CenterLongitude": -80.1, "RadiusKm": 20, "PostalCodePatterns": ["334##"]}], "Counties": [], "PostalCodePatterns": ["320##", "321##", "322##", "323##", "324##", "325##", "326##", "327##", "328##", "329##", "330##", "331##", "333##", "334##", "335##", "336##", "337##", "338##", "339##", "341##", "342##", "344##", "346##", "347##"], "CenterLatitude": 28, "CenterLongitude": -81.5, "RadiusKm": 300}, {"Code": "GA", "Name": "Georgia", "Cities": [{"Name": "Atlanta", "CenterLatitude": 33.749, "CenterLongitude": -84.388, "RadiusKm": 50, "PostalCodePatterns": ["303##", "300##", "301##", "302##"]}, {"Name": "Columbus", "CenterLatitude": 32.461, "CenterLongitude": -84.988, "RadiusKm": 20, "PostalCodePatterns": ["319##"]}, {"Name": "Savannah", "CenterLatitude": 32.084, "CenterLongitude": -81.1, "RadiusKm": 20, "PostalCodePatterns": ["314##"]}, {"Name": "Athens", "CenterLatitude": 33.961, "CenterLongitude": -83.378, "RadiusKm": 15, "PostalCodePatterns": ["306##"]}, {"Name": "South Fulton", "CenterLatitude": 33.593, "CenterLongitude": -84.673, "RadiusKm": 15, "PostalCodePatterns": ["303##"]}, {"Name": "Sandy Springs", "CenterLatitude": 33.924, "CenterLongitude": -84.379, "RadiusKm": 15, "PostalCodePatterns": ["303##"]}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 34.023, "CenterLongitude": -84.362, "RadiusKm": 15, "PostalCodePatterns": ["300##"]}, {"Name": "Macon", "CenterLatitude": 32.841, "CenterLongitude": -83.632, "RadiusKm": 20, "PostalCodePatterns": ["312##"]}, {"Name": "Johns Creek", "CenterLatitude": 34.029, "CenterLongitude": -84.199, "RadiusKm": 10, "PostalCodePatterns": ["300##"]}, {"Name": "Albany", "CenterLatitude": 31.579, "CenterLongitude": -84.156, "RadiusKm": 15, "PostalCodePatterns": ["317##"]}, {"Name": "<PERSON> Robins", "CenterLatitude": 32.616, "CenterLongitude": -83.627, "RadiusKm": 15, "PostalCodePatterns": ["310##"]}, {"Name": "Alpharetta", "CenterLatitude": 34.075, "CenterLongitude": -84.294, "RadiusKm": 10, "PostalCodePatterns": ["300##"]}, {"Name": "Augusta", "CenterLatitude": 33.4708, "CenterLongitude": -81.975, "RadiusKm": 25, "PostalCodePatterns": ["309##"]}], "Counties": [], "PostalCodePatterns": ["300##", "301##", "302##", "303##", "305##", "306##", "307##", "308##", "309##", "310##", "312##", "313##", "314##", "315##", "316##", "317##", "318##", "319##"], "CenterLatitude": 32.6461, "CenterLongitude": -83.4317, "RadiusKm": 250}, {"Code": "HI", "Name": "Hawaii", "Cities": [{"Name": "Honolulu", "CenterLatitude": 21.315603, "CenterLongitude": -157.858093, "RadiusKm": 40, "PostalCodePatterns": ["968##"]}, {"Name": "Pearl City", "CenterLatitude": 21.243, "CenterLongitude": -157.5801, "RadiusKm": 15, "PostalCodePatterns": ["967##"]}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 19.70552, "CenterLongitude": -155.085918, "RadiusKm": 20, "PostalCodePatterns": ["967##"]}, {"Name": "<PERSON><PERSON><PERSON> (Honolulu County)", "CenterLatitude": 21.3975, "CenterLongitude": -157.73944, "RadiusKm": 15, "PostalCodePatterns": ["967##"]}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "CenterLatitude": 21.3925, "CenterLongitude": -158.01083, "RadiusKm": 15, "PostalCodePatterns": ["967##"]}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 21.40917, "CenterLongitude": -157.79917, "RadiusKm": 15, "PostalCodePatterns": ["967##"]}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 21.45, "CenterLongitude": -158.00111, "RadiusKm": 10, "PostalCodePatterns": ["967##"]}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 20.88167, "CenterLongitude": -156.4675, "RadiusKm": 10, "PostalCodePatterns": ["967##"]}, {"Name": "Ewa <PERSON>", "CenterLatitude": 21.33999, "CenterLongitude": -158.03039, "RadiusKm": 10, "PostalCodePatterns": ["967##"]}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 20.75917, "CenterLongitude": -156.45722, "RadiusKm": 10, "PostalCodePatterns": ["967##"]}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 20.87389, "CenterLongitude": -156.6775, "RadiusKm": 10, "PostalCodePatterns": ["967##"]}], "Counties": [], "PostalCodePatterns": ["967##", "968##"], "CenterLatitude": 20.9517, "CenterLongitude": -157.26, "RadiusKm": 200}, {"Code": "IA", "Name": "Iowa", "Cities": [{"Name": "Des Moines", "CenterLatitude": 41.6005, "CenterLongitude": -93.6091, "RadiusKm": 30, "PostalCodePatterns": ["503##"]}, {"Name": "Cedar Rapids", "CenterLatitude": 41.977879, "CenterLongitude": -91.665627, "RadiusKm": 25, "PostalCodePatterns": ["524##"]}, {"Name": "<PERSON>", "CenterLatitude": 41.543056, "CenterLongitude": -90.590836, "RadiusKm": 20, "PostalCodePatterns": ["528##"]}, {"Name": "Sioux City", "CenterLatitude": 42.495132, "CenterLongitude": -96.40007, "RadiusKm": 20, "PostalCodePatterns": ["511##"]}, {"Name": "Waterloo", "CenterLatitude": 42.495132, "CenterLongitude": -92.342957, "RadiusKm": 15, "PostalCodePatterns": ["507##"]}, {"Name": "Iowa City", "CenterLatitude": 41.661129, "CenterLongitude": -91.530167, "RadiusKm": 15, "PostalCodePatterns": ["522##"]}, {"Name": "Council Bluffs", "CenterLatitude": 41.257222, "CenterLongitude": -95.888889, "RadiusKm": 15, "PostalCodePatterns": ["515##"]}, {"Name": "Ames", "CenterLatitude": 42.034722, "CenterLongitude": -93.619444, "RadiusKm": 10, "PostalCodePatterns": ["500##"]}, {"Name": "Dubuque", "CenterLatitude": 42.5, "CenterLongitude": -90.666667, "RadiusKm": 15, "PostalCodePatterns": ["520##"]}, {"Name": "West Des Moines", "CenterLatitude": 41.588889, "CenterLongitude": -93.704167, "RadiusKm": 15, "PostalCodePatterns": ["502##"]}], "Counties": [], "PostalCodePatterns": ["500##", "501##", "502##", "503##", "504##", "505##", "506##", "507##", "508##", "509##", "510##", "511##", "512##", "513##", "514##", "515##", "516##"], "CenterLatitude": 41.962, "CenterLongitude": -93.385, "RadiusKm": 200}, {"Code": "ID", "Name": "Idaho", "Cities": [{"Name": "Boise", "CenterLatitude": 43.618881, "CenterLongitude": -116.215019, "RadiusKm": 30, "PostalCodePatterns": ["837##"]}, {"Name": "Meridian", "CenterLatitude": 43.612343, "CenterLongitude": -116.393707, "RadiusKm": 20, "PostalCodePatterns": ["836##"]}, {"Name": "Po<PERSON><PERSON>", "CenterLatitude": 42.880363, "CenterLongitude": -112.452911, "RadiusKm": 15, "PostalCodePatterns": ["832##"]}, {"Name": "Idaho Falls", "CenterLatitude": 43.49165, "CenterLongitude": -112.033966, "RadiusKm": 20, "PostalCodePatterns": ["834##"]}, {"Name": "<PERSON><PERSON> <PERSON>", "CenterLatitude": 47.702465, "CenterLongitude": -116.796883, "RadiusKm": 15, "PostalCodePatterns": ["838##"]}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 43.565468, "CenterLongitude": -116.560822, "RadiusKm": 15, "PostalCodePatterns": ["836##"]}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 46.392014, "CenterLongitude": -117.010826, "RadiusKm": 10, "PostalCodePatterns": ["835##"]}, {"Name": "<PERSON>", "CenterLatitude": 43.661766, "CenterLongitude": -116.691559, "RadiusKm": 10, "PostalCodePatterns": ["836##"]}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 43.49183, "CenterLongitude": -116.42012, "RadiusKm": 5, "PostalCodePatterns": ["836##"]}, {"Name": "Twin Falls", "CenterLatitude": 42.562786, "CenterLongitude": -114.460503, "RadiusKm": 15, "PostalCodePatterns": ["833##"]}], "Counties": [], "PostalCodePatterns": ["832##", "833##", "834##", "835##", "836##", "837##", "838##"], "CenterLatitude": 44.24, "CenterLongitude": -114.51, "RadiusKm": 250}, {"Code": "IL", "Name": "Illinois", "Cities": [{"Name": "Chicago", "CenterLatitude": 41.881832, "CenterLongitude": -87.623177, "RadiusKm": 60, "PostalCodePatterns": ["606##", "607##", "608##"]}, {"Name": "Aurora", "CenterLatitude": 41.763889, "CenterLongitude": -88.290001, "RadiusKm": 20, "PostalCodePatterns": ["605##"]}, {"Name": "Rockford", "CenterLatitude": 42.259445, "CenterLongitude": -89.064445, "RadiusKm": 20, "PostalCodePatterns": ["611##"]}, {"Name": "Springfield", "CenterLatitude": 39.80172, "CenterLongitude": -89.64371, "RadiusKm": 15, "PostalCodePatterns": ["627##"]}, {"Name": "Peoria", "CenterLatitude": 40.69365, "CenterLongitude": -89.58899, "RadiusKm": 15, "PostalCodePatterns": ["616##"]}, {"Name": "Champaign", "CenterLatitude": 40.116421, "CenterLongitude": -88.243385, "RadiusKm": 15, "PostalCodePatterns": ["618##"]}], "Counties": [], "PostalCodePatterns": ["600##", "601##", "603##", "604##", "605##", "606##", "607##", "608##", "609##", "610##", "611##", "612##", "613##", "614##", "615##", "616##", "617##", "618##", "619##", "620##", "622##", "623##", "624##", "625##", "626##", "627##", "628##", "629##"], "CenterLatitude": 40.041667, "CenterLongitude": -89.196389, "RadiusKm": 250}, {"Code": "IN", "Name": "Indiana", "Cities": [{"Name": "Indianapolis", "CenterLatitude": 39.791, "CenterLongitude": -86.148003, "RadiusKm": 40, "PostalCodePatterns": ["462##"]}, {"Name": "Carmel", "CenterLatitude": 39.978, "CenterLongitude": -86.118, "RadiusKm": 20, "PostalCodePatterns": ["460##"]}, {"Name": "Bloomington", "CenterLatitude": 39.165, "CenterLongitude": -86.526, "RadiusKm": 20, "PostalCodePatterns": ["474##"]}], "Counties": [], "PostalCodePatterns": ["460##", "461##", "462##", "463##", "465##", "466##", "467##", "468##", "469##", "470##", "471##", "472##", "473##", "474##", "475##", "476##", "477##", "478##", "479##"], "CenterLatitude": 39.895, "CenterLongitude": -86.266667, "RadiusKm": 180}, {"Code": "KS", "Name": "Kansas", "Cities": [{"Name": "Wichita", "CenterLatitude": 37.692, "CenterLongitude": -97.338, "RadiusKm": 40, "PostalCodePatterns": ["672##"]}, {"Name": "Overland Park", "CenterLatitude": 38.982, "CenterLongitude": -94.671, "RadiusKm": 25, "PostalCodePatterns": ["662##"]}, {"Name": "Kansas City", "CenterLatitude": 39.114, "CenterLongitude": -94.627, "RadiusKm": 25, "PostalCodePatterns": ["661##"]}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 38.881, "CenterLongitude": -94.819, "RadiusKm": 20, "PostalCodePatterns": ["660##"]}, {"Name": "Topeka", "CenterLatitude": 39.048, "CenterLongitude": -95.678, "RadiusKm": 20, "PostalCodePatterns": ["666##"]}, {"Name": "<PERSON>", "CenterLatitude": 38.972, "CenterLongitude": -95.235, "RadiusKm": 15, "PostalCodePatterns": ["660##"]}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 39.042, "CenterLongitude": -94.72, "RadiusKm": 15, "PostalCodePatterns": ["662##"]}, {"Name": "Manhattan", "CenterLatitude": 39.184, "CenterLongitude": -96.572, "RadiusKm": 10, "PostalCodePatterns": ["665##"]}, {"Name": "Lenexa", "CenterLatitude": 38.954, "CenterLongitude": -94.734, "RadiusKm": 10, "PostalCodePatterns": ["662##"]}, {"Name": "Salina", "CenterLatitude": 38.84, "CenterLongitude": -97.611, "RadiusKm": 10, "PostalCodePatterns": ["674##"]}], "Counties": [], "PostalCodePatterns": ["660##", "661##", "662##", "664##", "665##", "666##", "667##", "668##", "669##", "670##", "671##", "672##", "673##", "674##", "675##", "676##", "677##"], "CenterLatitude": 38.498333, "CenterLongitude": -98.698333, "RadiusKm": 200}, {"Code": "KY", "Name": "Kentucky", "Cities": [{"Name": "Louisville", "CenterLatitude": 38.25, "CenterLongitude": -85.76, "RadiusKm": 40, "PostalCodePatterns": ["402##"]}, {"Name": "Lexington", "CenterLatitude": 38, "CenterLongitude": -84.5, "RadiusKm": 30, "PostalCodePatterns": ["405##"]}, {"Name": "Bowling Green", "CenterLatitude": 36.99, "CenterLongitude": -86.44, "RadiusKm": 20, "PostalCodePatterns": ["421##"]}, {"Name": "Owensboro", "CenterLatitude": 37.77, "CenterLongitude": -87.11, "RadiusKm": 15, "PostalCodePatterns": ["423##"]}, {"Name": "Covington", "CenterLatitude": 39.08, "CenterLongitude": -84.51, "RadiusKm": 15, "PostalCodePatterns": ["410##"]}], "Counties": [], "PostalCodePatterns": ["400##", "401##", "402##", "403##", "404##", "405##", "406##", "407##", "408##", "409##", "410##", "411##", "412##", "413##", "414##", "415##", "416##", "417##", "418##", "420##", "421##", "422##", "423##", "424##"], "CenterLatitude": 37.839333, "CenterLongitude": -84.27, "RadiusKm": 200}, {"Code": "LA", "Name": "Louisiana", "Cities": [{"Name": "New Orleans", "CenterLatitude": 29.95465, "CenterLongitude": -90.07507, "RadiusKm": 50, "PostalCodePatterns": ["701##"]}, {"Name": "Baton Rouge", "CenterLatitude": 30.471165, "CenterLongitude": -91.147385, "RadiusKm": 30, "PostalCodePatterns": ["708##"]}, {"Name": "Shreveport", "CenterLatitude": 32.52515, "CenterLongitude": -93.75018, "RadiusKm": 25, "PostalCodePatterns": ["711##"]}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 29.984, "CenterLongitude": -90.153, "RadiusKm": 20, "PostalCodePatterns": ["700##"]}, {"Name": "Lafayette", "CenterLatitude": 30.216667, "CenterLongitude": -92.033333, "RadiusKm": 20, "PostalCodePatterns": ["705##"]}, {"Name": "Lake Charles", "CenterLatitude": 30.212942, "CenterLongitude": -93.21891, "RadiusKm": 15, "PostalCodePatterns": ["706##"]}, {"Name": "Bossier City", "CenterLatitude": 32.516, "CenterLongitude": -93.732, "RadiusKm": 15, "PostalCodePatterns": ["711##"]}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 29.994, "CenterLongitude": -90.242, "RadiusKm": 10, "PostalCodePatterns": ["700##"]}, {"Name": "<PERSON>", "CenterLatitude": 32.509311, "CenterLongitude": -92.119301, "RadiusKm": 10, "PostalCodePatterns": ["712##"]}, {"Name": "Alexandria", "CenterLatitude": 31.284788, "CenterLongitude": -92.471176, "RadiusKm": 10, "PostalCodePatterns": ["713##"]}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 29.596, "CenterLongitude": -90.72, "RadiusKm": 10, "PostalCodePatterns": ["703##"]}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 29.899, "CenterLongitude": -90.1, "RadiusKm": 5, "PostalCodePatterns": ["700##"]}], "Counties": [], "PostalCodePatterns": ["700##", "701##", "703##", "704##", "705##", "706##", "707##", "708##", "710##", "711##", "712##", "713##", "714##"], "CenterLatitude": 30.9668, "CenterLongitude": -91.8018, "RadiusKm": 200}, {"Code": "MA", "Name": "Massachusetts", "Cities": [{"Name": "Boston", "CenterLatitude": 42.35843, "CenterLongitude": -71.05977, "RadiusKm": 40, "PostalCodePatterns": ["021##", "022##"]}, {"Name": "Worcester", "CenterLatitude": 42.271389, "CenterLongitude": -71.798889, "RadiusKm": 25, "PostalCodePatterns": ["016##"]}, {"Name": "Springfield", "CenterLatitude": 42.101391, "CenterLongitude": -72.590279, "RadiusKm": 20, "PostalCodePatterns": ["011##"]}, {"Name": "Lowell", "CenterLatitude": 42.639444, "CenterLongitude": -71.314722, "RadiusKm": 15, "PostalCodePatterns": ["018##"]}, {"Name": "Cambridge", "CenterLatitude": 42.373611, "CenterLongitude": -71.110558, "RadiusKm": 15, "PostalCodePatterns": ["021##"]}, {"Name": "New Bedford", "CenterLatitude": 41.638409, "CenterLongitude": -70.941208, "RadiusKm": 10, "PostalCodePatterns": ["027##"]}, {"Name": "Brockton", "CenterLatitude": 42.083431, "CenterLongitude": -71.018379, "RadiusKm": 10, "PostalCodePatterns": ["023##"]}, {"Name": "Quincy", "CenterLatitude": 42.25288, "CenterLongitude": -71.00227, "RadiusKm": 10, "PostalCodePatterns": ["021##"]}, {"Name": "<PERSON>", "CenterLatitude": 42.46676, "CenterLongitude": -70.94949, "RadiusKm": 10, "PostalCodePatterns": ["019##"]}, {"Name": "Fall River", "CenterLatitude": 41.70149, "CenterLongitude": -71.15505, "RadiusKm": 10, "PostalCodePatterns": ["027##"]}], "Counties": [], "PostalCodePatterns": ["010##", "011##", "012##", "013##", "014##", "015##", "016##", "017##", "018##", "019##", "020##", "021##", "022##", "023##", "024##", "025##", "026##", "027##"], "CenterLatitude": 42.3695, "CenterLongitude": -71.9481, "RadiusKm": 80}, {"Code": "MD", "Name": "Maryland", "Cities": [{"Name": "Baltimore", "CenterLatitude": 39.299236, "CenterLongitude": -76.609383, "RadiusKm": 40, "PostalCodePatterns": ["212##"]}, {"Name": "Columbia", "CenterLatitude": 39.24, "CenterLongitude": -76.839, "RadiusKm": 20, "PostalCodePatterns": ["210##"]}, {"Name": "Germantown", "CenterLatitude": 39.173, "CenterLongitude": -77.272, "RadiusKm": 15, "PostalCodePatterns": ["208##"]}, {"Name": "Silver Spring", "CenterLatitude": 38.991, "CenterLongitude": -77.026, "RadiusKm": 20, "PostalCodePatterns": ["209##"]}, {"Name": "<PERSON>", "CenterLatitude": 39.414, "CenterLongitude": -77.411, "RadiusKm": 15, "PostalCodePatterns": ["217##"]}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 38.625, "CenterLongitude": -76.939, "RadiusKm": 10, "PostalCodePatterns": ["206##"]}, {"Name": "<PERSON>ie", "CenterLatitude": 39.163, "CenterLongitude": -76.625, "RadiusKm": 10, "PostalCodePatterns": ["210##"]}, {"Name": "Gaithersburg", "CenterLatitude": 39.143, "CenterLongitude": -77.201, "RadiusKm": 15, "PostalCodePatterns": ["208##"]}, {"Name": "Rockville", "CenterLatitude": 39.084, "CenterLongitude": -77.153, "RadiusKm": 15, "PostalCodePatterns": ["208##"]}, {"Name": "Ellicott City", "CenterLatitude": 39.267, "CenterLongitude": -76.798, "RadiusKm": 10, "PostalCodePatterns": ["210##"]}, {"Name": "Annapolis", "CenterLatitude": 38.9789, "CenterLongitude": -76.4923, "RadiusKm": 10, "PostalCodePatterns": ["214##"]}, {"Name": "Salisbury", "CenterLatitude": 38.3607, "CenterLongitude": -75.5994, "RadiusKm": 10, "PostalCodePatterns": ["218##"]}, {"Name": "Hagerstown", "CenterLatitude": 39.6418, "CenterLongitude": -77.7208, "RadiusKm": 10, "PostalCodePatterns": ["217##"]}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 39.3943, "CenterLongitude": -76.6019, "RadiusKm": 10, "PostalCodePatterns": ["212##"]}, {"Name": "Bethesda", "CenterLatitude": 38.9847, "CenterLongitude": -77.0947, "RadiusKm": 10, "PostalCodePatterns": ["208##"]}], "Counties": [], "PostalCodePatterns": ["206##", "207##", "208##", "209##", "210##", "211##", "212##", "214##", "215##", "216##", "217##", "218##", "219##"], "CenterLatitude": 39.055, "CenterLongitude": -76.790833, "RadiusKm": 100}, {"Code": "ME", "Name": "Maine", "Cities": [{"Name": "Portland", "CenterLatitude": 43.657, "CenterLongitude": -70.259, "RadiusKm": 30}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 44.1, "CenterLongitude": -70.215, "RadiusKm": 20}, {"Name": "Bangor", "CenterLatitude": 44.799, "CenterLongitude": -68.773, "RadiusKm": 20}, {"Name": "South Portland", "CenterLatitude": 43.647, "CenterLongitude": -70.257, "RadiusKm": 15}, {"Name": "Auburn", "CenterLatitude": 44.09, "CenterLongitude": -70.25, "RadiusKm": 15}, {"Name": "Augusta", "CenterLatitude": 44.33, "CenterLongitude": -69.73, "RadiusKm": 15}, {"Name": "Biddeford", "CenterLatitude": 43.492, "CenterLongitude": -70.454, "RadiusKm": 10}, {"Name": "Saco", "CenterLatitude": 43.509, "CenterLongitude": -70.445, "RadiusKm": 10}, {"Name": "Westbrook", "CenterLatitude": 43.677, "CenterLongitude": -70.388, "RadiusKm": 10}, {"Name": "Presque Isle", "CenterLatitude": 46.68, "CenterLongitude": -68.018, "RadiusKm": 5}, {"Name": "Caribou", "CenterLatitude": 46.86, "CenterLongitude": -68.015, "RadiusKm": 5}, {"Name": "Ellsworth", "CenterLatitude": 44.54, "CenterLongitude": -68.424, "RadiusKm": 5}], "Counties": [], "PostalCodePatterns": ["039##", "040##", "041##", "042##", "043##", "044##", "045##", "046##", "047##", "048##", "049##"], "CenterLatitude": 45.253333, "CenterLongitude": -69.233333, "RadiusKm": 150}, {"Code": "MI", "Name": "Michigan", "Cities": [{"Name": "Detroit", "CenterLatitude": 42.3314, "CenterLongitude": -83.0458, "RadiusKm": 50}, {"Name": "Grand Rapids", "CenterLatitude": 42.963795, "CenterLongitude": -85.670006, "RadiusKm": 30}, {"Name": "<PERSON>", "CenterLatitude": 42.5, "CenterLongitude": -83.03, "RadiusKm": 20}, {"Name": "Sterling Heights", "CenterLatitude": 42.58, "CenterLongitude": -83.03, "RadiusKm": 20}, {"Name": "Ann Arbor", "CenterLatitude": 42.2808, "CenterLongitude": -83.743, "RadiusKm": 15}, {"Name": "Lansing", "CenterLatitude": 42.7325, "CenterLongitude": -84.5553, "RadiusKm": 15}, {"Name": "Flint", "CenterLatitude": 43.013, "CenterLongitude": -83.687, "RadiusKm": 15}, {"Name": "Dearborn", "CenterLatitude": 42.322, "CenterLongitude": -83.176, "RadiusKm": 10}, {"Name": "Livonia", "CenterLatitude": 42.368, "CenterLongitude": -83.353, "RadiusKm": 10}, {"Name": "Troy", "CenterLatitude": 42.606, "CenterLongitude": -83.15, "RadiusKm": 10}], "Counties": [], "PostalCodePatterns": ["480##", "481##", "482##", "483##", "484##", "485##", "486##", "487##", "488##", "489##", "490##", "491##", "492##", "493##", "494##", "495##", "496##", "497##", "498##", "499##"], "CenterLatitude": 44.314844, "CenterLongitude": -85.602364, "RadiusKm": 250}, {"Code": "MN", "Name": "Minnesota", "Cities": [{"Name": "Minneapolis", "CenterLatitude": 44.9778, "CenterLongitude": -93.265, "RadiusKm": 40}, {"Name": "Saint Paul", "CenterLatitude": 44.9537, "CenterLongitude": -93.09, "RadiusKm": 30}, {"Name": "Rochester", "CenterLatitude": 44.0232, "CenterLongitude": -92.4629, "RadiusKm": 20}, {"Name": "Duluth", "CenterLatitude": 46.7867, "CenterLongitude": -92.1005, "RadiusKm": 20}, {"Name": "Bloomington", "CenterLatitude": 44.8408, "CenterLongitude": -93.2983, "RadiusKm": 15}], "Counties": [], "PostalCodePatterns": ["550##", "551##", "553##", "554##", "555##", "556##", "557##", "558##", "559##", "560##", "561##", "562##", "563##", "564##", "565##", "566##", "567##"], "CenterLatitude": 46.7296, "CenterLongitude": -94.6859, "RadiusKm": 300}, {"Code": "MO", "Name": "Missouri", "Cities": [{"Name": "Kansas City", "CenterLatitude": 39.0997, "CenterLongitude": -94.5786, "RadiusKm": 50}, {"Name": "St. Louis", "CenterLatitude": 38.627, "CenterLongitude": -90.1994, "RadiusKm": 40}, {"Name": "Springfield", "CenterLatitude": 37.2153, "CenterLongitude": -93.2983, "RadiusKm": 25}, {"Name": "Columbia", "CenterLatitude": 38.9517, "CenterLongitude": -92.3341, "RadiusKm": 20}, {"Name": "Independence", "CenterLatitude": 39.0911, "CenterLongitude": -94.4158, "RadiusKm": 20}, {"Name": "Lee's Summit", "CenterLatitude": 38.9108, "CenterLongitude": -94.383, "RadiusKm": 15}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 38.7934, "CenterLongitude": -90.694, "RadiusKm": 15}, {"Name": "St. Joseph", "CenterLatitude": 39.7686, "CenterLongitude": -94.8463, "RadiusKm": 15}, {"Name": "St. Charles", "CenterLatitude": 38.7867, "CenterLongitude": -90.4956, "RadiusKm": 15}, {"Name": "Blue Springs", "CenterLatitude": 39.0261, "CenterLongitude": -94.2866, "RadiusKm": 10}], "Counties": [], "PostalCodePatterns": ["630##", "631##", "633##", "634##", "635##", "636##", "637##", "638##", "639##", "640##", "641##", "644##", "645##", "646##", "647##", "648##", "649##", "650##", "651##", "652##", "653##", "654##", "655##", "656##", "657##", "658##"], "CenterLatitude": 38.5739, "CenterLongitude": -92.6038, "RadiusKm": 250}, {"Code": "MS", "Name": "Mississippi", "Cities": [{"Name": "<PERSON>", "CenterLatitude": 32.2988, "CenterLongitude": -90.1848, "RadiusKm": 30}, {"Name": "Gulfport", "CenterLatitude": 30.3674, "CenterLongitude": -89.0928, "RadiusKm": 20}, {"Name": "Southaven", "CenterLatitude": 34.9865, "CenterLongitude": -89.9862, "RadiusKm": 15}, {"Name": "Hattiesburg", "CenterLatitude": 31.3271, "CenterLongitude": -89.2912, "RadiusKm": 15}, {"Name": "Biloxi", "CenterLatitude": 30.3963, "CenterLongitude": -88.8853, "RadiusKm": 15}, {"Name": "Meridian", "CenterLatitude": 32.3646, "CenterLongitude": -88.7039, "RadiusKm": 10}, {"Name": "Tupelo", "CenterLatitude": 34.2576, "CenterLongitude": -88.7045, "RadiusKm": 10}, {"Name": "Greenville", "CenterLatitude": 33.4082, "CenterLongitude": -91.0665, "RadiusKm": 10}, {"Name": "Olive Branch", "CenterLatitude": 34.9409, "CenterLongitude": -89.8162, "RadiusKm": 10}, {"Name": "Horn Lake", "CenterLatitude": 34.9562, "CenterLongitude": -90.0365, "RadiusKm": 10}], "Counties": [], "PostalCodePatterns": ["386##", "387##", "388##", "389##", "390##", "391##", "392##", "393##", "394##", "395##", "396##", "397##"], "CenterLatitude": 32.77, "CenterLongitude": -89.68, "RadiusKm": 180}, {"Code": "MT", "Name": "Montana", "Cities": [{"Name": "<PERSON><PERSON>", "CenterLatitude": 45.7833, "CenterLongitude": -108.5, "RadiusKm": 25}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 46.8721, "CenterLongitude": -113.994, "RadiusKm": 20}, {"Name": "Great Falls", "CenterLatitude": 47.503, "CenterLongitude": -111.3, "RadiusKm": 20}, {"Name": "Bozeman", "CenterLatitude": 45.676, "CenterLongitude": -111.042, "RadiusKm": 15}, {"Name": "Butte", "CenterLatitude": 46, "CenterLongitude": -112.5333, "RadiusKm": 15}, {"Name": "Helena", "CenterLatitude": 46.5928, "CenterLongitude": -112.021, "RadiusKm": 10}, {"Name": "Kalispell", "CenterLatitude": 48.196, "CenterLongitude": -114.313, "RadiusKm": 10}, {"Name": "Anaconda", "CenterLatitude": 46.1333, "CenterLongitude": -112.95, "RadiusKm": 10}, {"Name": "Miles City", "CenterLatitude": 46.4083, "CenterLongitude": -105.84, "RadiusKm": 10}, {"Name": "Livingston", "CenterLatitude": 45.66, "CenterLongitude": -110.56, "RadiusKm": 10}], "Counties": [], "PostalCodePatterns": ["590##", "591##", "592##", "593##", "594##", "595##", "596##", "597##", "598##", "599##"], "CenterLatitude": 47.031667, "CenterLongitude": -109.638333, "RadiusKm": 400}, {"Code": "NC", "Name": "North Carolina", "Cities": [{"Name": "Charlotte", "CenterLatitude": 35.2271, "CenterLongitude": -80.8431, "RadiusKm": 40}, {"Name": "Raleigh", "CenterLatitude": 35.7796, "CenterLongitude": -78.6382, "RadiusKm": 30}, {"Name": "Greensboro", "CenterLatitude": 36.0726, "CenterLongitude": -79.792, "RadiusKm": 25}, {"Name": "Durham", "CenterLatitude": 35.994, "CenterLongitude": -78.8986, "RadiusKm": 20}, {"Name": "Winston-Salem", "CenterLatitude": 36.0999, "CenterLongitude": -80.2442, "RadiusKm": 20}], "Counties": [], "PostalCodePatterns": ["270##", "271##", "272##", "273##", "274##", "275##", "276##", "277##", "278##", "279##", "280##", "281##", "282##", "283##", "284##", "285##", "286##", "287##", "288##", "289##"], "CenterLatitude": 35.7596, "CenterLongitude": -79.0193, "RadiusKm": 250}, {"Code": "ND", "Name": "North Dakota", "Cities": [{"Name": "Fargo", "CenterLatitude": 46.8772, "CenterLongitude": -96.7898, "RadiusKm": 25}, {"Name": "Bismarck", "CenterLatitude": 46.8083, "CenterLongitude": -100.7837, "RadiusKm": 20}, {"Name": "Grand Forks", "CenterLatitude": 47.9253, "CenterLongitude": -97.0329, "RadiusKm": 15}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 48.2325, "CenterLongitude": -101.2967, "RadiusKm": 15}, {"Name": "West Fargo", "CenterLatitude": 46.8772, "CenterLongitude": -96.8973, "RadiusKm": 10}, {"Name": "Mandan", "CenterLatitude": 46.8264, "CenterLongitude": -100.9035, "RadiusKm": 10}, {"Name": "<PERSON>", "CenterLatitude": 46.88, "CenterLongitude": -102.794, "RadiusKm": 10}, {"Name": "Jamestown", "CenterLatitude": 46.9, "CenterLongitude": -98.7, "RadiusKm": 10}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 48.15, "CenterLongitude": -103.6333, "RadiusKm": 10}], "Counties": [], "PostalCodePatterns": ["580##", "581##", "582##", "583##", "584##", "585##", "586##", "587##"], "CenterLatitude": 47.650589, "CenterLongitude": -100.437012, "RadiusKm": 250}, {"Code": "NE", "Name": "Nebraska", "Cities": [{"Name": "Omaha", "CenterLatitude": 41.2572, "CenterLongitude": -95.995, "RadiusKm": 40}, {"Name": "Lincoln", "CenterLatitude": 40.8136, "CenterLongitude": -96.7026, "RadiusKm": 30}, {"Name": "Bellevue", "CenterLatitude": 41.15, "CenterLongitude": -95.93, "RadiusKm": 15}, {"Name": "Grand Island", "CenterLatitude": 40.9264, "CenterLongitude": -98.369, "RadiusKm": 15}, {"Name": "Kearney", "CenterLatitude": 40.6997, "CenterLongitude": -99.0818, "RadiusKm": 10}, {"Name": "Fremont", "CenterLatitude": 41.4464, "CenterLongitude": -96.4892, "RadiusKm": 10}, {"Name": "Hastings", "CenterLatitude": 40.5875, "CenterLongitude": -98.3981, "RadiusKm": 10}, {"Name": "North Platte", "CenterLatitude": 41.1236, "CenterLongitude": -100.7654, "RadiusKm": 10}, {"Name": "Norfolk", "CenterLatitude": 42.0389, "CenterLongitude": -97.417, "RadiusKm": 10}, {"Name": "Columbus", "CenterLatitude": 41.4292, "CenterLongitude": -97.3639, "RadiusKm": 10}], "Counties": [], "PostalCodePatterns": ["680##", "681##", "683##", "684##", "685##", "686##", "687##", "688##", "689##", "690##", "691##", "692##", "693##"], "CenterLatitude": 41.525, "CenterLongitude": -99.861667, "RadiusKm": 250}, {"Code": "NH", "Name": "New Hampshire", "Cities": [{"Name": "Manchester", "CenterLatitude": 42.9956, "CenterLongitude": -71.4547, "RadiusKm": 20}, {"Name": "Nashua", "CenterLatitude": 42.765, "CenterLongitude": -71.4667, "RadiusKm": 15}, {"Name": "Concord", "CenterLatitude": 43.208, "CenterLongitude": -71.5375, "RadiusKm": 15}, {"Name": "Derry", "CenterLatitude": 42.89, "CenterLongitude": -71.29, "RadiusKm": 10}, {"Name": "Dover", "CenterLatitude": 43.198, "CenterLongitude": -70.874, "RadiusKm": 10}, {"Name": "Rochester", "CenterLatitude": 43.305, "CenterLongitude": -70.975, "RadiusKm": 10}, {"Name": "Salem", "CenterLatitude": 42.78, "CenterLongitude": -71.2, "RadiusKm": 10}, {"Name": "Me<PERSON>mack", "CenterLatitude": 42.86, "CenterLongitude": -71.49, "RadiusKm": 10}, {"Name": "Londonderry", "CenterLatitude": 42.88, "CenterLongitude": -71.35, "RadiusKm": 10}, {"Name": "<PERSON>", "CenterLatitude": 42.76, "CenterLongitude": -71.43, "RadiusKm": 10}], "Counties": [], "PostalCodePatterns": ["030##", "031##", "032##", "033##", "034##", "035##", "037##", "038##"], "CenterLatitude": 43.641667, "CenterLongitude": -71.571667, "RadiusKm": 80}, {"Code": "NJ", "Name": "New Jersey", "Cities": [{"Name": "Newark", "CenterLatitude": 40.7357, "CenterLongitude": -74.1724, "RadiusKm": 30}, {"Name": "Jersey City", "CenterLatitude": 40.7282, "CenterLongitude": -74.0776, "RadiusKm": 25}, {"Name": "Paterson", "CenterLatitude": 40.9168, "CenterLongitude": -74.1718, "RadiusKm": 20}, {"Name": "<PERSON>", "CenterLatitude": 40.6643, "CenterLongitude": -74.2113, "RadiusKm": 20}, {"Name": "Toms River", "CenterLatitude": 39.95, "CenterLongitude": -74.19, "RadiusKm": 15}, {"Name": "Clifton", "CenterLatitude": 40.85, "CenterLongitude": -74.16, "RadiusKm": 15}, {"Name": "Trenton", "CenterLatitude": 40.22, "CenterLongitude": -74.76, "RadiusKm": 15}, {"Name": "Camden", "CenterLatitude": 39.93, "CenterLongitude": -75.11, "RadiusKm": 15}, {"Name": "Passaic", "CenterLatitude": 40.86, "CenterLongitude": -74.12, "RadiusKm": 10}, {"Name": "Union City", "CenterLatitude": 40.78, "CenterLongitude": -74.03, "RadiusKm": 10}], "Counties": [], "PostalCodePatterns": ["070##", "071##", "072##", "073##", "074##", "075##", "076##", "077##", "078##", "079##", "080##", "081##", "082##", "083##", "084##", "085##", "086##", "087##", "088##", "089##"], "CenterLatitude": 40.190833, "CenterLongitude": -74.672778, "RadiusKm": 80}, {"Code": "NM", "Name": "New Mexico", "Cities": [{"Name": "Albuquerque", "CenterLatitude": 35.0844, "CenterLongitude": -106.6504, "RadiusKm": 40}, {"Name": "Las Cruces", "CenterLatitude": 32.3199, "CenterLongitude": -106.763, "RadiusKm": 25}, {"Name": "Rio Rancho", "CenterLatitude": 35.23, "CenterLongitude": -106.67, "RadiusKm": 20}, {"Name": "Santa Fe", "CenterLatitude": 35.687, "CenterLongitude": -105.937, "RadiusKm": 20}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 33.3942, "CenterLongitude": -104.523, "RadiusKm": 15}, {"Name": "Farmington", "CenterLatitude": 36.7282, "CenterLongitude": -108.2187, "RadiusKm": 15}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 34.4, "CenterLongitude": -103.2, "RadiusKm": 10}, {"Name": "<PERSON>", "CenterLatitude": 32.7, "CenterLongitude": -103.13, "RadiusKm": 10}, {"Name": "Alamogordo", "CenterLatitude": 32.89, "CenterLongitude": -105.96, "RadiusKm": 10}, {"Name": "Carlsbad", "CenterLatitude": 32.42, "CenterLongitude": -104.23, "RadiusKm": 10}], "Counties": [], "PostalCodePatterns": ["870##", "871##", "873##", "874##", "875##", "877##", "878##", "879##", "880##", "881##", "882##", "883##", "884##"], "CenterLatitude": 34.501667, "CenterLongitude": -106.111667, "RadiusKm": 300}, {"Code": "NV", "Name": "Nevada", "Cities": [{"Name": "Las Vegas", "CenterLatitude": 36.1699, "CenterLongitude": -115.1398, "RadiusKm": 50}, {"Name": "<PERSON>", "CenterLatitude": 36.0396, "CenterLongitude": -115.0818, "RadiusKm": 30}, {"Name": "Reno", "CenterLatitude": 39.5296, "CenterLongitude": -119.8138, "RadiusKm": 25}, {"Name": "North Las Vegas", "CenterLatitude": 36.19, "CenterLongitude": -115.11, "RadiusKm": 20}, {"Name": "Sparks", "CenterLatitude": 39.53, "CenterLongitude": -119.75, "RadiusKm": 15}, {"Name": "Carson City", "CenterLatitude": 39.1638, "CenterLongitude": -119.7674, "RadiusKm": 15}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 39.6, "CenterLongitude": -119.25, "RadiusKm": 10}, {"Name": "Elko", "CenterLatitude": 40.83, "CenterLongitude": -115.76, "RadiusKm": 10}, {"Name": "Mesquite", "CenterLatitude": 36.8, "CenterLongitude": -114.07, "RadiusKm": 10}, {"Name": "Boulder City", "CenterLatitude": 35.93, "CenterLongitude": -114.83, "RadiusKm": 10}], "Counties": [], "PostalCodePatterns": ["890##", "891##", "893##", "894##", "895##", "897##", "898##"], "CenterLatitude": 39.305, "CenterLongitude": -116.632222, "RadiusKm": 300}, {"Code": "NY", "Name": "New York", "Cities": [{"Name": "New York City", "CenterLatitude": 40.7128, "CenterLongitude": -74.006, "RadiusKm": 70}, {"Name": "Buffalo", "CenterLatitude": 42.8864, "CenterLongitude": -78.8784, "RadiusKm": 30}, {"Name": "Rochester", "CenterLatitude": 43.1566, "CenterLongitude": -77.6088, "RadiusKm": 25}, {"Name": "Yonkers", "CenterLatitude": 40.9312, "CenterLongitude": -73.8256, "RadiusKm": 20}, {"Name": "Syracuse", "CenterLatitude": 43.0481, "CenterLongitude": -76.1474, "RadiusKm": 20}, {"Name": "Albany", "CenterLatitude": 42.6526, "CenterLongitude": -73.7562, "RadiusKm": 15}, {"Name": "New Rochelle", "CenterLatitude": 40.91, "CenterLongitude": -73.78, "RadiusKm": 15}, {"Name": "Mount Vernon", "CenterLatitude": 40.91, "CenterLongitude": -73.83, "RadiusKm": 10}, {"Name": "Schenectady", "CenterLatitude": 42.81, "CenterLongitude": -73.94, "RadiusKm": 10}, {"Name": "Utica", "CenterLatitude": 43.1, "CenterLongitude": -75.23, "RadiusKm": 10}], "Counties": ["New York County", "Kings County", "Bronx County"], "PostalCodePatterns": ["100##", "101##", "102##", "103##", "104##", "105##", "106##", "107##", "108##", "109##", "110##", "111##", "112##", "113##", "114##", "115##", "116##", "117##", "118##", "119##", "120##", "121##", "122##", "123##", "124##", "125##", "126##", "127##", "128##", "129##", "130##", "131##", "132##", "133##", "134##", "135##", "136##", "137##", "138##", "139##", "140##", "141##", "142##", "143##", "144##", "145##", "146##", "147##", "148##", "149##"], "CenterLatitude": 43, "CenterLongitude": -75, "RadiusKm": 200}, {"Code": "OH", "Name": "Ohio", "Cities": [{"Name": "Columbus", "CenterLatitude": 39.9612, "CenterLongitude": -82.9988, "RadiusKm": 40}, {"Name": "Cleveland", "CenterLatitude": 41.4993, "CenterLongitude": -81.6944, "RadiusKm": 35}, {"Name": "Cincinnati", "CenterLatitude": 39.1031, "CenterLongitude": -84.512, "RadiusKm": 30}, {"Name": "Toledo", "CenterLatitude": 41.6639, "CenterLongitude": -83.5552, "RadiusKm": 25}, {"Name": "Akron", "CenterLatitude": 41.0814, "CenterLongitude": -81.519, "RadiusKm": 20}, {"Name": "Dayton", "CenterLatitude": 39.7589, "CenterLongitude": -84.1916, "RadiusKm": 20}, {"Name": "Parma", "CenterLatitude": 41.3833, "CenterLongitude": -81.72, "RadiusKm": 15}, {"Name": "Canton", "CenterLatitude": 40.8, "CenterLongitude": -81.37, "RadiusKm": 15}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 41.45, "CenterLongitude": -82.18, "RadiusKm": 10}, {"Name": "<PERSON>", "CenterLatitude": 39.39, "CenterLongitude": -84.56, "RadiusKm": 10}], "Counties": [], "PostalCodePatterns": ["430##", "431##", "432##", "433##", "434##", "435##", "436##", "437##", "438##", "439##", "440##", "441##", "442##", "443##", "444##", "445##", "446##", "447##", "448##", "449##", "450##", "451##", "452##", "453##", "454##", "456##", "457##", "458##"], "CenterLatitude": 40.367474, "CenterLongitude": -82.996216, "RadiusKm": 200}, {"Code": "OK", "Name": "Oklahoma", "Cities": [{"Name": "Oklahoma City", "CenterLatitude": 35.4676, "CenterLongitude": -97.5164, "RadiusKm": 50}, {"Name": "Tulsa", "CenterLatitude": 36.154, "CenterLongitude": -95.9928, "RadiusKm": 40}, {"Name": "<PERSON>", "CenterLatitude": 35.2226, "CenterLongitude": -97.4395, "RadiusKm": 20}, {"Name": "Broken Arrow", "CenterLatitude": 36.07, "CenterLongitude": -95.78, "RadiusKm": 20}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 34.6, "CenterLongitude": -98.4, "RadiusKm": 15}, {"Name": "<PERSON>", "CenterLatitude": 35.65, "CenterLongitude": -97.48, "RadiusKm": 15}, {"Name": "<PERSON>", "CenterLatitude": 35.33, "CenterLongitude": -97.49, "RadiusKm": 15}, {"Name": "Midwest City", "CenterLatitude": 35.42, "CenterLongitude": -97.38, "RadiusKm": 10}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 36.4, "CenterLongitude": -97.88, "RadiusKm": 10}, {"Name": "Stillwater", "CenterLatitude": 36.11, "CenterLongitude": -97.06, "RadiusKm": 10}], "Counties": [], "PostalCodePatterns": ["730##", "731##", "734##", "735##", "736##", "737##", "738##", "739##", "740##", "741##", "743##", "744##", "745##", "746##", "747##", "748##", "749##"], "CenterLatitude": 35.536667, "CenterLongitude": -97.66, "RadiusKm": 250}, {"Code": "OR", "Name": "Oregon", "Cities": [{"Name": "Portland", "CenterLatitude": 45.5231, "CenterLongitude": -122.6765, "RadiusKm": 40}, {"Name": "Salem", "CenterLatitude": 44.9429, "CenterLongitude": -123.0351, "RadiusKm": 25}, {"Name": "Eugene", "CenterLatitude": 44.0521, "CenterLongitude": -123.0729, "RadiusKm": 25}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 45.5, "CenterLongitude": -122.43, "RadiusKm": 15}, {"Name": "Hillsboro", "CenterLatitude": 45.52, "CenterLongitude": -122.98, "RadiusKm": 15}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 45.48, "CenterLongitude": -122.8, "RadiusKm": 15}, {"Name": "Bend", "CenterLatitude": 44.0582, "CenterLongitude": -121.3153, "RadiusKm": 20}, {"Name": "Medford", "CenterLatitude": 42.3265, "CenterLongitude": -122.8756, "RadiusKm": 15}, {"Name": "Springfield", "CenterLatitude": 44.05, "CenterLongitude": -123.02, "RadiusKm": 10}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 44.56, "CenterLongitude": -123.26, "RadiusKm": 10}], "Counties": [], "PostalCodePatterns": ["970##", "971##", "972##", "973##", "974##", "975##", "976##", "977##", "978##", "979##"], "CenterLatitude": 44, "CenterLongitude": -120.5, "RadiusKm": 300}, {"Code": "PA", "Name": "Pennsylvania", "Cities": [{"Name": "Philadelphia", "CenterLatitude": 39.9526, "CenterLongitude": -75.1652, "RadiusKm": 50}, {"Name": "Pittsburgh", "CenterLatitude": 40.4406, "CenterLongitude": -79.9959, "RadiusKm": 40}, {"Name": "Allentown", "CenterLatitude": 40.6084, "CenterLongitude": -75.4902, "RadiusKm": 20}, {"Name": "Erie", "CenterLatitude": 42.1292, "CenterLongitude": -80.0851, "RadiusKm": 20}, {"Name": "Reading", "CenterLatitude": 40.3359, "CenterLongitude": -75.9269, "RadiusKm": 15}, {"Name": "Scranton", "CenterLatitude": 41.4089, "CenterLongitude": -75.6624, "RadiusKm": 15}, {"Name": "Bethlehem", "CenterLatitude": 40.625, "CenterLongitude": -75.38, "RadiusKm": 15}, {"Name": "Lancaster", "CenterLatitude": 40.0379, "CenterLongitude": -76.3055, "RadiusKm": 15}, {"Name": "Harrisburg", "CenterLatitude": 40.2732, "CenterLongitude": -76.8867, "RadiusKm": 15}, {"Name": "York", "CenterLatitude": 39.9626, "CenterLongitude": -76.7277, "RadiusKm": 15}], "Counties": [], "PostalCodePatterns": ["150##", "151##", "152##", "153##", "154##", "155##", "156##", "157##", "158##", "159##", "160##", "161##", "162##", "163##", "164##", "165##", "166##", "167##", "168##", "169##", "170##", "171##", "172##", "173##", "174##", "175##", "176##", "177##", "178##", "179##", "180##", "181##", "182##", "183##", "184##", "185##", "186##", "187##", "188##", "189##", "190##", "191##", "193##", "194##", "195##", "196##"], "CenterLatitude": 40.896667, "CenterLongitude": -77.746667, "RadiusKm": 250}, {"Code": "RI", "Name": "Rhode Island", "Cities": [{"Name": "Providence", "CenterLatitude": 41.824, "CenterLongitude": -71.4128, "RadiusKm": 20}, {"Name": "Warwick", "CenterLatitude": 41.7, "CenterLongitude": -71.42, "RadiusKm": 15}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 41.78, "CenterLongitude": -71.43, "RadiusKm": 15}, {"Name": "Pawtucket", "CenterLatitude": 41.87, "CenterLongitude": -71.38, "RadiusKm": 10}, {"Name": "East Providence", "CenterLatitude": 41.81, "CenterLongitude": -71.36, "RadiusKm": 10}, {"Name": "Woonsocket", "CenterLatitude": 42, "CenterLongitude": -71.51, "RadiusKm": 10}, {"Name": "Coventry", "CenterLatitude": 41.68, "CenterLongitude": -71.68, "RadiusKm": 10}, {"Name": "North Providence", "CenterLatitude": 41.85, "CenterLongitude": -71.45, "RadiusKm": 10}, {"Name": "South Kingstown", "CenterLatitude": 41.45, "CenterLongitude": -71.53, "RadiusKm": 10}, {"Name": "Newport", "CenterLatitude": 41.49, "CenterLongitude": -71.31, "RadiusKm": 10}], "Counties": [], "PostalCodePatterns": ["028##", "029##"], "CenterLatitude": 41.671667, "CenterLongitude": -71.576667, "RadiusKm": 40}, {"Code": "SC", "Name": "South Carolina", "Cities": [{"Name": "Charleston", "CenterLatitude": 32.7765, "CenterLongitude": -79.9311, "RadiusKm": 30}, {"Name": "Columbia", "CenterLatitude": 34.0007, "CenterLongitude": -81.0348, "RadiusKm": 25}, {"Name": "North Charleston", "CenterLatitude": 32.85, "CenterLongitude": -80.02, "RadiusKm": 20}, {"Name": "Mount Pleasant", "CenterLatitude": 32.79, "CenterLongitude": -79.87, "RadiusKm": 15}, {"Name": "Rock Hill", "CenterLatitude": 34.92, "CenterLongitude": -81.02, "RadiusKm": 15}, {"Name": "Greenville", "CenterLatitude": 34.85, "CenterLongitude": -82.39, "RadiusKm": 20}, {"Name": "Summerville", "CenterLatitude": 33.02, "CenterLongitude": -80.18, "RadiusKm": 15}, {"Name": "Goose Creek", "CenterLatitude": 33, "CenterLongitude": -80.03, "RadiusKm": 10}, {"Name": "Hilton Head Island", "CenterLatitude": 32.2167, "CenterLongitude": -80.75, "RadiusKm": 15}, {"Name": "Florence", "CenterLatitude": 34.19, "CenterLongitude": -79.77, "RadiusKm": 10}], "Counties": [], "PostalCodePatterns": ["290##", "291##", "292##", "293##", "294##", "295##", "296##", "297##", "298##", "299##"], "CenterLatitude": 33.836082, "CenterLongitude": -81.163727, "RadiusKm": 150}, {"Code": "SD", "Name": "South Dakota", "Cities": [{"Name": "Sioux Falls", "CenterLatitude": 43.5446, "CenterLongitude": -96.7317, "RadiusKm": 30}, {"Name": "Rapid City", "CenterLatitude": 44.0805, "CenterLongitude": -103.231, "RadiusKm": 25}, {"Name": "Aberdeen", "CenterLatitude": 45.27, "CenterLongitude": -98.49, "RadiusKm": 15}, {"Name": "Brookings", "CenterLatitude": 44.31, "CenterLongitude": -96.79, "RadiusKm": 10}, {"Name": "Watertown", "CenterLatitude": 44.9, "CenterLongitude": -97.11, "RadiusKm": 10}, {"Name": "<PERSON>", "CenterLatitude": 44.37, "CenterLongitude": -100.35, "RadiusKm": 10}, {"Name": "Yankton", "CenterLatitude": 42.88, "CenterLongitude": -97.39, "RadiusKm": 10}, {"Name": "<PERSON>", "CenterLatitude": 43.55, "CenterLongitude": -98.03, "RadiusKm": 10}, {"Name": "Vermillion", "CenterLatitude": 42.78, "CenterLongitude": -96.93, "RadiusKm": 10}, {"Name": "Huron", "CenterLatitude": 44.37, "CenterLongitude": -98.22, "RadiusKm": 10}], "Counties": [], "PostalCodePatterns": ["570##", "571##", "572##", "573##", "574##", "575##", "576##", "577##"], "CenterLatitude": 44.401667, "CenterLongitude": -100.478333, "RadiusKm": 250}, {"Code": "TN", "Name": "Tennessee", "Cities": [{"Name": "Nashville", "CenterLatitude": 36.1627, "CenterLongitude": -86.7816, "RadiusKm": 40}, {"Name": "Memphis", "CenterLatitude": 35.1495, "CenterLongitude": -90.049, "RadiusKm": 40}, {"Name": "Knoxville", "CenterLatitude": 35.9606, "CenterLongitude": -83.9207, "RadiusKm": 30}, {"Name": "Chattanooga", "CenterLatitude": 35.0457, "CenterLongitude": -85.3097, "RadiusKm": 25}, {"Name": "Clarksville", "CenterLatitude": 36.5298, "CenterLongitude": -87.3595, "RadiusKm": 20}, {"Name": "<PERSON><PERSON><PERSON>sboro", "CenterLatitude": 35.845, "CenterLongitude": -86.39, "RadiusKm": 15}, {"Name": "<PERSON>", "CenterLatitude": 35.92, "CenterLongitude": -86.87, "RadiusKm": 15}, {"Name": "Johnson City", "CenterLatitude": 36.31, "CenterLongitude": -82.35, "RadiusKm": 15}, {"Name": "<PERSON>", "CenterLatitude": 35.61, "CenterLongitude": -88.82, "RadiusKm": 15}, {"Name": "Hendersonville", "CenterLatitude": 36.3, "CenterLongitude": -86.6, "RadiusKm": 10}], "Counties": [], "PostalCodePatterns": ["370##", "371##", "372##", "373##", "374##", "376##", "377##", "378##", "379##", "380##", "381##", "382##", "383##", "384##", "385##"], "CenterLatitude": 35.860119, "CenterLongitude": -86.660156, "RadiusKm": 200}, {"Code": "TX", "Name": "Texas", "Cities": [{"Name": "Houston", "CenterLatitude": 29.7604, "CenterLongitude": -95.3698, "RadiusKm": 70, "PostalCodePatterns": ["770##", "772##", "773##", "774##", "775##"]}, {"Name": "San Antonio", "CenterLatitude": 29.4241, "CenterLongitude": -98.4936, "RadiusKm": 60}, {"Name": "Dallas", "CenterLatitude": 32.7767, "CenterLongitude": -96.797, "RadiusKm": 60}, {"Name": "Austin", "CenterLatitude": 30.2672, "CenterLongitude": -97.7431, "RadiusKm": 50}, {"Name": "Fort Worth", "CenterLatitude": 32.7555, "CenterLongitude": -97.3308, "RadiusKm": 50}, {"Name": "El Paso", "CenterLatitude": 31.7619, "CenterLongitude": -106.485, "RadiusKm": 40}, {"Name": "Arlington", "CenterLatitude": 32.7357, "CenterLongitude": -97.1082, "RadiusKm": 30}, {"Name": "Corpus Christi", "CenterLatitude": 27.8, "CenterLongitude": -97.4, "RadiusKm": 30}, {"Name": "Plano", "CenterLatitude": 33.0198, "CenterLongitude": -96.6989, "RadiusKm": 25}, {"Name": "Laredo", "CenterLatitude": 27.5, "CenterLongitude": -99.5, "RadiusKm": 25}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 33.5779, "CenterLongitude": -101.8552, "RadiusKm": 25}, {"Name": "<PERSON>", "CenterLatitude": 32.91, "CenterLongitude": -96.64, "RadiusKm": 20}, {"Name": "<PERSON>", "CenterLatitude": 32.81, "CenterLongitude": -96.95, "RadiusKm": 20}, {"Name": "Amarillo", "CenterLatitude": 35.22, "CenterLongitude": -101.83, "RadiusKm": 20}, {"Name": "Grand Prairie", "CenterLatitude": 32.72, "CenterLongitude": -97.02, "RadiusKm": 20}, {"Name": "Brownsville", "CenterLatitude": 25.9, "CenterLongitude": -97.49, "RadiusKm": 20}, {"Name": "Pasadena", "CenterLatitude": 29.68, "CenterLongitude": -95.21, "RadiusKm": 15}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 31.11, "CenterLongitude": -97.73, "RadiusKm": 15}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CenterLatitude": 33.2, "CenterLongitude": -96.62, "RadiusKm": 15}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 33.15, "CenterLongitude": -96.82, "RadiusKm": 15}, {"Name": "<PERSON>", "CenterLatitude": 33.21, "CenterLongitude": -97.13, "RadiusKm": 15}, {"Name": "Waco", "CenterLatitude": 31.55, "CenterLongitude": -97.15, "RadiusKm": 15}, {"Name": "Midland", "CenterLatitude": 31.99, "CenterLongitude": -102.08, "RadiusKm": 15}, {"Name": "Odessa", "CenterLatitude": 31.84, "CenterLongitude": -102.37, "RadiusKm": 15}, {"Name": "Beaumont", "CenterLatitude": 30.08, "CenterLongitude": -94.1, "RadiusKm": 15}, {"Name": "Abilene", "CenterLatitude": 32.45, "CenterLongitude": -99.73, "RadiusKm": 15}, {"Name": "Round Rock", "CenterLatitude": 30.5, "CenterLongitude": -97.68, "RadiusKm": 15}, {"Name": "College Station", "CenterLatitude": 30.62, "CenterLongitude": -96.33, "RadiusKm": 15}, {"Name": "Pearland", "CenterLatitude": 29.55, "CenterLongitude": -95.28, "RadiusKm": 10}, {"Name": "<PERSON>", "CenterLatitude": 32.95, "CenterLongitude": -96.72, "RadiusKm": 10}, {"Name": "League City", "CenterLatitude": 29.5, "CenterLongitude": -95.09, "RadiusKm": 10}, {"Name": "<PERSON>", "CenterLatitude": 32.35, "CenterLongitude": -95.3, "RadiusKm": 10}, {"Name": "Lewisville", "CenterLatitude": 33.05, "CenterLongitude": -97, "RadiusKm": 10}, {"Name": "<PERSON>", "CenterLatitude": 33.1, "CenterLongitude": -96.67, "RadiusKm": 10}, {"Name": "Sugar Land", "CenterLatitude": 29.62, "CenterLongitude": -95.63, "RadiusKm": 10}, {"Name": "Edinburg", "CenterLatitude": 26.3, "CenterLongitude": -98.16, "RadiusKm": 10}, {"Name": "Mission", "CenterLatitude": 26.21, "CenterLongitude": -98.32, "RadiusKm": 10}, {"Name": "<PERSON>", "CenterLatitude": 30.67, "CenterLongitude": -96.37, "RadiusKm": 10}, {"Name": "Longview", "CenterLatitude": 32.5, "CenterLongitude": -94.75, "RadiusKm": 10}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 26.21, "CenterLongitude": -98.18, "RadiusKm": 10}, {"Name": "Baytown", "CenterLatitude": 29.75, "CenterLongitude": -95, "RadiusKm": 10}, {"Name": "Temple", "CenterLatitude": 31.09, "CenterLongitude": -97.35, "RadiusKm": 10}, {"Name": "Missouri City", "CenterLatitude": 29.58, "CenterLongitude": -95.53, "RadiusKm": 10}, {"Name": "Flower Mound", "CenterLatitude": 33, "CenterLongitude": -97.08, "RadiusKm": 10}, {"Name": "Harlingen", "CenterLatitude": 26.19, "CenterLongitude": -97.69, "RadiusKm": 10}, {"Name": "North Richland Hills", "CenterLatitude": 32.88, "CenterLongitude": -97.22, "RadiusKm": 10}, {"Name": "Victoria", "CenterLatitude": 28.8, "CenterLongitude": -97, "RadiusKm": 10}, {"Name": "New Braunfels", "CenterLatitude": 29.7, "CenterLongitude": -98.12, "RadiusKm": 10}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 30.3, "CenterLongitude": -95.45, "RadiusKm": 10}, {"Name": "San Angelo", "CenterLatitude": 31.46, "CenterLongitude": -100.44, "RadiusKm": 10}, {"Name": "Cedar Park", "CenterLatitude": 30.5, "CenterLongitude": -97.82, "RadiusKm": 10}, {"Name": "Mansfield", "CenterLatitude": 32.56, "CenterLongitude": -97.13, "RadiusKm": 10}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 32.9, "CenterLongitude": -96.57, "RadiusKm": 10}, {"Name": "Port Arthur", "CenterLatitude": 29.89, "CenterLongitude": -93.93, "RadiusKm": 10}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 32.85, "CenterLongitude": -97.08, "RadiusKm": 10}, {"Name": "Georgetown", "CenterLatitude": 30.63, "CenterLongitude": -97.68, "RadiusKm": 10}, {"Name": "Pflugerville", "CenterLatitude": 30.45, "CenterLongitude": -97.65, "RadiusKm": 10}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 32.58, "CenterLongitude": -96.87, "RadiusKm": 10}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "CenterLatitude": 32.93, "CenterLongitude": -97.08, "RadiusKm": 10}, {"Name": "Galveston", "CenterLatitude": 29.29, "CenterLongitude": -94.79, "RadiusKm": 10}, {"Name": "The Woodlands", "CenterLatitude": 30.16, "CenterLongitude": -95.46, "RadiusKm": 15}], "Counties": ["Harris County", "Dallas County", "Travis County"], "PostalCodePatterns": ["75###", "76###", "77###", "78###", "79###", "885##"], "CenterLatitude": 31, "CenterLongitude": -99, "RadiusKm": 500}, {"Code": "UT", "Name": "Utah", "Cities": [{"Name": "Salt Lake City", "CenterLatitude": 40.7608, "CenterLongitude": -111.891, "RadiusKm": 30}, {"Name": "West Valley City", "CenterLatitude": 40.6919, "CenterLongitude": -111.9944, "RadiusKm": 20}, {"Name": "Provo", "CenterLatitude": 40.233, "CenterLongitude": -111.6585, "RadiusKm": 20}, {"Name": "West Jordan", "CenterLatitude": 40.56, "CenterLongitude": -112, "RadiusKm": 15}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 40.29, "CenterLongitude": -111.7, "RadiusKm": 15}, {"Name": "<PERSON>", "CenterLatitude": 40.56, "CenterLongitude": -111.86, "RadiusKm": 15}, {"Name": "<PERSON>", "CenterLatitude": 41.223, "CenterLongitude": -111.9738, "RadiusKm": 15}, {"Name": "St. George", "CenterLatitude": 37.1, "CenterLongitude": -113.58, "RadiusKm": 15}, {"Name": "<PERSON>", "CenterLatitude": 41.06, "CenterLongitude": -111.97, "RadiusKm": 10}, {"Name": "Taylorsville", "CenterLatitude": 40.65, "CenterLongitude": -111.95, "RadiusKm": 10}], "Counties": [], "PostalCodePatterns": ["840##", "841##", "843##", "844##", "845##", "846##", "847##"], "CenterLatitude": 39.41922, "CenterLongitude": -111.950684, "RadiusKm": 200}, {"Code": "VA", "Name": "Virginia", "Cities": [{"Name": "Virginia Beach", "CenterLatitude": 36.8529, "CenterLongitude": -75.978, "RadiusKm": 40}, {"Name": "Chesapeake", "CenterLatitude": 36.765, "CenterLongitude": -76.2875, "RadiusKm": 30}, {"Name": "Norfolk", "CenterLatitude": 36.8508, "CenterLongitude": -76.2859, "RadiusKm": 25}, {"Name": "Richmond", "CenterLatitude": 37.5407, "CenterLongitude": -77.436, "RadiusKm": 30}, {"Name": "Newport News", "CenterLatitude": 37.07, "CenterLongitude": -76.41, "RadiusKm": 25}, {"Name": "Alexandria", "CenterLatitude": 38.8048, "CenterLongitude": -77.0469, "RadiusKm": 20}, {"Name": "<PERSON>", "CenterLatitude": 37.02, "CenterLongitude": -76.34, "RadiusKm": 20}, {"Name": "Roanoke", "CenterLatitude": 37.27, "CenterLongitude": -79.94, "RadiusKm": 20}, {"Name": "Portsmouth", "CenterLatitude": 36.83, "CenterLongitude": -76.35, "RadiusKm": 15}, {"Name": "Suffolk", "CenterLatitude": 36.73, "CenterLongitude": -76.58, "RadiusKm": 15}, {"Name": "Lynchburg", "CenterLatitude": 37.4, "CenterLongitude": -79.14, "RadiusKm": 15}, {"Name": "Harrisonburg", "CenterLatitude": 38.45, "CenterLongitude": -78.87, "RadiusKm": 10}, {"Name": "Leesburg", "CenterLatitude": 39.11, "CenterLongitude": -77.56, "RadiusKm": 10}, {"Name": "Manassas", "CenterLatitude": 38.75, "CenterLongitude": -77.47, "RadiusKm": 10}, {"Name": "Fredericksburg", "CenterLatitude": 38.3, "CenterLongitude": -77.46, "RadiusKm": 10}], "Counties": [], "PostalCodePatterns": ["201##", "220##", "221##", "222##", "223##", "224##", "225##", "226##", "227##", "228##", "229##", "230##", "231##", "232##", "233##", "234##", "235##", "236##", "237##", "238##", "239##", "240##", "241##", "242##", "243##", "244##", "245##", "246##"], "CenterLatitude": 37.51, "CenterLongitude": -78.625, "RadiusKm": 180}, {"Code": "VT", "Name": "Vermont", "Cities": [{"Name": "Burlington", "CenterLatitude": 44.4759, "CenterLongitude": -73.2121, "RadiusKm": 15}, {"Name": "South Burlington", "CenterLatitude": 44.46, "CenterLongitude": -73.17, "RadiusKm": 10}, {"Name": "Rutland", "CenterLatitude": 43.61, "CenterLongitude": -72.97, "RadiusKm": 10}, {"Name": "Essex Junction", "CenterLatitude": 44.49, "CenterLongitude": -73.1, "RadiusKm": 5}, {"Name": "Colchester", "CenterLatitude": 44.55, "CenterLongitude": -73.1, "RadiusKm": 10}, {"Name": "Bennington", "CenterLatitude": 42.88, "CenterLongitude": -73.19, "RadiusKm": 10}, {"Name": "Brattleboro", "CenterLatitude": 42.85, "CenterLongitude": -72.56, "RadiusKm": 10}, {"Name": "Montpelier", "CenterLatitude": 44.26, "CenterLongitude": -72.57, "RadiusKm": 5}, {"Name": "St. Albans", "CenterLatitude": 44.81, "CenterLongitude": -73.08, "RadiusKm": 5}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 44.2, "CenterLongitude": -72.5, "RadiusKm": 5}], "Counties": [], "PostalCodePatterns": ["050##", "051##", "052##", "053##", "054##", "056##", "057##", "058##", "059##"], "CenterLatitude": 43.926667, "CenterLongitude": -72.671667, "RadiusKm": 80}, {"Code": "WA", "Name": "Washington", "Cities": [{"Name": "Seattle", "CenterLatitude": 47.6062, "CenterLongitude": -122.3321, "RadiusKm": 50}, {"Name": "Spokane", "CenterLatitude": 47.6588, "CenterLongitude": -117.4265, "RadiusKm": 30}, {"Name": "Tacoma", "CenterLatitude": 47.2529, "CenterLongitude": -122.4443, "RadiusKm": 25}, {"Name": "Vancouver", "CenterLatitude": 45.6387, "CenterLongitude": -122.6615, "RadiusKm": 20}, {"Name": "Bellevue", "CenterLatitude": 47.61, "CenterLongitude": -122.2, "RadiusKm": 20}, {"Name": "Kent", "CenterLatitude": 47.38, "CenterLongitude": -122.23, "RadiusKm": 15}, {"Name": "<PERSON>", "CenterLatitude": 47.97, "CenterLongitude": -122.2, "RadiusKm": 15}, {"Name": "Ren<PERSON>", "CenterLatitude": 47.48, "CenterLongitude": -122.2, "RadiusKm": 15}, {"Name": "Federal Way", "CenterLatitude": 47.31, "CenterLongitude": -122.31, "RadiusKm": 15}, {"Name": "Ya<PERSON><PERSON>", "CenterLatitude": 46.6, "CenterLongitude": -120.5, "RadiusKm": 15}, {"Name": "Kirkland", "CenterLatitude": 47.68, "CenterLongitude": -122.2, "RadiusKm": 10}, {"Name": "Bellingham", "CenterLatitude": 48.75, "CenterLongitude": -122.47, "RadiusKm": 15}, {"Name": "Auburn", "CenterLatitude": 47.3, "CenterLongitude": -122.22, "RadiusKm": 10}, {"Name": "Pasco", "CenterLatitude": 46.23, "CenterLongitude": -119.1, "RadiusKm": 10}, {"Name": "Richland", "CenterLatitude": 46.28, "CenterLongitude": -119.28, "RadiusKm": 10}, {"Name": "Kennewick", "CenterLatitude": 46.21, "CenterLongitude": -119.13, "RadiusKm": 10}, {"Name": "Olympia", "CenterLatitude": 47.03, "CenterLongitude": -122.9, "RadiusKm": 10}, {"Name": "Marysville", "CenterLatitude": 48.05, "CenterLongitude": -122.17, "RadiusKm": 10}, {"Name": "<PERSON>", "CenterLatitude": 47.03, "CenterLongitude": -122.82, "RadiusKm": 10}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "CenterLatitude": 47.6, "CenterLongitude": -122.05, "RadiusKm": 10}, {"Name": "<PERSON>sa<PERSON><PERSON>", "CenterLatitude": 47.53, "CenterLongitude": -122.03, "RadiusKm": 10}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 47.67, "CenterLongitude": -122.12, "RadiusKm": 10}, {"Name": "Wenatchee", "CenterLatitude": 47.42, "CenterLongitude": -120.32, "RadiusKm": 10}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 46.73, "CenterLongitude": -117.18, "RadiusKm": 10}, {"Name": "Wall<PERSON> Wall<PERSON>", "CenterLatitude": 46.06, "CenterLongitude": -118.34, "RadiusKm": 10}], "Counties": [], "PostalCodePatterns": ["980##", "981##", "982##", "983##", "984##", "985##", "986##", "988##", "989##", "990##", "991##", "992##", "993##", "994##"], "CenterLatitude": 47.3865308, "CenterLongitude": -120.5238015, "RadiusKm": 250}, {"Code": "WI", "Name": "Wisconsin", "Cities": [{"Name": "Milwaukee", "CenterLatitude": 43.0389, "CenterLongitude": -87.9065, "RadiusKm": 40}, {"Name": "Madison", "CenterLatitude": 43.0731, "CenterLongitude": -89.4012, "RadiusKm": 30}, {"Name": "Green Bay", "CenterLatitude": 44.5192, "CenterLongitude": -88.0198, "RadiusKm": 20}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 42.5842, "CenterLongitude": -87.8214, "RadiusKm": 15}, {"Name": "Ra<PERSON>", "CenterLatitude": 42.726, "CenterLongitude": -87.782, "RadiusKm": 15}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 44.2619, "CenterLongitude": -88.4153, "RadiusKm": 15}, {"Name": "Oshkosh", "CenterLatitude": 44.02, "CenterLongitude": -88.55, "RadiusKm": 15}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 44.81, "CenterLongitude": -91.5, "RadiusKm": 15}, {"Name": "Janesville", "CenterLatitude": 42.68, "CenterLongitude": -89.02, "RadiusKm": 15}, {"Name": "West Allis", "CenterLatitude": 43.02, "CenterLongitude": -88, "RadiusKm": 10}], "Counties": [], "PostalCodePatterns": ["530##", "531##", "532##", "534##", "535##", "537##", "538##", "539##", "540##", "541##", "542##", "543##", "544##", "545##", "546##", "547##", "548##", "549##"], "CenterLatitude": 44.634444, "CenterLongitude": -89.709722, "RadiusKm": 200}, {"Code": "WV", "Name": "West Virginia", "Cities": [{"Name": "Charleston", "CenterLatitude": 38.3498, "CenterLongitude": -81.6326, "RadiusKm": 20}, {"Name": "Huntington", "CenterLatitude": 38.4196, "CenterLongitude": -82.4452, "RadiusKm": 20}, {"Name": "Morgantown", "CenterLatitude": 39.6295, "CenterLongitude": -79.9559, "RadiusKm": 15}, {"Name": "Parkersburg", "CenterLatitude": 39.26, "CenterLongitude": -81.55, "RadiusKm": 15}, {"Name": "Wheeling", "CenterLatitude": 40.07, "CenterLongitude": -80.72, "RadiusKm": 15}, {"Name": "Fairmont", "CenterLatitude": 39.48, "CenterLongitude": -80.14, "RadiusKm": 10}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 40.4, "CenterLongitude": -80.58, "RadiusKm": 10}, {"Name": "Martinsburg", "CenterLatitude": 39.45, "CenterLongitude": -77.97, "RadiusKm": 10}, {"Name": "Beckley", "CenterLatitude": 37.78, "CenterLongitude": -81.18, "RadiusKm": 10}, {"Name": "Clarksburg", "CenterLatitude": 39.28, "CenterLongitude": -80.34, "RadiusKm": 10}], "Counties": [], "PostalCodePatterns": ["247##", "248##", "249##", "250##", "251##", "252##", "253##", "254##", "255##", "256##", "257##", "258##", "259##", "260##", "261##", "262##", "263##", "264##", "265##", "266##", "267##", "268##"], "CenterLatitude": 38.598333, "CenterLongitude": -80.703333, "RadiusKm": 150}, {"Code": "WY", "Name": "Wyoming", "Cities": [{"Name": "Cheyenne", "CenterLatitude": 41.1399, "CenterLongitude": -104.8202, "RadiusKm": 20}, {"Name": "<PERSON>", "CenterLatitude": 42.8667, "CenterLongitude": -106.3131, "RadiusKm": 20}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 41.3114, "CenterLongitude": -105.5911, "RadiusKm": 15}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 44.29, "CenterLongitude": -105.5, "RadiusKm": 15}, {"Name": "Rock Springs", "CenterLatitude": 41.59, "CenterLongitude": -109.22, "RadiusKm": 15}, {"Name": "<PERSON>", "CenterLatitude": 44.79, "CenterLongitude": -106.95, "RadiusKm": 10}, {"Name": "Green River", "CenterLatitude": 41.52, "CenterLongitude": -109.46, "RadiusKm": 10}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 41.26, "CenterLongitude": -110.96, "RadiusKm": 10}, {"Name": "Riverton", "CenterLatitude": 43.02, "CenterLongitude": -108.38, "RadiusKm": 10}, {"Name": "<PERSON>", "CenterLatitude": 44.52, "CenterLongitude": -109.05, "RadiusKm": 10}], "Counties": [], "PostalCodePatterns": ["820##", "821##", "822##", "823##", "824##", "825##", "826##", "827##", "828##", "829##", "830##", "831##"], "CenterLatitude": 42.971667, "CenterLongitude": -107.671667, "RadiusKm": 250}]}, {"Code": "CA", "Name": "Canada", "States": [{"Code": "ON", "Name": "Ontario", "Cities": [{"Name": "Toronto", "CenterLatitude": 43.6532, "CenterLongitude": -79.3832, "RadiusKm": 50}, {"Name": "Ottawa", "CenterLatitude": 45.4215, "CenterLongitude": -75.6972, "RadiusKm": 40}, {"Name": "Mississauga", "CenterLatitude": 43.589, "CenterLongitude": -79.6441, "RadiusKm": 30}, {"Name": "Brampton", "CenterLatitude": 43.6816, "CenterLongitude": -79.759, "RadiusKm": 25}, {"Name": "<PERSON>", "CenterLatitude": 43.2557, "CenterLongitude": -79.8711, "RadiusKm": 25}, {"Name": "London", "CenterLatitude": 42.9837, "CenterLongitude": -81.2497, "RadiusKm": 20}, {"Name": "Markham", "CenterLatitude": 43.8561, "CenterLongitude": -79.337, "RadiusKm": 20}, {"Name": "<PERSON>", "CenterLatitude": 43.83, "CenterLongitude": -79.5, "RadiusKm": 20}, {"Name": "<PERSON>er", "CenterLatitude": 43.4516, "CenterLongitude": -80.4925, "RadiusKm": 20}, {"Name": "Windsor", "CenterLatitude": 42.3149, "CenterLongitude": -83.0364, "RadiusKm": 20}], "Counties": []}, {"Code": "QC", "Name": "Quebec", "Cities": [{"Name": "Montreal", "CenterLatitude": 45.5017, "CenterLongitude": -73.5673, "RadiusKm": 50}, {"Name": "Quebec City", "CenterLatitude": 46.8139, "CenterLongitude": -71.208, "RadiusKm": 40}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 45.58, "CenterLongitude": -73.75, "RadiusKm": 25}, {"Name": "Gatineau", "CenterLatitude": 45.47, "CenterLongitude": -75.7, "RadiusKm": 20}, {"Name": "<PERSON><PERSON><PERSON>", "CenterLatitude": 45.53, "CenterLongitude": -73.51, "RadiusKm": 20}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "CenterLatitude": 45.4, "CenterLongitude": -71.9, "RadiusKm": 15}, {"Name": "Saguenay", "CenterLatitude": 48.42, "CenterLongitude": -71.06, "RadiusKm": 15}, {"Name": "<PERSON><PERSON>", "CenterLatitude": 46.8, "CenterLongitude": -71.17, "RadiusKm": 15}, {"Name": "Trois-Rivieres", "CenterLatitude": 46.35, "CenterLongitude": -72.55, "RadiusKm": 15}, {"Name": "Terrebonne", "CenterLatitude": 45.7, "CenterLongitude": -73.63, "RadiusKm": 10}], "Counties": []}]}, {"Code": "MX", "Name": "Mexico", "States": [{"Code": "CDMX", "Name": "Ciudad de México", "Cities": [{"Name": "Ciudad de México", "CenterLatitude": 19.4326, "CenterLongitude": -99.1332, "RadiusKm": 70}], "Counties": []}, {"Code": "JAL", "Name": "Jalisco", "Cities": [{"Name": "Guadalajara", "CenterLatitude": 20.6597, "CenterLongitude": -103.3496, "RadiusKm": 40}, {"Name": "Zapopan", "CenterLatitude": 20.72, "CenterLongitude": -103.4, "RadiusKm": 30}, {"Name": "Tlaquepaque", "CenterLatitude": 20.63, "CenterLongitude": -103.31, "RadiusKm": 20}, {"Name": "Puerto Vallarta", "CenterLatitude": 20.6534, "CenterLongitude": -105.2253, "RadiusKm": 25}, {"Name": "Tonala", "CenterLatitude": 20.62, "CenterLongitude": -103.22, "RadiusKm": 15}, {"Name": "Tlajomulco de Zuniga", "CenterLatitude": 20.5, "CenterLongitude": -103.45, "RadiusKm": 15}], "Counties": []}]}]