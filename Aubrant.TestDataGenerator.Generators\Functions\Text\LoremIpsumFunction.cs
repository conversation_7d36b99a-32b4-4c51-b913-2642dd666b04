
using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Functions;
using Bogus;

namespace Aubrant.TestDataGenerator.Generators.Functions
{
    /// <summary>
    /// A generator function that produces Lorem Ipsum placeholder text.
    /// </summary>
    public class LoremIpsumFunction : BaseFunction
    {
        public override string Name => "LoremIpsum";
        public override string Description => "Generates Lorem Ipsum placeholder text (words, sentences, or paragraphs).";
        public override DataType ReturnType => DataType.String;

        /// <summary>
        /// Initializes a new instance of the LoremIpsumFunction class.
        /// </summary>
        public LoremIpsumFunction() : base("Text")
        {
            Parameters.Add(new Parameter("Type", "The type of text to generate.", DataType.String, new DataValue("Sentences"))); // Words, Sentences, Paragraphs
            Parameters.Add(new Parameter("Count", "The number of words, sentences, or paragraphs to generate.", DataType.Integer, new DataValue(3)));
        }

        /// <summary>
        /// Generates Lorem Ipsum text based on the configured parameters.
        /// </summary>
        public override DataValue Generate(DataGeneratorContext context)
        {
            string type = Parameters["Type"].Value?.ToString() ?? "Sentences";
            int count = Parameters["Count"].Value?.ToInt() ?? 3;

            if (count <= 0)
            {
                throw new ArgumentOutOfRangeException(nameof(count), "Count must be positive.");
            }

            var faker = new Faker();
            string result;

            switch (type.ToLowerInvariant())
            {
                case "words":
                    result = string.Join(" ", faker.Lorem.Words(count));
                    break;
                case "sentences":
                    result = faker.Lorem.Sentences(count);
                    break;
                case "paragraphs":
                    result = faker.Lorem.Paragraphs(count);
                    break;
                default:
                    throw new ArgumentException($"Invalid type specified: '{type}'. Expected 'Words', 'Sentences', or 'Paragraphs'.");
            }

            return new DataValue(DataType.String, result);
        }
    }
}
