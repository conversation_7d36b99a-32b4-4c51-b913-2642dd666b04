using Microsoft.SemanticKernel;
using System.Text.Json;
using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Interfaces;

namespace Aubrant.TestDataGenerator.Generators
{
    /// <summary>
    /// Defines the AI provider to be used for generating data.
    /// </summary>
    public enum AIProvider
    {
        /// <summary>
        /// Use the Google AI API (requires an API key).
        /// </summary>
        Google,
        /// <summary>
        /// Use a local Ollama instance.
        /// </summary>
        Ollama,
        /// <summary>
        /// Use the Mistral AI API (requires an API key).
        /// </summary>
        Mistral
    }

    /// <summary>
    /// Represents a generator that uses an AI model to generate data.
    /// </summary>
    public class AIGenerator : ICompiledGenerator
    {
        private List<DataValue>? _values;
        private int _currentIndex = 0;
        private readonly object _lock = new();
        private Kernel _kernel;
        private KernelArguments _arguments;
        private bool _isDirectPromptInvocation;
        private KernelFunction? _kernelFunction;
        private string _model;

        /// <summary>Gets the name of the generator.</summary>
        public string Name => "AI";

        /// <summary>Gets the description of the generator.</summary>
        public string Description => "Generates diverse test data by interpreting a natural language prompt using an external AI model. Ideal for complex, original or context-dependent data generation.";

        /// <summary>Gets the return type of the generator.</summary>
        public DataType ReturnType => DataType.String;

        /// <summary>The prompt to be used by the AI model.</summary>
        [System.ComponentModel.Description("The prompt to be used by the AI model.")]
        public string Prompt { get; set; }

        public AIGenerator()
        {
            Prompt = string.Empty;
            _model = string.Empty;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AIGenerator"/> class for direct AI prompt generation.
        /// </summary>
        /// <param name="prompt">The prompt to be used by the AI model.</param>
        /// <param name="provider">The AI provider to use (Google, Ollama, or Mistral).</param>
        /// <param name="model">The AI model to use (e.g., "gemini-1.5-flash" for Google, "mistral" for Ollama, "mistral-tiny" for Mistral).</param>
        /// <param name="apiKey">The API key for the Google AI API or Mistral AI API (required for Google and Mistral providers).</param>
        /// <param name="ollamaUrl">The URL for the Ollama instance (only for Ollama provider).</param>
        public AIGenerator(string prompt, AIProvider provider, string model, string? apiKey, string ollamaUrl = "http://localhost:11434")
        {
            Prompt = prompt;
            _model = model;

            var builder = Kernel.CreateBuilder();

            if (provider == AIProvider.Google)
            {
                builder.AddGoogleAIGeminiChatCompletion(_model, apiKey);
            }
            else if (provider == AIProvider.Ollama)
            {
                builder.AddOllamaChatCompletion(_model, new Uri(ollamaUrl));
            }
            else if (provider == AIProvider.Mistral)
            {
                builder.AddMistralChatCompletion(_model, apiKey);
            }

            _kernel = builder.Build();
            _arguments = new KernelArguments { { "prompt", prompt } };
            _isDirectPromptInvocation = true;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AIGenerator"/> class for invoking a specific KernelFunction.
        /// </summary>
        /// <param name="kernel">The Semantic Kernel instance.</param>
        /// <param name="kernelFunction">The KernelFunction to invoke.</param>
        /// <param name="arguments">The arguments to pass to the KernelFunction.</param>
        public AIGenerator(Kernel kernel, KernelFunction kernelFunction, KernelArguments arguments)
        {
            _kernel = kernel;
            _kernelFunction = kernelFunction; 
            _arguments = arguments;
            Prompt = arguments.TryGetValue("prompt", out object? promptValue) ? promptValue?.ToString() ?? string.Empty : string.Empty; 
            _model = string.Empty; 
            _isDirectPromptInvocation = false;
        }

        /// <summary>
        /// Generates a data value using the AI model.
        /// </summary>
        /// <returns>A <see cref="DataValue"/> generated by the AI model.</returns>
        public DataValue Generate()
        {
            return GenerateAsync().GetAwaiter().GetResult();
        }

        /// <summary>
        /// Asynchronously generates a data value using the AI model.
        /// </summary>
        /// <returns>A <see cref="DataValue"/> generated by the AI model.</returns>
        public async Task<DataValue> GenerateAsync()
        {
            await EnsureValuesGeneratedAsync();
            if (_values == null || _values.Count == 0)
                throw new InvalidOperationException("No values generated by AI.");

            var value = _values[_currentIndex];
            _currentIndex = (_currentIndex + 1) % _values.Count;
            return value;
        }

        /// <summary>
        /// Ensures that values have been generated by the AI model.
        /// </summary>
        private async Task EnsureValuesGeneratedAsync()
        {
            if (_values != null) return;
            
            // Note: This locking mechanism is not ideal for async, but will be addressed later.
            lock (_lock)
            {
                if (_values != null) return;
            }

            var result = await GenerateValuesFromAIAsync();
            
            lock(_lock)
            {
                if (_values == null)
                {
                    _values = result;
                }
            }
        }

        /// <summary>
        /// Generates a list of DataValues from the AI model.
        /// </summary>
        /// <returns>A list of <see cref="DataValue"/> generated by the AI model.</returns>
        private async Task<List<DataValue>> GenerateValuesFromAIAsync()
        {
            FunctionResult result;
            if (_isDirectPromptInvocation)
            {
                var systemPrompt = "You are a data generator. Generate a list of values based on the user prompt. Output only the values, one per line, with no numbering, no bullets, no extra text, and no additional formatting. Each line should contain only the generated value followed by a newline.";
                var fullPrompt = $"{systemPrompt}\n\nUser prompt: '{Prompt}'";
                result = await _kernel.InvokePromptAsync(fullPrompt);
            }
            else
            {
                result = await _kernel.InvokeAsync(_kernelFunction!, _arguments); 
            }

            var resultAsString = result.GetValue<string>();

            if (string.IsNullOrWhiteSpace(resultAsString)){
                return new List<DataValue>();
            }

            try
            {
                var names = JsonSerializer.Deserialize<List<string>>(resultAsString);
                if (names != null)
                    return names.Select(n => new DataValue(n)).ToList();
            }
            catch
            {
                // Fallback: try to extract JSON from text
                int start = resultAsString.IndexOf("[");
                int end = resultAsString.LastIndexOf("]");
                if (start >= 0 && end > start)
                {
                    try
                    {
                        var json = resultAsString.Substring(start, end - start + 1);
                        var names = JsonSerializer.Deserialize<List<string>>(json);
                        if (names != null)
                            return names.Select(n => new DataValue(n)).ToList();
                    }
                    catch
                    {
                        // Ignore if parsing the substring fails
                    }
                }
            }

            var lines = resultAsString.Split(new[] { "\n", "\r" }, StringSplitOptions.RemoveEmptyEntries)
                .Select(l => l.Trim())
                .Where(l => !string.IsNullOrWhiteSpace(l))
                .ToList();
            return lines.Select(l => new DataValue(l)).ToList();
        }

        public void Parse(DataType dataType, IDictionary<string, object> settings)
        {
            if (settings.TryGetValue("Prompt", out object? promptObject) && promptObject is JsonElement promptElement && promptElement.ValueKind == JsonValueKind.String)
            {
                Prompt = promptElement.GetString();
            }
        }
    }
}
