﻿using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Aubrant.TestDataGenerator.Core.Interfaces
{
    /// <summary>
    /// Interface for managing reference data pools across table generation
    /// </summary>
    public interface IReferenceDataManager
    {
        /// <summary>
        /// Registers a primary key value pool for an entity
        /// </summary>
        /// <param name="entityName">Name of the entity</param>
        /// <param name="fieldName">Name of the primary key field</param>
        /// <param name="values">Collection of generated primary key values</param>
        void RegisterPrimaryKeyPool(string entityName, string fieldName, IEnumerable<DataValue> values);

        /// <summary>
        /// Gets a random reference value from the specified entity's primary key pool
        /// </summary>
        /// <param name="entityName">Target entity name</param>
        /// <param name="fieldName">Target field name</param>
        /// <param name="random">Random instance for selection</param>
        /// <returns>A random reference value or null if pool is empty</returns>
        DataValue? GetRandomReference(string entityName, string fieldName, Random random);

        /// <summary>
        /// Gets multiple random reference values (for bulk operations)
        /// </summary>
        /// <param name="entityName">Target entity name</param>
        /// <param name="fieldName">Target field name</param>
        /// <param name="count">Number of values to retrieve</param>
        /// <param name="random">Random instance for selection</param>
        /// <param name="allowDuplicates">Whether to allow duplicate values</param>
        /// <returns>Collection of reference values</returns>
        IEnumerable<DataValue> GetRandomReferences(string entityName, string fieldName, int count, Random random, bool allowDuplicates = true);

        /// <summary>
        /// Gets all available reference values for an entity field
        /// </summary>
        /// <param name="entityName">Target entity name</param>
        /// <param name="fieldName">Target field name</param>
        /// <returns>All available reference values</returns>
        IEnumerable<DataValue> GetAllReferences(string entityName, string fieldName);

        /// <summary>
        /// Checks if reference data is available for the specified entity field
        /// </summary>
        /// <param name="entityName">Target entity name</param>
        /// <param name="fieldName">Target field name</param>
        /// <returns>True if reference data is available</returns>
        bool HasReferenceData(string entityName, string fieldName);

        /// <summary>
        /// Gets the count of available reference values
        /// </summary>
        /// <param name="entityName">Target entity name</param>
        /// <param name="fieldName">Target field name</param>
        /// <returns>Number of available reference values</returns>
        int GetReferenceCount(string entityName, string fieldName);

        /// <summary>
        /// Clears all reference data (useful for new generation runs)
        /// </summary>
        void ClearAllReferences();

        /// <summary>
        /// Clears reference data for a specific entity
        /// </summary>
        /// <param name="entityName">Entity name to clear</param>
        void ClearEntityReferences(string entityName);

        /// <summary>
        /// Adds a single reference value to an existing pool
        /// </summary>
        /// <param name="entityName">Target entity name</param>
        /// <param name="fieldName">Target field name</param>
        /// <param name="value">Value to add</param>
        void AddReference(string entityName, string fieldName, DataValue value);

        /// <summary>
        /// Gets reference statistics for monitoring and debugging
        /// </summary>
        /// <returns>Statistics about reference pools</returns>
        ReferenceDataStatistics GetStatistics();
    }

    /// <summary>
    /// Statistics about reference data pools
    /// </summary>
    public class ReferenceDataStatistics
    {
        /// <summary>
        /// Total number of reference pools
        /// </summary>
        public int TotalPools { get; set; }

        /// <summary>
        /// Total number of reference values across all pools
        /// </summary>
        public int TotalReferenceValues { get; set; }

        /// <summary>
        /// Details about each reference pool
        /// </summary>
        public List<ReferencePoolInfo> PoolDetails { get; set; } = new();

        /// <summary>
        /// Memory usage estimate in bytes
        /// </summary>
        public long EstimatedMemoryUsage { get; set; }
    }

    /// <summary>
    /// Information about a specific reference pool
    /// </summary>
    public class ReferencePoolInfo
    {
        /// <summary>
        /// Entity name
        /// </summary>
        public string EntityName { get; set; } = null!;

        /// <summary>
        /// Field name
        /// </summary>
        public string FieldName { get; set; } = null!;

        /// <summary>
        /// Number of values in the pool
        /// </summary>
        public int ValueCount { get; set; }

        /// <summary>
        /// Data type of values in the pool
        /// </summary>
        public DataType DataType { get; set; }

        /// <summary>
        /// Sample values from the pool (for debugging)
        /// </summary>
        public List<object> SampleValues { get; set; } = new();

        /// <summary>
        /// Number of times this pool has been accessed
        /// </summary>
        public int AccessCount { get; set; }

        /// <summary>
        /// When this pool was created
        /// </summary>
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// In-memory implementation of reference data manager
    /// </summary>
    public class InMemoryReferenceDataManager : IReferenceDataManager
    {
        private readonly Dictionary<string, Dictionary<string, ReferencePool>> _referencePools = new();
        private readonly object _lock = new();

        public void RegisterPrimaryKeyPool(string entityName, string fieldName, IEnumerable<DataValue> values)
        {
            lock (_lock)
            {
                if (!_referencePools.ContainsKey(entityName))
                    _referencePools[entityName] = new Dictionary<string, ReferencePool>();

                _referencePools[entityName][fieldName] = new ReferencePool
                {
                    EntityName = entityName,
                    FieldName = fieldName,
                    Values = values.ToList(),
                    CreatedAt = DateTime.UtcNow
                };
            }
        }

        public DataValue? GetRandomReference(string entityName, string fieldName, Random random)
        {
            lock (_lock)
            {
                if (_referencePools.TryGetValue(entityName, out var entityPools) &&
                    entityPools.TryGetValue(fieldName, out var pool) &&
                    pool.Values.Count > 0)
                {
                    pool.AccessCount++;
                    return pool.Values[random.Next(pool.Values.Count)];
                }
                return null;
            }
        }

        public IEnumerable<DataValue> GetRandomReferences(string entityName, string fieldName, int count, Random random, bool allowDuplicates = true)
        {
            lock (_lock)
            {
                if (!_referencePools.TryGetValue(entityName, out var entityPools) ||
                    !entityPools.TryGetValue(fieldName, out var pool) ||
                    pool.Values.Count == 0)
                {
                    return Enumerable.Empty<DataValue>();
                }

                pool.AccessCount += count;

                if (allowDuplicates)
                {
                    return Enumerable.Range(0, count)
                        .Select(_ => pool.Values[random.Next(pool.Values.Count)]);
                }
                else
                {
                    var availableCount = Math.Min(count, pool.Values.Count);
                    return pool.Values.OrderBy(_ => random.Next()).Take(availableCount);
                }
            }
        }

        public IEnumerable<DataValue> GetAllReferences(string entityName, string fieldName)
        {
            lock (_lock)
            {
                if (_referencePools.TryGetValue(entityName, out var entityPools) &&
                    entityPools.TryGetValue(fieldName, out var pool))
                {
                    return pool.Values.ToList(); // Return a copy
                }
                return Enumerable.Empty<DataValue>();
            }
        }

        public bool HasReferenceData(string entityName, string fieldName)
        {
            lock (_lock)
            {
                return _referencePools.TryGetValue(entityName, out var entityPools) &&
                       entityPools.TryGetValue(fieldName, out var pool) &&
                       pool.Values.Count > 0;
            }
        }

        public int GetReferenceCount(string entityName, string fieldName)
        {
            lock (_lock)
            {
                if (_referencePools.TryGetValue(entityName, out var entityPools) &&
                    entityPools.TryGetValue(fieldName, out var pool))
                {
                    return pool.Values.Count;
                }
                return 0;
            }
        }

        public void ClearAllReferences()
        {
            lock (_lock)
            {
                _referencePools.Clear();
            }
        }

        public void ClearEntityReferences(string entityName)
        {
            lock (_lock)
            {
                _referencePools.Remove(entityName);
            }
        }

        public void AddReference(string entityName, string fieldName, DataValue value)
        {
            lock (_lock)
            {
                if (!_referencePools.ContainsKey(entityName))
                    _referencePools[entityName] = new Dictionary<string, ReferencePool>();

                if (!_referencePools[entityName].ContainsKey(fieldName))
                {
                    _referencePools[entityName][fieldName] = new ReferencePool
                    {
                        EntityName = entityName,
                        FieldName = fieldName,
                        Values = new List<DataValue>(),
                        CreatedAt = DateTime.UtcNow
                    };
                }

                _referencePools[entityName][fieldName].Values.Add(value);
            }
        }

        public ReferenceDataStatistics GetStatistics()
        {
            lock (_lock)
            {
                var stats = new ReferenceDataStatistics
                {
                    TotalPools = _referencePools.Values.Sum(e => e.Count),
                    TotalReferenceValues = _referencePools.Values.SelectMany(e => e.Values).Sum(p => p.Values.Count)
                };

                foreach (var entityPools in _referencePools)
                {
                    foreach (var pool in entityPools.Value.Values)
                    {
                        stats.PoolDetails.Add(new ReferencePoolInfo
                        {
                            EntityName = pool.EntityName,
                            FieldName = pool.FieldName,
                            ValueCount = pool.Values.Count,
                            DataType = pool.Values.FirstOrDefault()?.Type ?? DataType.Any,
                            SampleValues = pool.Values.Take(5).Select(v => v.Value).ToList(),
                            AccessCount = pool.AccessCount,
                            CreatedAt = pool.CreatedAt
                        });
                    }
                }

                // Rough memory usage estimate
                stats.EstimatedMemoryUsage = stats.TotalReferenceValues * 32; // Rough estimate

                return stats;
            }
        }

        private class ReferencePool
        {
            public string EntityName { get; set; } = null!;
            public string FieldName { get; set; } = null!;
            public List<DataValue> Values { get; set; } = new();
            public int AccessCount { get; set; }
            public DateTime CreatedAt { get; set; }
        }
    }
}
