using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;

namespace Aubrant.TestDataGenerator.Core.Utils.Models
{
    public class NumericOffsetCalculator : IOffsetCalculator
    {
        public DataValue ApplyOffset(DataValue baseValue, string minOffset, string maxOffset, Random random)
        {
            if (baseValue.Type == DataType.Integer)
            {
                int baseVal = baseValue.ToInt() ?? 0;
                int min = int.Parse(minOffset);
                int max = int.Parse(maxOffset);
                return new DataValue(baseVal + random.Next(min, max + 1));
            }
            else if (baseValue.Type == DataType.Decimal)
            {
                decimal baseVal = baseValue.ToDecimal() ?? 0m;
                decimal min = decimal.Parse(minOffset);
                decimal max = decimal.Parse(maxOffset);
                return new DataValue(baseVal + min + (max - min) * (decimal)random.NextDouble());
            }
            else
            {
                throw new NotSupportedException($"Numeric offsets are not supported for type {baseValue.Type}");
            }
        }
    }
}
