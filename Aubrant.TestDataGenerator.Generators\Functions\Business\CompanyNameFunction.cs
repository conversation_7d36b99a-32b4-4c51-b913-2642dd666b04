using Bogus;
using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Functions;

namespace Aubrant.TestDataGenerator.Generators.Functions
{
    /// <summary>
    /// A generator function that produces a random company name.
    /// </summary>
    public class CompanyNameFunction : BaseFunction
    {
        public override string Name => "CompanyName";
        public override string Description => "Generates a random company name.";
        public override DataType ReturnType => DataType.String;

        /// <summary>
        /// Initializes a new instance of the CompanyNameFunction class.
        /// </summary>
        public CompanyNameFunction() : base("Business")
        {
            Parameters.Add(new Parameter("locale", "The output locale.", DataType.String, new DataValue("en")));
        }

        /// <summary>
        /// Generates a random company name using the Bogus library.
        /// </summary>
        public override DataValue Generate(DataGeneratorContext context)
        {
            string locale = Parameters["locale"].Value?.ToString() ?? "en";
            var faker = new Faker(locale);
            string companyName = faker.Company.CompanyName();
            return new DataValue(DataType.String, companyName);
        }
    }
}
