using System.Globalization;
using Aubrant.TestDataGenerator.Core.Enums;
using System.Text.Json;

namespace Aubrant.TestDataGenerator.Core
{
    public class DataValue
    {
        public DataType Type { get; init; }
        public object? Value { get; init; }

        /// <summary>
        /// Private constructor for direct assignment, bypassing conversion logic.
        /// The boolean parameter is a marker to create a unique signature.
        /// </summary>
        private DataValue(DataType type, object? value, bool isDirect)
        {
            Type = type;
            Value = value;
        }

        /// <summary>
        /// Primary public constructor for DataValue. It attempts to convert the given value
        /// to the specified DataType, handling primitives, strings, and JsonElements.
        /// </summary>
        /// <param name="type">The target DataType to convert the value to.</param>
        /// <param name="value">The input value to convert.</param>
        public DataValue(DataType type, object? value) 
        {
            Type = type;
            Value = ConvertValue(type, value);
        }

        private static object? ConvertValue(DataType targetType, object? inputValue)
        {
            // Handle nulls universally first
            if (inputValue == null || inputValue is DBNull)
            {
                return DBNull.Value;
            }

            // If the value is a JsonElement, unwrap it to its primitive equivalent
            if (inputValue is JsonElement element)
            {
                switch (element.ValueKind)
                {
                    case JsonValueKind.String:
                        // Smart conversion: a string could be a date
                        inputValue = element.TryGetDateTime(out var dt) ? dt : element.GetString();
                        break;
                    case JsonValueKind.Number:
                        inputValue = element.TryGetInt32(out var i) ? i : element.GetDecimal();
                        break;
                    case JsonValueKind.True:
                        inputValue = true;
                        break;
                    case JsonValueKind.False:
                        inputValue = false;
                        break;
                    case JsonValueKind.Null:
                        return DBNull.Value;
                    default:
                        // For arrays, objects, or undefined, fallback to string representation
                        inputValue = element.ToString();
                        break;
                }
            }

            // Perform the final conversion to the target type
            switch (targetType)
            {
                case DataType.String:
                    return inputValue?.ToString();

                case DataType.Integer:
                    if (inputValue is int i) return i;
                    if (inputValue is decimal d) return (int)d; // Truncate
                    if (inputValue is bool b_int) return b_int ? 1 : 0;
                    if (int.TryParse(inputValue?.ToString(), NumberStyles.Any, CultureInfo.InvariantCulture, out var intResult))
                    {
                        return intResult;
                    }
                    return null; // Conversion failed

                case DataType.Decimal:
                    if (inputValue is decimal dec) return dec;
                    if (inputValue is int intVal) return (decimal)intVal;
                    if (inputValue is bool boolVal) return boolVal ? 1m : 0m;
                    if (decimal.TryParse(inputValue?.ToString(), NumberStyles.Any, CultureInfo.InvariantCulture, out var decResult))
                    {
                        return decResult;
                    }
                    return null; // Conversion failed

                case DataType.DateTime:
                    if (inputValue is DateTime dt) return dt;
                    if (DateTime.TryParse(inputValue?.ToString(), CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal, out var dateResult))
                    {
                        return dateResult;
                    }
                    return null; // Conversion failed

                case DataType.Boolean:
                    if (inputValue is bool b_bool) return b_bool;
                    if (inputValue is int intBool) return intBool != 0;
                    if (inputValue is decimal decBool) return decBool != 0m;
                    var strVal = inputValue?.ToString()?.ToLowerInvariant();
                    if (strVal == "true" || strVal == "1") return true;
                    if (strVal == "false" || strVal == "0") return false;
                    return null; // Conversion failed

                case DataType.Any:
                default:
                    return inputValue; // For 'Any', just store the unwrapped value
            }
        }


        /// <summary>
        /// Convenience constructor for integer values, automatically setting DataType.Integer.
        /// </summary>
        public DataValue(int value) : this(DataType.Integer, value, true) { }

        /// <summary>
        /// Convenience constructor for decimal values, automatically setting DataType.Decimal.
        /// </summary>
        public DataValue(decimal value) : this(DataType.Decimal, value, true) { }

        /// <summary>
        /// Convenience constructor for DateTime values, automatically setting DataType.DateTime.
        /// </summary>
        public DataValue(DateTime value) : this(DataType.DateTime, value, true) { }

        /// <summary>
        /// Convenience constructor for boolean values, automatically setting DataType.Boolean.
        /// </summary>
        public DataValue(bool value) : this(DataType.Boolean, value, true) { }

        /// <summary>
        /// Convenience constructor for string values, automatically setting DataType.String.
        /// </summary>
        public DataValue(string? value) : this(DataType.String, value, true) { }

        /// <summary>
        /// Convenience constructor for nul values.
        /// </summary>
        public DataValue(DBNull value) : this(DataType.Any, value, true) { }

        /// <summary>
        /// Attempts to convert the DataValue to a string.
        /// Returns null if the value is null. Otherwise, returns its string representation.
        /// </summary>
        public override string ToString()
        {
            return Value?.ToString() ?? "null";
        }

        /// <summary>
        /// Attempts to convert the DataValue to an integer.
        /// Returns null if the value is null or conversion fails.
        /// </summary>
        public int? ToInt()
        {
            if (Value == null || Value is DBNull) return null;
            // Use the constructor's own logic to perform the conversion
            var tempValue = new DataValue(DataType.Integer, Value);
            return tempValue.Value as int?;
        }

        /// <summary>
        /// Attempts to convert the DataValue to a decimal.
        /// Returns null if the value is null or conversion fails.
        /// </summary>
        public decimal? ToDecimal()
        {
            if (Value == null || Value is DBNull) return null;
            // Use the constructor's own logic to perform the conversion
            var tempValue = new DataValue(DataType.Decimal, Value);
            return tempValue.Value as decimal?;
        }

        /// <summary>
        /// Attempts to convert the DataValue to a DateTime.
        /// Returns null if the value is null or conversion fails.
        /// </summary>
        public DateTime? ToDate()
        {
            if (Value == null || Value is DBNull) return null;
            // Use the constructor's own logic to perform the conversion
            var tempValue = new DataValue(DataType.DateTime, Value);
            return tempValue.Value as DateTime?;
        }

        /// <summary>
        /// Attempts to convert the DataValue to a boolean.
        /// Returns null if the value is null or conversion fails.
        /// </summary>
        public bool? ToBoolean()
        {
            if (Value == null || Value is DBNull) return null;
            // Use the constructor's own logic to perform the conversion
            var tempValue = new DataValue(DataType.Boolean, Value);
            return tempValue.Value as bool?;
        }
    }
}