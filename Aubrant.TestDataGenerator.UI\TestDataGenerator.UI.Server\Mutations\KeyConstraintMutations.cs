using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.Data.Enums;
using Aubrant.TestDataGenerator.UI.Server.GraphQL;

namespace Aubrant.TestDataGenerator.UI.Server.Mutations
{
    [ExtendObjectType(typeof(Mutation))]
    public class KeyConstraintMutations
    {
        public async Task<KeyConstraint> CreateKeyConstraint(int entityId, KeyType keyType, List<string> columns, string? referencedEntity, List<string>? referencedColumns, [Service] DatabaseContext context)
        {
            var keyConstraint = new KeyConstraint
            {
                EntityId = entityId,
                KeyType = keyType,
                Columns = columns,
                ReferencedEntity = referencedEntity,
                ReferencedColumns = referencedColumns ?? new List<string>()
            };

            context.KeyConstraints.Add(keyConstraint);
            await context.SaveChangesAsync();

            return keyConstraint;
        }

        public async Task<KeyConstraint> UpdateKeyConstraint(int id, KeyType? keyType, List<string>? columns, string? referencedEntity, List<string>? referencedColumns, [Service] DatabaseContext context)
        {
            var keyConstraint = await context.KeyConstraints.FindAsync(id);

            if (keyConstraint == null)
            {
                throw new System.Exception("KeyConstraint not found.");
            }

            if (keyType.HasValue)
            {
                keyConstraint.KeyType = keyType.Value;
            }

            if (columns != null)
            {
                keyConstraint.Columns = columns;
            }

            if (referencedEntity != null)
            {
                keyConstraint.ReferencedEntity = referencedEntity;
            }

            if (referencedColumns != null)
            {
                keyConstraint.ReferencedColumns = referencedColumns;
            }

            await context.SaveChangesAsync();

            return keyConstraint;
        }

        public async Task<bool> DeleteKeyConstraint(int id, [Service] DatabaseContext context)
        {
            var keyConstraint = await context.KeyConstraints.FindAsync(id);

            if (keyConstraint == null)
            {
                return false;
            }

            context.KeyConstraints.Remove(keyConstraint);
            await context.SaveChangesAsync();

            return true;
        }
    }
}
