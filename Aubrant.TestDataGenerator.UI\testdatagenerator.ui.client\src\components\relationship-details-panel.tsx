import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import type { Relationship, Entity } from "../types/database";
import { Link } from "lucide-react"

interface RelationshipDetailsPanelProps {
  selectedRelationship: {
    relationship: Relationship
    sourceEntity: Entity
    targetEntity: Entity
  }
}

export function RelationshipDetailsPanel({
  selectedRelationship,
}: RelationshipDetailsPanelProps) {
  return (
    <div className="space-y-4">
      <div>
        <h3 className="font-semibold text-base mb-2 flex items-center gap-2">
          <Link className="w-4 h-4" />
          Relationship Details
        </h3>
        <div className="space-y-2">
          <div>
            <label className="text-sm font-medium">Source</label>
            <p className="text-sm text-muted-foreground">
              {selectedRelationship.sourceEntity.Name}.
              {selectedRelationship.relationship.SourceField}
            </p>
          </div>
          <div>
            <label className="text-sm font-medium">Target</label>
            <p className="text-sm text-muted-foreground">
              {selectedRelationship.targetEntity.Name}.
              {selectedRelationship.relationship.TargetField}
            </p>
          </div>
          <div>
            <label className="text-sm font-medium">Cardinality</label>
            <p className="text-sm text-muted-foreground">
              {selectedRelationship.relationship.Cardinality}
            </p>
          </div>
        </div>
      </div>
      <Separator />
      <div>
        <h4 className="font-medium mb-2">Relationship Type</h4>
        <Badge variant="outline" className="text-sm">
          {selectedRelationship.relationship.Cardinality}
        </Badge>
      </div>
    </div>
  )
}
