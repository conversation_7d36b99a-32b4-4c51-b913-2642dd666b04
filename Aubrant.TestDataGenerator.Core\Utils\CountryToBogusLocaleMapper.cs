namespace Aubrant.TestDataGenerator.Core.Utils
{
    public static class CountryToBogusLocaleMapper
    {
        private static readonly Dictionary<string, string> localeMap = new(StringComparer.OrdinalIgnoreCase)
        {
            /* ---------- ISO 3166-1 alpha-2 codes ---------- */
            { "US", "en_US" },
            { "CA", "en_CA" },
            { "MX", "es_MX" },
            { "CR", "es_CR" },
            { "PA", "es_PA" },
            { "CO", "es_CO" },
            { "AR", "es_AR" },
            { "CL", "es_CL" },
            { "PE", "es" },
            { "VE", "es" },
            { "EC", "es" },
            { "BR", "pt_BR" },
            { "PT", "pt_PT" },
            { "GB", "en_GB" },
            { "IE", "en_IE" },
            { "AU", "en_AU" },
            { "NZ", "en_NZ" },
            { "DE", "de" },
            { "AT", "de_AT" },
            { "CH", "de_CH" },
            { "FR", "fr" },
            { "BE", "fr_BE" },
            { "CH", "fr_CH" },
            { "ES", "es" },
            { "IT", "it" },
            { "NL", "nl" },
            { "BE", "nl_BE" },
            { "NO", "no" },
            { "SE", "sv" },
            { "DK", "da" },
            { "FI", "fi" },
            { "PL", "pl" },
            { "CZ", "cs_CZ" },
            { "SK", "sk" },
            { "HU", "hu" },
            { "RO", "ro" },
            { "BG", "bg" },
            { "HR", "hr" },
            { "SI", "sl" },
            { "RS", "sr_Cyrl_RS" },
            { "RU", "ru" },
            { "TR", "tr" },
            { "GR", "el" },
            { "IL", "he" },
            { "SA", "ar_SA" },
            { "EG", "ar" },
            { "AE", "ar" },
            { "JP", "ja" },
            { "KR", "ko" },
            { "CN", "zh_CN" },
            { "TW", "zh_TW" },
            { "HK", "zh_HK" },
            { "IN", "hi" },
            { "TH", "th" },
            { "VN", "vi" },
            { "ID", "id_ID" },
            { "MY", "ms" },
            { "ZA", "en_ZA" },
            { "NG", "en_NG" },

            /* ---------- English country names ---------- */
            { "United States",     "en_US" },
            { "Canada",            "en_CA" },
            { "Mexico",            "es_MX" },
            { "Costa Rica",        "es_CR" },
            { "Panama",            "es_PA" },
            { "Colombia",          "es_CO" },
            { "Argentina",         "es_AR" },
            { "Chile",             "es_CL" },
            { "Peru",              "es" },
            { "Venezuela",         "es" },
            { "Ecuador",           "es" },
            { "Brazil",            "pt_BR" },
            { "Portugal",          "pt_PT" },
            { "United Kingdom",    "en_GB" },
            { "Ireland",           "en_IE" },
            { "Australia",         "en_AU" },
            { "New Zealand",       "en_NZ" },
            { "Germany",           "de" },
            { "Austria",           "de_AT" },
            { "Switzerland",       "de_CH" },
            { "France",            "fr" },
            { "Belgium",           "fr_BE" },
            { "Spain",             "es" },
            { "Italy",             "it" },
            { "Netherlands",       "nl" },
            { "Norway",            "no" },
            { "Sweden",            "sv" },
            { "Denmark",           "da" },
            { "Finland",           "fi" },
            { "Poland",            "pl" },
            { "Czech Republic",    "cs_CZ" },
            { "Slovakia",          "sk" },
            { "Hungary",           "hu" },
            { "Romania",           "ro" },
            { "Bulgaria",          "bg" },
            { "Croatia",           "hr" },
            { "Slovenia",          "sl" },
            { "Serbia",            "sr_Cyrl_RS" },
            { "Russia",            "ru" },
            { "Turkey",            "tr" },
            { "Greece",            "el" },
            { "Israel",            "he" },
            { "Saudi Arabia",      "ar_SA" },
            { "Egypt",             "ar" },
            { "United Arab Emirates","ar" },
            { "Japan",             "ja" },
            { "South Korea",       "ko" },
            { "China",             "zh_CN" },
            { "Taiwan",            "zh_TW" },
            { "Hong Kong",         "zh_HK" },
            { "India",             "hi" },
            { "Thailand",          "th" },
            { "Vietnam",           "vi" },
            { "Indonesia",         "id_ID" },
            { "Malaysia",          "ms" },
            { "South Africa",      "en_ZA" },
            { "Nigeria",           "en_NG" }
        };

        /// <summary>
        /// Resolves a country input (ISO code, full name) to a Bogus.NET locale string for geographical data generation.
        /// </summary>
        /// <param name="countryInput">The country identifier (e.g., "US", "Costa Rica").</param>
        /// <returns>A Bogus.NET locale string (e.g., "en_US"), or "en_US" if not found.</returns>
        public static string MapCountryToBogusLocale(string? countryInput)
        {
            if (string.IsNullOrWhiteSpace(countryInput))
            {
                return "en_US"; // Default Bogus locale for geographical data if no input
            }

            if (localeMap.TryGetValue(countryInput, out string? locale))
            {
                Console.WriteLine($"Mapped country '{countryInput}' to locale '{locale}'");
                return locale;
            }

            // Fallback: if input is already a Bogus-like locale (e.g., "fr", "en_GB"), use it directly
            // This is a simple check, could be more robust if needed.
            if (countryInput.Contains("_") || countryInput.Length == 2)
            {
                return countryInput;
            }

            return "en_US"; // Default Bogus locale if resolution fails
        }
    }
}
