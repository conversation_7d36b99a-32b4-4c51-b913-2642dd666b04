﻿using Microsoft.EntityFrameworkCore;

namespace Aubrant.TestDataGenerator.Data
{
    public class DatabaseContext : DbContext
    {
        public DbSet<Project> Projects { get; set; }
        public DbSet<DataSource> DataSources { get; set; }
        public DbSet<Entity> Entities { get; set; }
        public DbSet<Field> Fields { get; set; }
        public DbSet<FieldSetting> FieldSettings { get; set; }
        public DbSet<KeyConstraint> KeyConstraints { get; set; }
        public DbSet<Relationship> Relationships { get; set; }

        public string DbPath { get; private set; }

        public DatabaseContext()
        {
            var folder = Directory.GetParent(AppDomain.CurrentDomain.BaseDirectory)!.Parent!.Parent!.Parent!.Parent!.FullName;
            DbPath = Path.Combine(folder, @"Aubrant.TestDataGenerator.Data\Database\TestDataGenerator.db");
        }

        public DatabaseContext(DbContextOptions<DatabaseContext> options) : base(options) 
        {
            var folder = Directory.GetParent(AppDomain.CurrentDomain.BaseDirectory)!.Parent!.Parent!.Parent!.Parent!.FullName;
            DbPath = Path.Combine(folder, @"Aubrant.TestDataGenerator.Data\Database\TestDataGenerator.db");
        }

        protected override void OnConfiguring(DbContextOptionsBuilder options)
        {
            if (!options.IsConfigured)
            {
                options.UseLazyLoadingProxies().UseSqlite($"Data Source={DbPath}");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<DataSource>()
                .Property(d => d.Type)
                .HasConversion<int>();
            modelBuilder.Entity<DataSource>()
                .Property(d => d.Provider)
                .HasConversion<int>();
            modelBuilder.Entity<Entity>()
                .Property(e => e.SyncStrategy)
                .HasConversion<int>();
            modelBuilder.Entity<Field>()
                .Property(f => f.Type)
                .HasConversion<int>();
            modelBuilder.Entity<FieldSetting>()
                .Property(fs => fs.Type)
                .HasConversion<int>();
            modelBuilder.Entity<KeyConstraint>()
                .Property(kc => kc.KeyType)
                .HasConversion<int>();
            modelBuilder.Entity<Relationship>()
                .Property(r => r.Cardinality)
                .HasConversion<int>();
        }
    }
}
