{"Name": "northwind_small", "Type": "RelationalDatabase", "Provider": "SQLite", "ConnectionString": "Data Source=C:\\Users\\<USER>\\Downloads\\northwind_small.sqlite", "Entities": [{"Name": "Employee", "Fields": [{"Name": "Id", "NativeType": "INTEGER", "IsNullable": true, "IsPrimaryKey": true}, {"Name": "LastName", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "FirstName", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "Title", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "TitleOfCourtesy", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "BirthDate", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "HireDate", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "Address", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "City", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "Region", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "PostalCode", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "Country", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "HomePhone", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "Extension", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "Photo", "Type": "Binary", "NativeType": "BLOB", "IsNullable": true}, {"Name": "Notes", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "ReportsTo", "NativeType": "INTEGER", "IsNullable": true}, {"Name": "PhotoPath", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}], "KeyConstraints": [{"Columns": ["Id"], "ReferencedColumns": []}], "Relationships": [], "Data": [{"Id": 1, "LastName": "<PERSON><PERSON><PERSON>", "FirstName": "Nancy", "Title": "Sales Representative", "TitleOfCourtesy": "Ms.", "BirthDate": "1980-12-08", "HireDate": "2024-05-01", "Address": "507 - 20th Ave. E. Apt. 2A", "City": "Seattle", "Region": "North America", "PostalCode": "98122", "Country": "USA", "HomePhone": "(*************", "Extension": "5467", "Notes": "Education includes a BA in psychology from Colorado State University in 1970.  She also completed 'The Art of the Cold Call.'  <PERSON> is a member of Toastmasters International.", "ReportsTo": 2, "PhotoPath": "http://accweb/emmployees/davolio.bmp"}, {"Id": 2, "LastName": "<PERSON>", "FirstName": "<PERSON>", "Title": "Vice President, Sales", "TitleOfCourtesy": "Dr.", "BirthDate": "1984-02-19", "HireDate": "2024-08-14", "Address": "908 W. Capital Way", "City": "Tacoma", "Region": "North America", "PostalCode": "98401", "Country": "USA", "HomePhone": "(*************", "Extension": "3457", "Notes": "<PERSON> received his BTS commercial in 1974 and a Ph.D. in international marketing from the University of Dallas in 1981.  He is fluent in French and Italian and reads German.  He joined the company as a sales representative, was promoted to sales manager in January 1992 and to vice president of sales in March 1993.  <PERSON> is a member of the Sales Management Roundtable, the Seattle Chamber of Commerce, and the Pacific Rim Importers Association.", "PhotoPath": "http://accweb/emmployees/fuller.bmp"}, {"Id": 8, "LastName": "<PERSON><PERSON>", "FirstName": "<PERSON>", "Title": "Inside Sales Coordinator", "TitleOfCourtesy": "Ms.", "BirthDate": "1990-01-09", "HireDate": "2026-03-05", "Address": "4726 - 11th Ave. N.E.", "City": "Seattle", "Region": "North America", "PostalCode": "98105", "Country": "USA", "HomePhone": "(*************", "Extension": "2344", "Notes": "<PERSON> received a BA in psychology from the University of Washington.  She has also completed a course in business French.  She reads and writes French.", "ReportsTo": 2, "PhotoPath": "http://accweb/emmployees/davolio.bmp"}, {"Id": 9, "LastName": "Dodsworth", "FirstName": "<PERSON>", "Title": "Sales Representative", "TitleOfCourtesy": "Ms.", "BirthDate": "1998-01-27", "HireDate": "2026-11-15", "Address": "7 Houndstooth Rd.", "City": "London", "Region": "British Isles", "PostalCode": "WG2 7LT", "Country": "UK", "HomePhone": "(71) 555-4444", "Extension": "452", "Notes": "<PERSON> has a BA degree in English from St. Lawrence College.  She is fluent in French and German.", "ReportsTo": 5, "PhotoPath": "http://accweb/emmployees/davolio.bmp"}]}, {"Name": "Category", "Fields": [{"Name": "Id", "NativeType": "INTEGER", "IsNullable": true, "IsPrimaryKey": true}, {"Name": "CategoryName", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "Description", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}], "KeyConstraints": [{"Columns": ["Id"], "ReferencedColumns": []}], "Relationships": [], "Data": [{"Id": 1, "CategoryName": "Beverages", "Description": "Soft drinks, coffees, teas, beers, and ales"}, {"Id": 2, "CategoryName": "Condiments", "Description": "Sweet and savory sauces, relishes, spreads, and seasonings"}, {"Id": 7, "CategoryName": "Produce", "Description": "Dried fruit and bean curd"}, {"Id": 8, "CategoryName": "Seafood", "Description": "Seaweed and fish"}]}, {"Name": "Customer", "Fields": [{"Name": "Id", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true, "IsPrimaryKey": true}, {"Name": "CompanyName", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "ContactName", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "ContactTitle", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "Address", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "City", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "Region", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "PostalCode", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "Country", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "Phone", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "Fax", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}], "KeyConstraints": [{"Columns": ["Id"], "ReferencedColumns": []}], "Relationships": [], "Data": [{"Id": "ALFKI", "CompanyName": "<PERSON><PERSON>", "ContactName": "<PERSON>", "ContactTitle": "Sales Representative", "Address": "Obere Str. 57", "City": "Berlin", "Region": "Western Europe", "PostalCode": "12209", "Country": "Germany", "Phone": "030-0074321", "Fax": "030-0076545"}, {"Id": "ANATR", "CompanyName": "<PERSON>eda<PERSON> y helados", "ContactName": "<PERSON>", "ContactTitle": "Owner", "Address": "Avda. de la Constitución 2222", "City": "México D.F.", "Region": "Central America", "PostalCode": "05021", "Country": "Mexico", "Phone": "(5) 555-4729", "Fax": "(5) 555-3745"}, {"Id": "WILMK", "CompanyName": "<PERSON><PERSON><PERSON>", "ContactName": "<PERSON><PERSON>", "ContactTitle": "Owner/Marketing Assistant", "Address": "Keskuskatu 45", "City": "Helsinki", "Region": "Scandinavia", "PostalCode": "21240", "Country": "Finland", "Phone": "90-224 8858", "Fax": "90-224 8858"}, {"Id": "WOLZA", "CompanyName": "Wolski  Zajazd", "ContactName": "<PERSON>by<PERSON><PERSON>", "ContactTitle": "Owner", "Address": "<PERSON><PERSON> 68", "City": "Warszawa", "Region": "Eastern Europe", "PostalCode": "01-012", "Country": "Poland", "Phone": "(26) 642-7012", "Fax": "(26) 642-7012"}]}, {"Name": "Shipper", "Fields": [{"Name": "Id", "NativeType": "INTEGER", "IsNullable": true, "IsPrimaryKey": true}, {"Name": "CompanyName", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "Phone", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}], "KeyConstraints": [{"Columns": ["Id"], "ReferencedColumns": []}], "Relationships": [], "Data": [{"Id": 1, "CompanyName": "Speedy Express", "Phone": "(*************"}, {"Id": 2, "CompanyName": "United Package", "Phone": "(*************"}, {"Id": 3, "CompanyName": "Federal Shipping", "Phone": "(*************"}]}, {"Name": "Supplier", "Fields": [{"Name": "Id", "NativeType": "INTEGER", "IsNullable": true, "IsPrimaryKey": true}, {"Name": "CompanyName", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "ContactName", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "ContactTitle", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "Address", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "City", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "Region", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "PostalCode", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "Country", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "Phone", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "Fax", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "HomePage", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}], "KeyConstraints": [{"Columns": ["Id"], "ReferencedColumns": []}], "Relationships": [], "Data": [{"Id": 1, "CompanyName": "Exotic Liquids", "ContactName": "<PERSON>", "ContactTitle": "Purchasing Manager", "Address": "49 Gilbert <PERSON>.", "City": "London", "Region": "British Isles", "PostalCode": "EC1 4SD", "Country": "UK", "Phone": "(*************"}, {"Id": 2, "CompanyName": "New Orleans Cajun Delights", "ContactName": "<PERSON>", "ContactTitle": "Order Administrator", "Address": "P.O. Box 78934", "City": "New Orleans", "Region": "North America", "PostalCode": "70117", "Country": "USA", "Phone": "(*************", "HomePage": "#CAJUN.HTM#"}, {"Id": 28, "CompanyName": "Gai pâturage", "ContactName": "<PERSON><PERSON>", "ContactTitle": "Sales Representative", "Address": "Bat. B 3, rue des Alpes", "City": "<PERSON><PERSON>", "Region": "Western Europe", "PostalCode": "74000", "Country": "France", "Phone": "***********", "Fax": "***********"}, {"Id": 29, "CompanyName": "Forêts d'érables", "ContactName": "<PERSON><PERSON>", "ContactTitle": "Accounting Manager", "Address": "148 rue Chasseur", "City": "Ste-Hya<PERSON>the", "Region": "North America", "PostalCode": "J2S 7S8", "Country": "Canada", "Phone": "(*************", "Fax": "(*************"}]}, {"Name": "Order", "Fields": [{"Name": "Id", "NativeType": "INTEGER", "IsNullable": true, "IsPrimaryKey": true}, {"Name": "CustomerId", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "EmployeeId", "NativeType": "INTEGER"}, {"Name": "OrderDate", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "RequiredDate", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "ShippedDate", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "ShipVia", "NativeType": "INTEGER", "IsNullable": true}, {"Name": "Freight", "Type": "Decimal", "NativeType": "DECIMAL"}, {"Name": "ShipName", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "ShipAddress", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "ShipCity", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "ShipRegion", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "ShipPostalCode", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "ShipCountry", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}], "KeyConstraints": [{"Columns": ["Id"], "ReferencedColumns": []}], "Relationships": [], "Data": [{"Id": 10248, "CustomerId": "VINET", "EmployeeId": 5, "OrderDate": "2012-07-04", "RequiredDate": "2012-08-01", "ShippedDate": "2012-07-16", "ShipVia": 3, "Freight": 32.38, "ShipName": "Vins et alcools Chevalier", "ShipAddress": "59 rue de l'Abbaye", "ShipCity": "Reims", "ShipRegion": "Western Europe", "ShipPostalCode": "51100", "ShipCountry": "France"}, {"Id": 10249, "CustomerId": "TOMSP", "EmployeeId": 6, "OrderDate": "2012-07-05", "RequiredDate": "2012-08-16", "ShippedDate": "2012-07-10", "ShipVia": 1, "Freight": 11.61, "ShipName": "Toms Spezialitäten", "ShipAddress": "Luisenstr. 48", "ShipCity": "Münster", "ShipRegion": "Western Europe", "ShipPostalCode": "44087", "ShipCountry": "Germany"}, {"Id": 11076, "CustomerId": "BONAP", "EmployeeId": 4, "OrderDate": "2014-05-06", "RequiredDate": "2014-06-03", "ShipVia": 2, "Freight": 38.28, "ShipName": "Bon app'", "ShipAddress": "12, rue des Bouchers", "ShipCity": "Marseille", "ShipRegion": "Western Europe", "ShipPostalCode": "13008", "ShipCountry": "France"}, {"Id": 11077, "CustomerId": "RATTC", "EmployeeId": 1, "OrderDate": "2014-05-06", "RequiredDate": "2014-06-03", "ShipVia": 2, "Freight": 8.53, "ShipName": "Rattlesnake Canyon Grocery", "ShipAddress": "2817 <PERSON>.", "ShipCity": "Albuquerque", "ShipRegion": "North America", "ShipPostalCode": "87110", "ShipCountry": "USA"}]}, {"Name": "Product", "Fields": [{"Name": "Id", "NativeType": "INTEGER", "IsNullable": true, "IsPrimaryKey": true}, {"Name": "ProductName", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "SupplierId", "NativeType": "INTEGER"}, {"Name": "CategoryId", "NativeType": "INTEGER"}, {"Name": "QuantityPerUnit", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "UnitPrice", "Type": "Decimal", "NativeType": "DECIMAL"}, {"Name": "UnitsInStock", "NativeType": "INTEGER"}, {"Name": "UnitsOnOrder", "NativeType": "INTEGER"}, {"Name": "ReorderLevel", "NativeType": "INTEGER"}, {"Name": "Discontinued", "NativeType": "INTEGER"}], "KeyConstraints": [{"Columns": ["Id"], "ReferencedColumns": []}], "Relationships": [], "Data": [{"Id": 1, "ProductName": "<PERSON><PERSON>", "SupplierId": 1, "CategoryId": 1, "QuantityPerUnit": "10 boxes x 20 bags", "UnitPrice": 18.0, "UnitsInStock": 39, "UnitsOnOrder": 0, "ReorderLevel": 10, "Discontinued": 0}, {"Id": 2, "ProductName": "<PERSON>", "SupplierId": 1, "CategoryId": 1, "QuantityPerUnit": "24 - 12 oz bottles", "UnitPrice": 19.0, "UnitsInStock": 17, "UnitsOnOrder": 40, "ReorderLevel": 25, "Discontinued": 0}, {"Id": 76, "ProductName": "<PERSON><PERSON>lik<PERSON><PERSON><PERSON>", "SupplierId": 23, "CategoryId": 1, "QuantityPerUnit": "500 ml", "UnitPrice": 18.0, "UnitsInStock": 57, "UnitsOnOrder": 0, "ReorderLevel": 20, "Discontinued": 0}, {"Id": 77, "ProductName": "Original Frankfurter grüne Soße", "SupplierId": 12, "CategoryId": 2, "QuantityPerUnit": "12 boxes", "UnitPrice": 13.0, "UnitsInStock": 32, "UnitsOnOrder": 0, "ReorderLevel": 15, "Discontinued": 0}]}, {"Name": "OrderDetail", "Fields": [{"Name": "Id", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true, "IsPrimaryKey": true}, {"Name": "OrderId", "NativeType": "INTEGER"}, {"Name": "ProductId", "NativeType": "INTEGER"}, {"Name": "UnitPrice", "Type": "Decimal", "NativeType": "DECIMAL"}, {"Name": "Quantity", "NativeType": "INTEGER"}, {"Name": "Discount", "Type": "Decimal", "NativeType": "DOUBLE"}], "KeyConstraints": [{"Columns": ["Id"], "ReferencedColumns": []}], "Relationships": [], "Data": [{"Id": "10248/11", "OrderId": 10248, "ProductId": 11, "UnitPrice": 14.0, "Quantity": 12, "Discount": 0.0}, {"Id": "10248/42", "OrderId": 10248, "ProductId": 42, "UnitPrice": 9.8, "Quantity": 10, "Discount": 0.0}, {"Id": "11077/77", "OrderId": 11077, "ProductId": 77, "UnitPrice": 13.0, "Quantity": 2, "Discount": 0.0}, {"Id": "11077/8", "OrderId": 11077, "ProductId": 8, "UnitPrice": 40.0, "Quantity": 2, "Discount": 0.1}]}, {"Name": "CustomerCustomerDemo", "Fields": [{"Name": "Id", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true, "IsPrimaryKey": true}, {"Name": "CustomerTypeId", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}], "KeyConstraints": [{"Columns": ["Id"], "ReferencedColumns": []}], "Relationships": [], "Data": []}, {"Name": "CustomerDemographic", "Fields": [{"Name": "Id", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true, "IsPrimaryKey": true}, {"Name": "CustomerDesc", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}], "KeyConstraints": [{"Columns": ["Id"], "ReferencedColumns": []}], "Relationships": [], "Data": []}, {"Name": "Region", "Fields": [{"Name": "Id", "NativeType": "INTEGER", "IsNullable": true, "IsPrimaryKey": true}, {"Name": "RegionDescription", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}], "KeyConstraints": [{"Columns": ["Id"], "ReferencedColumns": []}], "Relationships": [], "Data": [{"Id": 1, "RegionDescription": "Eastern"}, {"Id": 2, "RegionDescription": "Western"}, {"Id": 3, "RegionDescription": "Northern"}, {"Id": 4, "RegionDescription": "Southern"}]}, {"Name": "Territory", "Fields": [{"Name": "Id", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true, "IsPrimaryKey": true}, {"Name": "TerritoryDescription", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}, {"Name": "RegionId", "NativeType": "INTEGER"}], "KeyConstraints": [{"Columns": ["Id"], "ReferencedColumns": []}], "Relationships": [], "Data": [{"Id": "01581", "TerritoryDescription": "Westboro", "RegionId": 1}, {"Id": "01730", "TerritoryDescription": "Bedford", "RegionId": 1}, {"Id": "98052", "TerritoryDescription": "<PERSON><PERSON>", "RegionId": 2}, {"Id": "98104", "TerritoryDescription": "Seattle", "RegionId": 2}]}, {"Name": "EmployeeTerritory", "Fields": [{"Name": "Id", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true, "IsPrimaryKey": true}, {"Name": "EmployeeId", "NativeType": "INTEGER"}, {"Name": "TerritoryId", "Type": "String", "NativeType": "VARCHAR", "MaxLength": 8000, "IsNullable": true}], "KeyConstraints": [{"Columns": ["Id"], "ReferencedColumns": []}], "Relationships": [], "Data": [{"Id": "1/06897", "EmployeeId": 1, "TerritoryId": "06897"}, {"Id": "1/19713", "EmployeeId": 1, "TerritoryId": "19713"}, {"Id": "9/55113", "EmployeeId": 9, "TerritoryId": "55113"}, {"Id": "9/55439", "EmployeeId": 9, "TerritoryId": "55439"}]}]}