using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;

namespace Aubrant.TestDataGenerator.Data
{
    /// <summary>
    /// Represents a project configuration, containing multiple data sources.
    /// </summary>
    public class Project
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the name of the project.
        /// </summary>
        [Required]
        public required string Name { get; set; }

        /// <summary>
        /// Gets or sets the description of the project.
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Gets or sets the author of the project.
        /// </summary>
        public string? Author { get; set; }

        /// <summary>
        /// Gets or sets the creation date of the project.
        /// </summary>
        [Required]
        public required DateTime CreatedDate { get; set; }

        /// <summary>
        /// Gets or sets the list of data sources configured for this project.
        /// </summary>
        public virtual ICollection<DataSource> DataSources { get; set; } = new List<DataSource>();

        /// <summary>
        /// Serializes the Project object to a JSON string.
        /// </summary>
        /// <returns>A JSON string representation of the Project.</returns>
        public string Serialize() => JsonConvert.SerializeObject(this, Formatting.Indented);

        /// <summary>
        /// Saves the serialized Project object to a specified file path.
        /// </summary>
        /// <param name="filePath">The absolute path to the file where the Project will be saved.</param>
        public void SaveToFile(string filePath)
        {
            var json = Serialize();
            File.WriteAllText(filePath, json);
        }

        /// <summary>
        /// Loads and deserializes a Project object from a specified file path.
        /// </summary>
        /// <param name="filePath">The absolute path to the file from which the Project will be loaded.</param>
        /// <returns>A deserialized Project object.</returns>
        public static Project? LoadFromFile(string filePath)
        {
            var json = File.ReadAllText(filePath);
            return JsonConvert.DeserializeObject<Project>(json);
        }
    }
}