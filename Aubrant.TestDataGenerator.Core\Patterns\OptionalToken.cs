using Aubrant.TestDataGenerator.Core.Enums;
using System.Text.Json.Serialization;

namespace Aubrant.TestDataGenerator.Core.Patterns
{
    public class OptionalToken : Token
    {
        public string Pattern { get; }
        public Token InnerToken { get; }

        [JsonConstructor]
        public OptionalToken(string name, string pattern, Token innerToken) : this(name, string.Empty, pattern, innerToken)
        {
        }

        public OptionalToken(string name, string description, string pattern, Token innerToken) : base(name, description)
        {
            Pattern = pattern ?? throw new ArgumentNullException(nameof(pattern));
            InnerToken = innerToken ?? throw new ArgumentNullException(nameof(innerToken));
        }

        public override DataValue Generate(DataGeneratorContext context)
        {
            bool include = context.Random.Next(2) == 0;
            if (!include) return new DataValue(DataType.String, string.Empty);
            var value = InnerToken.Generate(context).ToString() ?? string.Empty;
            return new DataValue(DataType.String, Pattern.Replace($"{{{InnerToken.Name}}}", value));
        }
    }
}