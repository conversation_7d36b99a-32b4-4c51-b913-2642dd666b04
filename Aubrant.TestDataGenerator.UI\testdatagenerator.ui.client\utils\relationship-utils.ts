import type { Entity, Relationship, CardinalityInfo, ProcessedRelationship, ConnectionPoint } from "src/types/database"

export const getCardinalityInfo = (cardinality: string): CardinalityInfo => {
  switch (cardinality) {
    case "OneToOne":
      return { source: { type: "one", symbol: "1" }, target: { type: "one", symbol: "1" } }
    case "OneToMany":
      return { source: { type: "one", symbol: "1" }, target: { type: "many", symbol: "*" } }
    case "ManyToOne":
      return { source: { type: "many", symbol: "*" }, target: { type: "one", symbol: "1" } }
    case "ManyToMany":
      return { source: { type: "many", symbol: "*" }, target: { type: "many", symbol: "*" } }
    default:
      return { source: { type: "one", symbol: "1" }, target: { type: "one", symbol: "1" } }
  }
}

export const findEntityByName = (entities: Entity[], entityName: string): Entity | undefined => {
  // First try exact match with schema.name format
  let entity = entities.find((e) => `${e.Schema}.${e.Name}` === entityName)
  if (entity) return entity

  // Try matching just the table name part (after the dot)
  const tableName = entityName.includes(".") ? entityName.split(".").pop() : entityName
  entity = entities.find((e) => e.Name === tableName)
  if (entity) return entity

  // Try matching without schema (for entities without schema)
  entity = entities.find((e) => e.Name === entityName)
  return entity
}

export const getConnectionPath = (source: Entity, target: Entity, sourceField: string, targetField: string): string => {
  if (!source.position || !target.position) return ""

  const sourceFieldIndex = source.Fields.findIndex((f) => f.Name === sourceField)
  const targetFieldIndex = target.Fields.findIndex((f) => f.Name === targetField)

  // Calculate field positions
  const fieldHeight = 28
  const headerHeight = 70
  const entityWidth = 320

  const sourceFieldY = source.position.y + headerHeight + sourceFieldIndex * fieldHeight + 14
  const targetFieldY = target.position.y + headerHeight + targetFieldIndex * fieldHeight + 14

  // Determine connection sides based on relative positions
  const sourceIsLeft = source.position.x + entityWidth / 2 < target.position.x + entityWidth / 2

  let sourceX, targetX
  if (sourceIsLeft) {
    sourceX = source.position.x + entityWidth // Right edge of source
    targetX = target.position.x // Left edge of target
  } else {
    sourceX = source.position.x // Left edge of source
    targetX = target.position.x + entityWidth // Right edge of target
  }

  // Calculate if we need a straight line or three segments
  const deltaY = Math.abs(targetFieldY - sourceFieldY)

  if (deltaY < 10) {
    // Straight horizontal line
    return `M ${sourceX} ${sourceFieldY} L ${targetX} ${targetFieldY}`
  } else {
    // Three-segment line (L-shaped)
    const midX = sourceX + (targetX - sourceX) / 2
    return `M ${sourceX} ${sourceFieldY} L ${midX} ${sourceFieldY} L ${midX} ${targetFieldY} L ${targetX} ${targetFieldY}`
  }
}

export const getConnectionPoint = (entity: Entity, fieldName: string): ConnectionPoint | null => {
  if (!entity.position) return null

  const fieldIndex = entity.Fields.findIndex((f) => f.Name === fieldName)
  if (fieldIndex === -1) return null

  const fieldHeight = 28
  const headerHeight = 70
  const y = entity.position.y + headerHeight + fieldIndex * fieldHeight + 14

  return {
    x: entity.position.x,
    y,
    fieldIndex,
  }
}

export const processRelationships = (
  entities: Entity[],
  selectedRelationship: { relationship: Relationship; sourceEntity: Entity; targetEntity: Entity } | null,
): ProcessedRelationship[] => {
  const processedRelationships = new Set<string>()
  const results: ProcessedRelationship[] = []

  entities.forEach((entity) => {
    entity.Relationships.forEach((rel) => {
      const targetEntity = findEntityByName(entities, rel.TargetEntity)
      if (targetEntity && targetEntity !== entity && entity.position && targetEntity.position) {
        // Create a unique key for this relationship to avoid duplicates
        const relationshipKey = [`${entity.Name}.${rel.SourceField}`, `${targetEntity.Name}.${rel.TargetField}`]
          .sort()
          .join("<->")

        // Skip if we've already processed this relationship
        if (processedRelationships.has(relationshipKey)) {
          return
        }
        processedRelationships.add(relationshipKey)

        const sourcePath = getConnectionPath(entity, targetEntity, rel.SourceField, rel.TargetField)
        const sourcePoint = getConnectionPoint(entity, rel.SourceField)
        const targetPoint = getConnectionPoint(targetEntity, rel.TargetField)

        if (sourcePath && sourcePoint && targetPoint) {
          results.push({
            relationship: rel,
            sourceEntity: entity,
            targetEntity,
            sourcePath,
            targetPath: sourcePath,
            sourcePoint,
            targetPoint,
            cardinality: getCardinalityInfo(rel.Cardinality),
            isSelected: selectedRelationship?.relationship === rel,
          })
        }
      }
    })
  })

  return results
}
