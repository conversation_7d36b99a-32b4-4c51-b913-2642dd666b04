using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.Data.Enums;
using HotChocolate.Types;

using Aubrant.TestDataGenerator.UI.Server.Types;
using Aubrant.TestDataGenerator.Core.Enums;
namespace Aubrant.TestDataGenerator.UI.Server.Types
{
    public class FieldType : ObjectType<Field>
    {
        protected override void Configure(IObjectTypeDescriptor<Field> descriptor)
        {
            descriptor.Field(f => f.Id).Type<IdType>();
            descriptor.Field(f => f.EntityId).Type<IntType>();
            descriptor.Field(f => f.Name).Type<StringType>();
            descriptor.Field(f => f.Type).Type<EnumType<DataType>>();
            descriptor.Field(f => f.NativeType).Type<StringType>();
            descriptor.Field(f => f.MaxLength).Type<IntType>();
            descriptor.Field(f => f.IsNullable).Type<BooleanType>();
            descriptor.Field(f => f.<PERSON>).Type<BooleanType>();
            descriptor.Field(f => f.IsIdentity).Type<BooleanType>();
            descriptor.Field(f => f.IsUnique).Type<BooleanType>();
            descriptor.Field(f => f.Entity).Type<EntityType>();
            descriptor.Field(f => f.FieldSetting).Type<FieldSettingType>();
        }
    }
}
