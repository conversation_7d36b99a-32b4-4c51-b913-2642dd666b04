using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Functions;
using Aubrant.TestDataGenerator.Generators.Scripts;
using Microsoft.SemanticKernel;

namespace Aubrant.TestDataGenerator.Generators.Functions
{
    /// <summary>
    /// Defines the type of the runtime function.
    /// </summary>
    public enum RuntimeType
    {
        /// <summary>
        /// The function is a C# script.
        /// </summary>
        Script,
        /// <summary>
        /// The function uses an AI prompt.
        /// </summary>
        AI
    }

    /// <summary>
    /// Represents a function that is executed at runtime, either as a C# script or an AI prompt.
    /// </summary>
    public class RuntimeFunction : BaseFunction
    {
        /// <summary>
        /// Gets the type of the runtime function (Script or AI).
        /// </summary>
        public RuntimeType Type { get; }

        /// <summary>
        /// Gets the C# script to be executed. This is only used when the Type is Script.
        /// </summary>
        public string? Script { get; }

        /// <summary>
        /// Gets the AI prompt to be used. This is only used when the Type is AI.
        /// </summary>
        public string? Prompt { get; }

        /// <summary>
        /// Gets the AI provider to be used. This is only used when the Type is AI.
        /// </summary>
        public AIProvider AIProvider { get; }

        /// <summary>
        /// Gets the AI model to be used. This is only used when the Type is AI.
        /// </summary>
        public string? AIModel { get; }

        /// <summary>
        /// Gets the API key for the AI provider. This is only used when the Type is AI and the provider requires it.
        /// </summary>
        public string? APIKey { get; }

        public override string Name => Type == RuntimeType.AI ? "AI" : "Script";
        public override string Description => Type == RuntimeType.AI ? "Generates data using an AI prompt." : "Generates data using a C# script.";
        public override DataType ReturnType => DataType.Any;

        private readonly CSharpScriptRunner? _scriptRunner;
        private readonly Kernel? _aiKernel;
        private KernelFunction? _aiKernelFunction; // Made non-readonly for lazy init

        /// <summary>
        /// Initializes a new instance of the <see cref="RuntimeFunction"/> class.
        /// </summary>
        /// <param name="type">The type of the runtime function (Script or AI).</param>
        /// <param name="script">The C# script to execute (required if type is Script).</param>
        /// <param name="prompt">The AI prompt to use (required if type is AI).</param>
        /// <param name="aiProvider">The AI provider to use (required if type is AI).</param>
        /// <param name="aiModel">The AI model to use (required if type is AI).</param>
        /// <param name="apiKey">The API key for the AI provider (required if type is AI and provider is Google).</param>
        public RuntimeFunction(RuntimeType type, string? script = null, string? prompt = null, AIProvider aiProvider = AIProvider.Google, string? aiModel = null, string? apiKey = null) : base("Runtime")
        {
            Type = type;
            AIProvider = aiProvider;
            AIModel = aiModel;
            APIKey = apiKey;

            if (type == RuntimeType.Script)
            {
                if (string.IsNullOrWhiteSpace(script))
                {
                    throw new ArgumentNullException(nameof(script), "Script cannot be null or empty when the type is Script.");
                }
                Script = script;
                _scriptRunner = new CSharpScriptRunner(Script);
            }
            else if (type == RuntimeType.AI)
            {
                if (string.IsNullOrWhiteSpace(prompt))
                {
                    throw new ArgumentNullException(nameof(prompt), "Prompt cannot be null or empty when the type is AI.");
                }
                if (string.IsNullOrWhiteSpace(aiModel))
                {
                    throw new ArgumentNullException(nameof(aiModel), "AI Model cannot be null or empty when the type is AI.");
                }
                Prompt = prompt;

                // Initialize AI Kernel
                var builder = Kernel.CreateBuilder();
                if (aiProvider == AIProvider.Google)
                {
                    builder.AddGoogleAIGeminiChatCompletion(AIModel, APIKey);
                }
                else if (AIProvider == AIProvider.Ollama)
                {
                    builder.AddOllamaChatCompletion(AIModel, new Uri("http://localhost:11434")); // Assuming default Ollama URL
                }
                else if (AIProvider == AIProvider.Mistral)
                {
                    builder.AddMistralChatCompletion(AIModel, APIKey);
                }
                _aiKernel = builder.Build();

                // _aiKernelFunction will be created lazily in Generate method
            }
        }

        /// <summary>
        /// Generates a value by executing the script or AI prompt.
        /// </summary>
        /// <param name="context">The data generator context.</param>
        /// <returns>A DataValue containing the generated result.</returns>
        public override DataValue Generate(DataGeneratorContext context)
        {
            if (Type == RuntimeType.Script)
            {
                if (_scriptRunner == null) throw new InvalidOperationException("Script runner is not initialized.");

                var globals = new ScriptGlobals(context, Parameters);
                var result = _scriptRunner.Execute(globals);

                return result switch
                {
                    int i => new DataValue(i),
                    decimal d => new DataValue(d),
                    DateTime dt => new DataValue(dt),
                    bool b => new DataValue(b),
                    string s => new DataValue(s),
                    _ => new DataValue(DataType.Any, result)
                };
            }
            else if (Type == RuntimeType.AI)
            {
                if (_aiKernel == null)
                    throw new InvalidOperationException("AI Kernel is not initialized.");

                // Lazily create _aiKernelFunction
                if (_aiKernelFunction == null)
                {
                    var systemPrompt = "You are a data generator. Generate a list of values based on the user prompt. Output only the values, one per line, with no numbering, no bullets, no extra text, and no additional formatting. Each line should contain only the generated value followed by a newline.";
                    var fullPrompt = $"{systemPrompt}\n\n{Prompt}";
                    _aiKernelFunction = _aiKernel.CreateFunctionFromPrompt(fullPrompt, new PromptExecutionSettings());
                }

                var kernelArguments = new KernelArguments();
                foreach (var param in Parameters)
                {
                    kernelArguments[param.Name] = param.Value?.Value;
                }

                var aiGenerator = new AIGenerator(_aiKernel, _aiKernelFunction, kernelArguments);
                return aiGenerator.Generate();
            }
            else
            {
                throw new NotImplementedException("Unknown RuntimeType.");
            }
        }
    }
}