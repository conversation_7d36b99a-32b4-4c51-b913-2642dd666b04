using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.Data.Enums;
using Aubrant.TestDataGenerator.SchemaProfiler.Common;
using Aubrant.TestDataGenerator.SchemaProfiler.Logging;
using System.Data;
using System.Data.Common;
using System.Data.SQLite;

namespace Aubrant.TestDataGenerator.SchemaProfiler.Extractors
{
    /// <summary>
    /// Extracts schema and loads sample data from SQLite databases.
    /// </summary>
    public class SQLiteSchemaExtractor : IDataSourceSchemaExtractor
    {
        private readonly ILogger _logger;
        private readonly int _sampleSize;

        /// <summary>
        /// Initializes a new instance of the <see cref="SQLiteSchemaExtractor"/> class.
        /// </summary>
        /// <param name="logger">Optional. An ILogger instance for logging. If null, a NullLogger will be used.</param>
        public SQLiteSchemaExtractor(SchemaExtractionOptions? options = null, ILogger? logger = null)
        {
            options = options ?? new SchemaExtractionOptions();
            _sampleSize = options.SampleSize;
            _logger = logger ?? new NullLogger();
            _logger.LogInfo($"SQLiteSchemaExtractor initialized with SampleSize: {_sampleSize}");
        }

        /// <summary>
        /// Extracts the schema from a SQLite database asynchronously.
        /// </summary>
        /// <param name="connectionString">The connection string for the SQLite database.</param>
        /// <param name="dataSourceName">Optional. The name to assign to the data source. If null, a name will be inferred from the database file name.</param>
        /// <returns>A Task representing the asynchronous operation, returning the extracted DataSource schema.</returns>
        public async Task<DataSource> ExtractAsync(string connectionString, string? dataSourceName = null)
        {
            _logger.LogInfo($"Extracting schema from SQLite: {connectionString}");
            var effectiveDataSourceName = string.IsNullOrWhiteSpace(dataSourceName) ? Path.GetFileNameWithoutExtension(new SQLiteConnectionStringBuilder(connectionString).DataSource) : dataSourceName;
            var dataSource = new DataSource { Name = effectiveDataSourceName, Type = DataSourceType.RelationalDatabase, Provider = DataSourceProvider.SQLite, ConnectionString = connectionString };

            try
            {
                using (var connection = new SQLiteConnection(connectionString))
                {
                    await connection.OpenAsync();
                    _logger.LogInfo($"Connected to SQLite database: {connection.DataSource}");
                    var tableNames = await GetTableNamesAsync(connection);
                    if (tableNames.Count == 0)
                    {
                        _logger.LogWarning("No tables found in SQLite database.");
                        return dataSource;
                    }

                    var entities = new Dictionary<string, Entity>();
                    foreach (var tableName in tableNames)
                    {
                        _logger.LogInfo($"Extracting schema for table: {tableName}");
                        entities[tableName] = await ExtractTableSchemaAsync(connection, tableName);
                    }
                    _logger.LogInfo("Processing relationships for SQLite database.");
                    await ProcessRelationshipsAsync(connection, entities);
                    dataSource.Entities = entities.Values.ToList();
                }
                _logger.LogInfo($"Schema extracted for {dataSource.Name}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error extracting schema from SQLite database {connectionString}", ex);
                throw;
            }
            return dataSource;
        }

        /// <summary>
        /// Extracts the schema for a specific table in a SQLite database asynchronously.
        /// </summary>
        /// <param name="connection">The SQLiteConnection to use.</param>
        /// <param name="tableName">The name of the table.</param>
        /// <returns>A Task representing the asynchronous operation, returning the Entity schema for the table.</returns>
        private async Task<Entity> ExtractTableSchemaAsync(SQLiteConnection connection, string tableName)
        {
            var entity = new Entity { Name = tableName, Schema = null };
            string columnQuery = $"PRAGMA table_info('{tableName.Replace("'", "''")}')";
            using (var command = new SQLiteCommand(columnQuery, connection))
            using (var reader = await command.ExecuteReaderAsync())
            {
                while (await reader.ReadAsync())
                {
                    var fullNativeType = reader["type"].ToString()!;
                    var field = new Field
                    {
                        Name = reader["name"].ToString(),
                        Type = MapSqliteTypeToDataType(fullNativeType),
                        NativeType = fullNativeType,
                        IsNullable = Convert.ToInt32(reader["notnull"]) == 0,
                        IsPrimaryKey = Convert.ToInt32(reader["pk"]) > 0,
                        IsIdentity = false
                    };

                    var match = System.Text.RegularExpressions.Regex.Match(fullNativeType, @"^(.*?)\s*\((.*?)\)$");
                    if (match.Success && int.TryParse(match.Groups[2].Value, out var length))
                    {
                        field.NativeType = match.Groups[1].Value;
                        field.MaxLength = length;
                    }

                    entity.Fields.Add(field);

                    if (field.IsPrimaryKey)
                    {
                        var pk = entity.KeyConstraints.FirstOrDefault(k => k.KeyType == KeyType.Primary);
                        if (pk == null)
                        {
                            pk = new KeyConstraint { KeyType = KeyType.Primary, Columns = new List<string>() };
                            entity.KeyConstraints.Add(pk);
                        }
                        pk.Columns.Add(field.Name!);
                    }
                }
            }
            return entity;
        }

        /// <summary>
        /// Processes and infers relationships (foreign keys) between entities in a SQLite database asynchronously.
        /// </summary>
        /// <param name="connection">The SQLiteConnection to use.</param>
        /// <param name="entities">A dictionary of entities (tables) to process.</param>
        /// <returns>A Task representing the asynchronous operation.</returns>
        private async Task ProcessRelationshipsAsync(SQLiteConnection connection, Dictionary<string, Entity> entities)
        {
            var tableNames = entities.Keys.ToList();
            var uniqueIndexes = await GetUniqueIndexesAsync(connection, tableNames);

            foreach (var tableName in tableNames)
            {
                string fkQuery = $"PRAGMA foreign_key_list('{tableName.Replace("'", "''")}')";
                using (var fkCommand = new SQLiteCommand(fkQuery, connection))
                using (var fkReader = await fkCommand.ExecuteReaderAsync())
                {
                    var fkGroups = (fkReader.Cast<DbDataRecord>().ToList()).GroupBy(rec => rec["id"]).Select(g => new { SourceTable = tableName, TargetTable = g.First()["table"].ToString(), SourceColumns = g.OrderBy(fk => fk["seq"]).Select(fk => fk["from"].ToString()!).ToList(), TargetColumns = g.OrderBy(fk => fk["seq"]).Select(fk => fk["to"].ToString()!).ToList() }).ToList();
                    foreach (var fk in fkGroups)
                    {
                        if (entities.TryGetValue(fk.SourceTable, out var sourceEntity) && entities.TryGetValue(fk.TargetTable, out var targetEntity))
                        {
                            bool isOneToOne = uniqueIndexes.TryGetValue(fk.SourceTable, out var indexes) && indexes.Any(cols => cols.SetEquals(fk.SourceColumns));
                            sourceEntity.Relationships.Add(new Relationship { SourceEntityId = sourceEntity.Id, TargetEntity = fk.TargetTable, Cardinality = isOneToOne ? Cardinality.OneToOne : Cardinality.ManyToOne, SourceField = string.Join(", ", fk.SourceColumns), TargetField = string.Join(", ", fk.TargetColumns) });
                            sourceEntity.KeyConstraints.Add(new KeyConstraint { KeyType = KeyType.Foreign, Columns = fk.SourceColumns, ReferencedEntity = fk.TargetTable, ReferencedColumns = fk.TargetColumns });
                            targetEntity.Relationships.Add(new Relationship { SourceEntityId = targetEntity.Id, TargetEntity = fk.SourceTable, Cardinality = isOneToOne ? Cardinality.OneToOne : Cardinality.OneToMany, SourceField = string.Join(", ", fk.TargetColumns), TargetField = string.Join(", ", fk.SourceColumns) });
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Loads sample data into the provided DataSource from a SQLite database asynchronously.
        /// </summary>
        /// <param name="dataSource">The DataSource object to populate with sample data.</param>
        /// <param name="connectionString">The connection string for the SQLite database.</param>
        /// <param name="sampleSize">The number of sample rows to load for each entity.</param>
        /// <returns>A Task representing the asynchronous operation.</returns>
        public async Task LoadSampleDataAsync(DataSource dataSource, string connectionString, int? sampleSize = null)
        {
            sampleSize = sampleSize ?? _sampleSize;
            _logger.LogInfo($"Loading sample data for SQLite: {dataSource.Name} (sample size: {sampleSize})");
            try
            {
                using (var connection = new SQLiteConnection(connectionString))
                {
                    await connection.OpenAsync();
                    foreach (var entity in dataSource.Entities)
                    {
                        _logger.LogInfo($"Loading sample data for entity: {entity.Name}");
                        await LoadSampleDataForEntityAsync(connection, entity, sampleSize.Value);
                    }
                }
                _logger.LogInfo($"Sample data loaded for {dataSource.Name}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error loading sample data from SQLite database {connectionString}", ex);
                throw;
            }
        }

        /// <summary>
        /// Loads sample data for a specific entity from a SQLite database asynchronously.
        /// </summary>
        /// <param name="connection">The SQLiteConnection to use.</param>
        /// <param name="entity">The entity to populate with sample data.</param>
        /// <param name="sampleSize">The number of sample rows to load.</param>
        /// <returns>A Task representing the asynchronous operation.</returns>
        private async Task LoadSampleDataForEntityAsync(SQLiteConnection connection, Entity entity, int sampleSize)
        {
            var dataTable = new DataTable(entity.Name);
            foreach (var field in entity.Fields)
            {
                dataTable.Columns.Add(new DataColumn(field.Name!, DataTypeMapper.ToSystemType(field.Type)));
            }

            var pk = entity.KeyConstraints.FirstOrDefault(k => k.KeyType == KeyType.Primary);
            string orderByColumn = pk != null && pk.Columns.Any() ? $"[{pk.Columns.First()}]" : "rowid";
            int halfSampleSize = sampleSize > 0 ? Math.Max(1, sampleSize / 2) : 0;

            string query = $"SELECT * FROM (SELECT * FROM [{entity.Name}] ORDER BY {orderByColumn} ASC LIMIT {halfSampleSize}) UNION SELECT * FROM (SELECT * FROM [{entity.Name}] ORDER BY {orderByColumn} DESC LIMIT {halfSampleSize})";
            using (var command = new SQLiteCommand(query, connection))
            using (var reader = await command.ExecuteReaderAsync())
            {
                while (await reader.ReadAsync())
                {
                    var row = dataTable.NewRow();
                    foreach (var field in entity.Fields)
                    {
                        var value = reader[field.Name!];
                        row[field.Name!] = value == DBNull.Value ? DBNull.Value : Convert.ChangeType(value, DataTypeMapper.ToSystemType(field.Type));
                    }
                    dataTable.Rows.Add(row);
                }
            }
            entity.Data = dataTable;
        }

        /// <summary>
        /// Retrieves a list of table names from a SQLite database asynchronously.
        /// </summary>
        /// <param name="connection">The SQLiteConnection to use.</param>
        /// <returns>A Task representing the asynchronous operation, returning a list of table names.</returns>
        private async Task<List<string>> GetTableNamesAsync(SQLiteConnection connection)
        {
            var tableNames = new List<string>();
            using (var command = new SQLiteCommand("SELECT name FROM sqlite_master WHERE type = 'table' AND name NOT LIKE 'sqlite_%'", connection))
            using (var reader = await command.ExecuteReaderAsync())
            {
                while (await reader.ReadAsync()) tableNames.Add(reader.GetString(0));
            }
            return tableNames;
        }

        /// <summary>
        /// Retrieves unique indexes for tables in a SQLite database asynchronously.
        /// </summary>
        /// <param name="connection">The SQLiteConnection to use.</param>
        /// <param name="tableNames">A list of table names to retrieve unique indexes for.</param>
        /// <returns>A dictionary where the key is the table name and the value is a list of hash sets of column names forming unique indexes.</returns>
        private async Task<Dictionary<string, List<HashSet<string>>>> GetUniqueIndexesAsync(SQLiteConnection connection, List<string> tableNames)
        {
            var uniqueIndexes = new Dictionary<string, List<HashSet<string>>>();
            foreach (var tableName in tableNames)
            {
                uniqueIndexes[tableName] = new List<HashSet<string>>();
                var indexes = new List<string>();
                using (var command = new SQLiteCommand($"PRAGMA index_list('{tableName.Replace("'", "''")}')", connection))
                using (var reader = await command.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync()) if (Convert.ToInt32(reader["unique"]) == 1) indexes.Add(reader["name"].ToString()!);
                }
                foreach (var indexName in indexes)
                {
                    var columns = new HashSet<string>();
                    using (var colCommand = new SQLiteCommand($"PRAGMA index_info('{indexName.Replace("'", "''")}')", connection))
                    using (var colReader = await colCommand.ExecuteReaderAsync())
                    {
                        while (await colReader.ReadAsync()) columns.Add(colReader["name"].ToString()!);
                    }
                    uniqueIndexes[tableName].Add(columns);
                }
            }
            return uniqueIndexes;
        }

        /// <summary>
        /// Maps a SQLite native data type string to a custom DataType enum value.
        /// </summary>
        /// <param name="sqliteType">The native SQLite data type string.</param>
        /// <returns>The corresponding DataType enum value.</returns>
        private DataType MapSqliteTypeToDataType(string sqliteType)
        {
            return sqliteType.Split('(')[0].ToLower().Trim() switch
            {
                "integer" or "int" or "tinyint" or "smallint" or "bigint" => DataType.Integer,
                "real" or "float" or "double" or "numeric" or "decimal" or "money" or "currency" => DataType.Decimal,
                "text" or "varchar" or "nvarchar" or "char" or "guid" or "uniqueidentifier" => DataType.String,
                "datetime" or "date" or "time" or "timestamp" => DataType.DateTime,
                "boolean" or "bit" => DataType.Boolean,
                "blob" => DataType.Binary,
                _ => DataType.Unsupported
            };
        }
    }
}