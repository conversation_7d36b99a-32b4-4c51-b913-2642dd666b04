{"Generators": [{"Name": "City", "Description": "Generates a realistic city name based on the specified country, optional state, and output locale.", "ReturnType": "String", "Parameters": [{"Name": "country", "Type": "String", "Description": "The country to generate the city in.", "DefaultValue": "US"}, {"Name": "locale", "Type": "String", "Description": "The output locale.", "DefaultValue": "en"}, {"Name": "state", "Type": "String", "Description": "The state to generate the city in."}]}, {"Name": "AI", "Description": "Generates diverse test data by interpreting a natural language prompt using an external AI model. Ideal for complex, original or context-dependent data generation.", "ReturnType": "String", "Properties": {"Prompt": {"Type": "String", "Description": "The prompt to be used by the AI model."}}}, {"Name": "Random", "Description": "Selects a Random value in a Range", "ReturnType": "Integer | Decimal | Date", "Properties": {"Min": {"Type": "DataValue", "Description": "The minimum bound for random value generation"}, "Max": {"Type": "DataValue", "Description": "The maximum bound for random value generation"}}}, {"Name": "Constant", "Description": "Constant Value", "ReturnType": "Any", "Properties": {"Value": {"Type": "DataValue", "Description": "The constant DataValue that this generator will always return"}}}, {"Name": "Choice", "Description": "Selects a value from a predefined list of options, which can include direct values, numerical or date ranges, and supports probabilistic distribution. Use this generator when a field's data must come from a known, finite set of possibilities, or when specific values need to appear with controlled frequencies.", "ReturnType": "Any", "Properties": {"Options": {"Type": "array", "Description": "List of Options", "Items": {"Type": "object", "Properties": {"Value": {"Type": "DataValue", "Description": "Option Value"}, "Range": {"Type": "object", "Description": "Option Range of Values", "Properties": {"Min": {"Type": "DataValue", "Description": "Min value in Range"}, "Max": {"Type": "DataValue", "Description": "Max value in Range"}}}, "Probability": {"Type": "Double", "Description": "Probability of selecting value or range"}, "Label": {"Type": "String", "Description": "An optional label for the value. To guide the user on the meaning of the value in the UI, not used to generate the value."}}}}}}]}