using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Functions;
using System;

namespace Aubrant.TestDataGenerator.Generators.Functions{
    /// <summary>
    /// A generator function that produces random decimal values following a Log-Normal distribution.
    /// </summary>
    public class LogNormalDistributionFunction : BaseFunction
    {
        private bool _hasDeviate;
        private double _storedDeviate;

        public override string Name => "Log-Normal Distribution";
        public override string Description => "Generates random numbers from a Log-Normal distribution.";
        public override DataType ReturnType => DataType.Decimal;

        /// <summary>
        /// Initializes a new instance of the LogNormalDistributionFunction class.
        /// Defines parameters for Mu (mean of the logarithm) and Sigma (standard deviation of the logarithm).
        /// </summary>
        public LogNormalDistributionFunction() : base("Statistical")
        {
            Parameters.Add(new Parameter("Mu", "The mean of the logarithm.", DataType.Decimal, new DataValue(0.0M))); // Default mean of log is 0
            Parameters.Add(new Parameter("Sigma", "The standard deviation of the logarithm.", DataType.Decimal, new DataValue(1.0M))); // Default std dev of log is 1
        }

        /// <summary>
        /// Generates a random decimal value from a Log-Normal distribution based on the configured Mu and Sigma.
        /// Uses the Box-Muller transform to generate a normal deviate, then exponentiates it.
        /// </summary>
        /// <param name="context">The data generator context.</param>
        /// <returns>A DataValue containing the log-normally distributed decimal number.</returns>
        /// <exception cref="InvalidOperationException">Thrown if Mu or Sigma parameters are missing or invalid.</exception>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if Sigma is non-positive.</exception>
        public override DataValue Generate(DataGeneratorContext context)
        {
            double mu = Parameters["Mu"].Value?.ToDecimal()?.ToDouble() ??
                        throw new InvalidOperationException("Mu parameter is missing or invalid.");
            double sigma = Parameters["Sigma"].Value?.ToDecimal()?.ToDouble() ??
                           throw new InvalidOperationException("Sigma parameter is missing or invalid.");

            if (sigma <= 0)
            {
                throw new ArgumentOutOfRangeException(nameof(sigma), "Sigma must be positive for Log-Normal distribution.");
            }

            double u1, u2, randStdNormal;

            if (_hasDeviate)
            {
                _hasDeviate = false;
                randStdNormal = _storedDeviate;
            }
            else
            {
                u1 = 1.0 - context.Random.NextDouble(); // uniform(0,1], avoid log(0)
                u2 = 1.0 - context.Random.NextDouble(); // uniform(0,1], avoid log(0)

                randStdNormal = Math.Sqrt(-2.0 * Math.Log(u1)) * Math.Sin(2.0 * Math.PI * u2);
                _storedDeviate = Math.Sqrt(-2.0 * Math.Log(u1)) * Math.Cos(2.0 * Math.PI * u2);
                _hasDeviate = true;
            }

            // Transform standard normal to log-normal
            double result = Math.Exp(mu + sigma * randStdNormal);

            return new DataValue(DataType.Decimal, (decimal)result);
        }
    }
}
