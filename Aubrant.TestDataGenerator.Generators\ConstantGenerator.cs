﻿using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Interfaces;
using System.Text.Json;

namespace Aubrant.TestDataGenerator.Generators
{
    /// <summary>
    /// Implements the IGenerator interface to return a constant DataValue.
    /// This generator is useful when a fixed, unchanging value is required for test data generation.
    /// </summary>
    public class ConstantGenerator : ICompiledGenerator
    {
        public string Name => "Constant";

        public string Description => "Generates a single, unchanging scalar value (e.g., string, number, boolean, date).";

        public DataType ReturnType => DataType.Any;

        /// <summary>
        /// The constant DataValue that this generator will always return.
        /// </summary>
        [System.ComponentModel.Description("The actual constant scalar value (e.g., string, number, boolean, date) that this generator will always return.")]
        public DataValue Value { get; set; }

        /// <summary>
        /// Parameterless constructor required for Semantic Analyzer
        /// </summary>
        public ConstantGenerator() { }

        /// <summary>
        /// Initializes a new instance of the ConstantGenerator class with a predefined DataValue.
        /// This is the primary constructor.
        /// </summary>
        /// <param name="value">The DataValue object to be returned by this generator.</param>
        public ConstantGenerator(DataValue value)
        {
            Value = value;
        }

        /// <summary>
        /// Initializes a new instance of the ConstantGenerator class with a constant string value.
        /// Automatically wraps the string in a DataValue object.
        /// </summary>
        /// <param name="stringValue">The constant string value to be generated.</param>
        public ConstantGenerator(string stringValue)
        {
            Value = new DataValue(stringValue);
        }

        /// <summary>
        /// Initializes a new instance of the ConstantGenerator class with a constant integer value.
        /// Automatically wraps the integer in a DataValue object.
        /// </summary>
        /// <param name="integerValue">The constant integer value to be generated.</param>
        public ConstantGenerator(int integerValue)
        {
            Value = new DataValue(integerValue);
        }

        /// <summary>
        /// Initializes a new instance of the ConstantGenerator class with a constant decimal value.
        /// Automatically wraps the decimal in a DataValue object.
        /// </summary>
        /// <param name="decimalValue">The constant decimal value to be generated.</param>
        public ConstantGenerator(decimal decimalValue)
        {
            Value = new DataValue(decimalValue);
        }

        /// <summary>
        /// Initializes a new instance of the ConstantGenerator class with a constant DateTime value.
        /// Automatically wraps the DateTime in a DataValue object.
        /// </summary>
        /// <param name="dateValue">The constant DateTime value to be generated.</param>
        public ConstantGenerator(DateTime dateValue)
        {
            Value = new DataValue(dateValue);
        }

        /// <summary>
        /// Initializes a new instance of the ConstantGenerator class with a constant boolean value.
        /// Automatically wraps the boolean in a DataValue object.
        /// </summary>
        /// <param name="booleanValue">The constant boolean value to be generated.</param>
        public ConstantGenerator(bool booleanValue)
        {
            Value = new DataValue(booleanValue);
        }

        /// <summary>
        /// Generates and returns the constant DataValue stored in this generator.
        /// </summary>
        /// <returns>The predefined constant DataValue.</returns>
        public DataValue Generate()
        {
            return Value;
        }

        public void Parse(DataType dataType, IDictionary<string, object> settings)
        {
            if (settings.TryGetValue("Value", out object value))
            {
                if (value is JsonElement element)
                {
                    Value = new DataValue(dataType, element);
                }
                else if (value == null)
                    Value = new DataValue(dataType, DBNull.Value);
                else
                {
                    Value = new DataValue(value.ToString());
                }
            }
        }
    }
}
