using System.Text.RegularExpressions;
using Aubrant.TestDataGenerator.Core;

namespace Aubrant.TestDataGenerator.Core.Utils.Models
{
    public class DateTimeOffsetCalculator : IOffsetCalculator
    {
        public DataValue ApplyOffset(DataValue baseValue, string minOffset, string maxOffset, Random random)
        {
            var baseDate = baseValue.ToDate() ?? throw new InvalidOperationException("Base value must be a valid date.");

            var (minValue, minUnit) = ParseOffset(minOffset);
            var (maxValue, maxUnit) = ParseOffset(maxOffset);

            if (minUnit != maxUnit)
            {
                throw new ArgumentException("Minimum and maximum offsets must have the same unit (e.g., both 'y' or both 'd').");
            }

            if (minValue > maxValue)
            {
                (minValue, maxValue) = (maxValue, minValue);
            }

            int randomValue = random.Next(minValue, maxValue + 1);

            return minUnit switch
            {
                'y' or 'Y' => new DataValue(baseDate.AddYears(randomValue)),
                'M' => new DataValue(baseDate.AddMonths(randomValue)),
                'd' or 'D' => new DataValue(baseDate.AddDays(randomValue)),
                'h' or 'H' => new DataValue(baseDate.AddHours(randomValue)),
                'm' => new DataValue(baseDate.AddMinutes(randomValue)),
                's' or 'S' => new DataValue(baseDate.AddSeconds(randomValue)),
                _ => throw new InvalidOperationException("Unsupported unit.")
            };
        }

        private (int value, char unit) ParseOffset(string offset)
        {
            var match = Regex.Match(offset, @"([+-]?\d+)([ymd])");
            if (!match.Success)
            {
                throw new FormatException($"Invalid offset format: {offset}. Expected format like '+1y', '-10d'.");
            }

            int value = int.Parse(match.Groups[1].Value);
            char unit = match.Groups[2].Value[0];

            return (value, unit);
        }
    }
}

