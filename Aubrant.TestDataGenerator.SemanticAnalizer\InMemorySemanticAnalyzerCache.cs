﻿using Aubrant.TestDataGenerator.Data;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Aubrant.TestDataGenerator.SemanticAnalizer
{
    /// <summary>
    /// In-memory implementation of semantic analyzer cache for improved performance
    /// </summary>
    public class InMemorySemanticAnalyzerCache : ISemanticAnalyzerCache
    {
        private readonly ConcurrentDictionary<string, FieldSetting> _fieldCache = new();
        private readonly ConcurrentDictionary<string, Dictionary<string, FieldSetting>> _batchCache = new();
        private readonly TimeSpan _cacheExpiry;
        private readonly ConcurrentDictionary<string, DateTime> _cacheTimestamps = new();

        public InMemorySemanticAnalyzerCache(TimeSpan? cacheExpiry = null)
        {
            _cacheExpiry = cacheExpiry ?? TimeSpan.FromHours(24); // Default 24-hour cache
        }

        public Task<FieldSetting?> GetCachedResultAsync(string fieldSignature)
        {
            if (IsCacheEntryExpired(fieldSignature))
            {
                _fieldCache.TryRemove(fieldSignature, out _);
                _cacheTimestamps.TryRemove(fieldSignature, out _);
                return Task.FromResult<FieldSetting?>(null);
            }

            _fieldCache.TryGetValue(fieldSignature, out var result);
            return Task.FromResult(result);
        }

        public Task SetCachedResultAsync(string fieldSignature, FieldSetting fieldSetting)
        {
            _fieldCache[fieldSignature] = fieldSetting;
            _cacheTimestamps[fieldSignature] = DateTime.UtcNow;
            return Task.CompletedTask;
        }

        public Task<Dictionary<string, FieldSetting>?> GetCachedBatchResultAsync(string batchSignature)
        {
            if (IsCacheEntryExpired(batchSignature))
            {
                _batchCache.TryRemove(batchSignature, out _);
                _cacheTimestamps.TryRemove(batchSignature, out _);
                return Task.FromResult<Dictionary<string, FieldSetting>?>(null);
            }

            _batchCache.TryGetValue(batchSignature, out var result);
            return Task.FromResult(result);
        }

        public Task SetCachedBatchResultAsync(string batchSignature, Dictionary<string, FieldSetting> results)
        {
            _batchCache[batchSignature] = results;
            _cacheTimestamps[batchSignature] = DateTime.UtcNow;
            return Task.CompletedTask;
        }

        public Task ClearCacheAsync()
        {
            _fieldCache.Clear();
            _batchCache.Clear();
            _cacheTimestamps.Clear();
            return Task.CompletedTask;
        }

        private bool IsCacheEntryExpired(string key)
        {
            if (!_cacheTimestamps.TryGetValue(key, out var timestamp))
                return true;

            return DateTime.UtcNow - timestamp > _cacheExpiry;
        }

        /// <summary>
        /// Creates a unique signature for a field based on its characteristics
        /// </summary>
        public static string CreateFieldSignature(Field field, string? sampleData = null)
        {
            var signature = new
            {
                Name = field.Name?.ToLowerInvariant(),
                Type = field.Type.ToString(),
                NativeType = field.NativeType?.ToLowerInvariant(),
                MaxLength = field.MaxLength,
                IsNullable = field.IsNullable,
                IsPrimaryKey = field.IsPrimaryKey,
                IsIdentity = field.IsIdentity,
                IsUnique = field.IsUnique,
                SampleData = sampleData?.ToLowerInvariant()
            };

            return Convert.ToBase64String(
                System.Text.Encoding.UTF8.GetBytes(
                    JsonSerializer.Serialize(signature)
                )
            );
        }

        /// <summary>
        /// Creates a unique signature for a batch of fields
        /// </summary>
        public static string CreateBatchSignature(IEnumerable<Field> fields, Entity entity)
        {
            var fieldSignatures = fields.Select(f => CreateFieldSignature(f)).OrderBy(s => s);
            var batchInfo = new
            {
                EntityName = entity.Name?.ToLowerInvariant(),
                EntitySchema = entity.Schema?.ToLowerInvariant(),
                FieldSignatures = fieldSignatures
            };

            return Convert.ToBase64String(
                System.Text.Encoding.UTF8.GetBytes(
                    JsonSerializer.Serialize(batchInfo)
                )
            );
        }
    }
}
