using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Functions;
using Aubrant.TestDataGenerator.Core.Utils;
using Bogus;

namespace Aubrant.TestDataGenerator.Generators.Functions{
    /// <summary>
    /// A generator function that produces random latitude coordinates based on the specified country, optional state/city, and output locale.
    /// </summary>
    public class LatitudeFunction : BaseFunction
    {
        public override string Name => "Latitude";
        public override string Description => "Generates a random latitude coordinate based on the specified country, optional state/city, and output locale.";
        public override DataType ReturnType => DataType.Decimal;

        /// <summary>
        /// Initializes a new instance of the LatitudeFunction class.
        /// Defines 'country', 'locale', and optional 'state', 'city' parameters.
        /// </summary>
        public LatitudeFunction() : base("Location")
        {
            Parameters.Add(new Parameter("country", "The country to generate the latitude in.", DataType.String, new DataValue("US"))); // Default country is US
            Parameters.Add(new Parameter("state", "The state to generate the latitude in.", DataType.String, null)); // Optional state parameter
            Parameters.Add(new Parameter("city", "The city to generate the latitude in.", DataType.String, null)); // Optional city parameter
            Parameters.Add(new Parameter("format", "The output format of the coordinate.", DataType.String, new DataValue(CoordinateFormat.DecimalDegrees.ToString()))); // Default format is DecimalDegrees
        }

        /// <summary>
        /// Generates a random latitude coordinate using the GeoDataRepository or Bogus library, considering the specified country, optional state/city, and output format.
        /// </summary>
        /// <returns>A DataValue containing the generated latitude as a decimal or string.</returns>
        /// <exception cref="InvalidOperationException">Thrown if any parameter is missing or invalid.</exception>
        public override DataValue Generate(DataGeneratorContext context)
        {
            string countryInput = Parameters["country"].Value?.ToString() ??
                                  throw new InvalidOperationException("Country parameter is missing or invalid.");
            string? stateInput = Parameters["state"].Value?.ToString();
            string? cityInput = Parameters["city"].Value?.ToString();
            CoordinateFormat format = Enum.Parse<CoordinateFormat>(Parameters["format"].Value?.ToString() ?? CoordinateFormat.DecimalDegrees.ToString());

            var faker = new Faker();

            try
            {
                double? centerLat = null;
                double? centerLon = null;
                double? radiusKm = null;

                // Prioritize city-level coordinates
                if (!string.IsNullOrWhiteSpace(cityInput) && !string.IsNullOrWhiteSpace(stateInput))
                {
                    var city = GeoDataRepository.GetCity(countryInput, stateInput, cityInput);
                    if (city?.CenterLatitude.HasValue == true && city.CenterLongitude.HasValue == true && city.RadiusKm.HasValue == true)
                    {
                        centerLat = city.CenterLatitude.Value;
                        centerLon = city.CenterLongitude.Value;
                        radiusKm = city.RadiusKm.Value;
                    }
                }

                // Fallback to state-level coordinates if city not found or no specific coordinates
                if (!centerLat.HasValue && !string.IsNullOrWhiteSpace(stateInput))
                {
                    var state = GeoDataRepository.GetState(countryInput, stateInput);
                    if (state?.CenterLatitude.HasValue == true && state.CenterLongitude.HasValue == true && state.RadiusKm.HasValue == true)
                    {
                        centerLat = state.CenterLatitude.Value;
                        centerLon = state.CenterLongitude.Value;
                        radiusKm = state.RadiusKm.Value;
                    }
                }

                double generatedLatitude;

                if (centerLat.HasValue && centerLon.HasValue && radiusKm.HasValue)
                {
                    generatedLatitude = GenerateRandomLatitude(centerLat.Value, centerLon.Value, radiusKm.Value, context.Random);
                }
                else
                {
                    // Fallback to Bogus.NET if no specific coordinates found in curated data
                    generatedLatitude = faker.Address.Latitude();
                }

                if (format == CoordinateFormat.DegreesMinutesSeconds)
                {
                    return new DataValue(DataType.String, ConvertToDMS(generatedLatitude, "N", "S"));
                }
                else
                {
                    return new DataValue(DataType.Decimal, (decimal)generatedLatitude);
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error generating latitude for country '{countryInput}' with state '{stateInput}', city '{cityInput}': {ex.Message}", ex);
            }
        }

        // Helper method to generate a random latitude within a given radius from a center point
        private double GenerateRandomLatitude(double centerLat, double centerLon, double radiusKm, Random random)
        {
            // Convert radius from km to degrees (approx 1 degree lat = 111 km)
            double radiusDegrees = radiusKm / 111.0;

            // Generate random angle and distance within the circle
            double u = random.NextDouble(); // 0 to 1
            double v = random.NextDouble(); // 0 to 1
            double w = radiusDegrees * Math.Sqrt(u);
            double t = 2 * Math.PI * v;
            double x = w * Math.Cos(t);
            double y = w * Math.Sin(t);

            // Adjust the x coordinate for longitude compression at poles
            double newLat = centerLat + y;
            // Ensure latitude is within valid range (-90 to 90)
            newLat = Math.Max(-90.0, Math.Min(90.0, newLat));

            return newLat;
        }

        private string ConvertToDMS(double coordinate, string positiveChar, string negativeChar)
        {
            string cardinal = coordinate >= 0 ? positiveChar : negativeChar;
            coordinate = Math.Abs(coordinate);
            int degrees = (int)coordinate;
            double minutesDecimal = (coordinate - degrees) * 60;
            int minutes = (int)minutesDecimal;
            double seconds = (minutesDecimal - minutes) * 60;
            return string.Format("{0}°{1:00}'{2:00.0}\"{3}", degrees, minutes, seconds, cardinal);
        }
    }
}
