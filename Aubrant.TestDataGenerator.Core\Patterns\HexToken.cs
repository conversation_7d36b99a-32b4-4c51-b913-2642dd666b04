using Aubrant.TestDataGenerator.Core.Enums;
using System.Text.Json.Serialization;

namespace Aubrant.TestDataGenerator.Core.Patterns
{
    public class HexToken : Token
    {
        public int Length { get; }

        [JsonConverter(typeof(JsonStringEnumConverter))]
        public CaseOption Case { get; }

        [JsonConstructor]
        public HexToken(string name, int length, CaseOption @case = CaseOption.Random) : this(name, string.Empty, length, @case)
        {
        }

        public HexToken(string name, string description, int length, CaseOption caseOption = CaseOption.Random) : base(name, description)
        {
            if (length <= 0) throw new ArgumentException("Length must be positive.");
            Length = length;
            Case = caseOption;
        }

        public override DataValue Generate(DataGeneratorContext context)
        {
            var bytes = new byte[(Length + 1) / 2];
            context.Random.NextBytes(bytes);

            var hex = BitConverter.ToString(bytes).Replace("-", "");
            if (hex.Length > Length) hex = hex.Substring(0, Length);
            else if (hex.Length < Length) hex = hex.PadLeft(Length, '0');

            hex = Case switch
            {
                CaseOption.Upper => hex.ToUpperInvariant(),
                CaseOption.Lower => hex.ToLowerInvariant(),
                _ => context.Random.Next(2) == 0 ? hex.ToUpperInvariant() : hex.ToLowerInvariant()
            };

            return new DataValue(DataType.String, hex);
        }
    }
}