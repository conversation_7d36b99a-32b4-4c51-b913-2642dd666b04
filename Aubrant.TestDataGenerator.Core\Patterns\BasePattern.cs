﻿using System.Text.Json;
using System.Text.Json.Serialization;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Interfaces;
using Aubrant.TestDataGenerator.Core.Utils;

namespace Aubrant.TestDataGenerator.Core.Patterns
{
    public abstract class BasePattern : IRuntimeGenerator
    {
        private string pattern = string.Empty;

        public string Name { get; init; }

        public string Description { get; init; }

        [JsonConverter(typeof(JsonStringEnumConverter))]
        public DataType ReturnType => DataType.String;

        public List<Token> Tokens { get; init; } = new List<Token>();

        public string Pattern
        {
            get
            {
                return pattern;
            }
            set
            {
                if (string.IsNullOrWhiteSpace(value))
                    throw new ArgumentException("Pattern cannot be empty.");

                pattern = value;
            }
        }

        public BasePattern() { }

        public BasePattern(string name, string description, string pattern, List<Token> tokens)
        {
            Name = name;
            Description = description;
            Pattern = pattern;
            Tokens.AddRange(tokens);
        }

        public virtual DataValue Generate(DataGeneratorContext context)
        {
            var result = Pattern;

            foreach (var token in Tokens)
            {
                while (result.Contains($"{{{token.Name}}}"))
                {
                    var tokenValue = token.Generate(context);
                    result = result.ReplaceFirst($"{{{token.Name}}}", tokenValue.ToString() ?? string.Empty, StringComparison.OrdinalIgnoreCase);
                }
            }

            return new DataValue(DataType.String, result);
        }

        public virtual void Parse(DataType dataType, IDictionary<string, object> settings)
        {
            if (settings.TryGetValue("Pattern", out object? patternObject) && patternObject is JsonElement patternElement && patternElement.ValueKind == JsonValueKind.String)
            {
                Pattern = patternElement.GetString();
            }

            if (settings.TryGetValue("Tokens", out object? tokensObject) && tokensObject is JsonElement tokensElement)
            {
                var options = new JsonSerializerOptions();
                options.Converters.Add(new TokenConverter());
                var tokens = JsonSerializer.Deserialize<List<Token>>(tokensElement.GetRawText(), options);
                if (tokens != null)
                {
                    Tokens.Clear();
                    Tokens.AddRange(tokens);
                }
            }
        }
    }
}
