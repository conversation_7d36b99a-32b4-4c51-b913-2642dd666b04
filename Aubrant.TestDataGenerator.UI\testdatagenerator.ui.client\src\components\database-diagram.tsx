"use client";

import { useState } from "react";

import { sampleProject } from "../../data/sample-data";
import { DiagramHeader } from "./diagram-header";
import { useDiagramState } from "../hooks/use-diagram-state";
import { PropertiesPanel } from "./properties-panel";
import { DiagramCanvas } from "./diagram-canvas";

export default function DatabaseDiagram() {
  const [isDarkMode, setIsDarkMode] = useState(false);

  const {
    entities,
    selectedEntities,
    activeEntity,
    selectedField,
    selectedRelationship,
    scale,
    panOffset,
    alignmentGuides,
    marquee,
    canvasRef,
    svgRef,
    arrangeLeftRight,
    arrangeSnowflake,
    arrangeCompact,
    zoomTo100,
    zoomIn,
    zoomOut,
    handleFitToWindow,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleWheel,
    handleEntityClick,
    handleFieldClick,
    handleRelationshipClick,
    handleEntityMouseDown,
    zoomToSelection,
  } = useDiagramState(
    sampleProject.DataSources.flatMap((ds) =>
      ds.Entities.map((entity) => ({
        ...entity,
        dataSourceName: ds.Name, // Add dataSourceName to each entity
      }))
    )
  );

  return (
    <div className={`h-screen flex ${isDarkMode ? "dark" : ""}`}>
      <div className="flex-1 flex flex-col bg-background">
        <DiagramHeader
          isDarkMode={isDarkMode}
          onToggleDarkMode={() => setIsDarkMode(!isDarkMode)}
          onArrangeLeftRight={arrangeLeftRight}
          onArrangeSnowflake={arrangeSnowflake}
          onArrangeCompact={arrangeCompact}
          scale={scale}
          onZoomIn={zoomIn}
          onZoomOut={zoomOut}
          onZoomTo100={zoomTo100}
          onFitToWindow={handleFitToWindow}
          onZoomToSelection={zoomToSelection}
        />

        <DiagramCanvas
          entities={entities}
          selectedEntities={selectedEntities}
          activeEntity={activeEntity}
          selectedField={selectedField}
          selectedRelationship={selectedRelationship}
          panOffset={panOffset}
          scale={scale}
          alignmentGuides={alignmentGuides}
          marquee={marquee}
          canvasRef={canvasRef}
          svgRef={svgRef}
          handleMouseDown={handleMouseDown}
          handleMouseMove={handleMouseMove}
          handleMouseUp={handleMouseUp}
          handleWheel={handleWheel}
          handleEntityClick={handleEntityClick}
          handleFieldClick={handleFieldClick}
          handleRelationshipClick={handleRelationshipClick}
          handleEntityMouseDown={handleEntityMouseDown}
        />
      </div>

      <PropertiesPanel
        selectedEntity={entities.find(e => e.Name === activeEntity) || null}
        selectedField={selectedField}
        selectedRelationship={selectedRelationship}
      />
    </div>
  );
}
