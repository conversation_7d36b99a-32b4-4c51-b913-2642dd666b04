namespace Aubrant.TestDataGenerator.SemanticAnalizer
{
    public class GeneratorInfo
    {
        public required string Name { get; set; }
        public required string Description { get; set; }
        public required string ReturnType { get; set; }
        public string? Summary { get; set; } // Added Summary for the generator class
        public List<ParameterInfo> Parameters { get; set; } = new List<ParameterInfo>();
    }

    public class ParameterInfo
    {
        public required string Name { get; set; }
        public required string Type { get; set; }
        public bool IsOptional { get; set; }
        public object? DefaultValue { get; set; }
        public string? Summary { get; set; }
    }
}