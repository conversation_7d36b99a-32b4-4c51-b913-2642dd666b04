﻿using Aubrant.TestDataGenerator.Core.Patterns;

namespace Aubrant.TestDataGenerator.Generators.Patterns
{
    public class USPostalCodePattern : BasePattern
    {
        public USPostalCodePattern() : base(
            name: "USPostalCode",
            description: "U.S. ZIP code in 5-digit or ZIP+4 format",
            pattern: "{ZIP5}{PLUS4}",
            tokens: new List<Token>
            {
                new DigitsToken("ZIP5", "Five-digit ZIP code", 5),
                new OptionalToken("PLUS4", "Optional ZIP+4 extension", "-{D4}", new DigitsToken("D4", "Four-digit extension", 4))
            })
        { }
    }
}
