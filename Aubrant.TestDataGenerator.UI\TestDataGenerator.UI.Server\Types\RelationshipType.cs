using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.Data.Enums;
namespace Aubrant.TestDataGenerator.UI.Server.Types
{
    public class RelationshipType : ObjectType<Relationship>
    {
        protected override void Configure(IObjectTypeDescriptor<Relationship> descriptor)
        {
            descriptor.Field(r => r.Id).Type<IdType>();
            descriptor.Field(r => r.SourceEntityId).Type<IntType>();
            descriptor.Field(r => r.SourceEntity).Type<StringType>();
            descriptor.Field(r => r.SourceField).Type<StringType>();
            descriptor.Field(r => r.TargetEntity).Type<StringType>();
            descriptor.Field(r => r.TargetField).Type<StringType>();
            descriptor.Field(r => r.Cardinality).Type<EnumType<Cardinality>>();
        }
    }
}
