using Aubrant.TestDataGenerator.SchemaProfiler.Enums;

namespace Aubrant.TestDataGenerator.SchemaProfiler.Logging
{
    /// <summary>
    /// Provides a null implementation of the ILogger interface, effectively disabling logging.
    /// </summary>
    public class NullLogger : ILogger
    {
        /// <summary>
        /// Gets the logging levels. Always returns LogLevel.None for NullLogger.
        /// </summary>
        public LogLevel VerboseLogLevels => LogLevel.None;
        /// <summary>
        /// Logs an informational message (does nothing).
        /// </summary>
        /// <param name="message">The message to log.</param>
        public void LogInfo(string message) { /* Do nothing */ }
        /// <summary>
        /// Logs a warning message (does nothing).
        /// </summary>
        /// <param name="message">The message to log.</param>
        public void LogWarning(string message) { /* Do nothing */ }
        /// <summary>
        /// Logs an error message (does nothing).
        /// </summary>
        /// <param name="message">The message to log.</param>
        /// <param name="ex">The exception to log (optional).</param>
        public void LogError(string message, System.Exception? ex = null) { /* Do nothing */ }
    }
}