"use client"

import { useState, useEffect, useCallback } from "react"
import { projectsApi } from "./projects"
import { dataSourcesApi } from "./datasources"
import type { Project, CreateProjectRequest, DataSource, TestConnectionRequest, TestConnectionResponse } from "./types"

// Custom hooks for API calls with loading states and error handling

export function useProjects(params?: {
  page?: number
  limit?: number
  search?: string
  owner?: string
  databaseType?: string
}) {
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  })

  const fetchProjects = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await projectsApi.getProjects(params)

      if (response.success) {
        setProjects(response.data)
        setPagination(response.pagination)
      } else {
        setError(response.error || "Failed to fetch projects")
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setLoading(false)
    }
  }, [params])

  useEffect(() => {
    fetchProjects()
  }, [fetchProjects])

  const createProject = async (projectData: CreateProjectRequest) => {
    try {
      const response = await projectsApi.createProject(projectData)
      if (response.success) {
        await fetchProjects() // Refresh the list
        return response
      }
      throw new Error(response.error || "Failed to create project")
    } catch (err) {
      throw err
    }
  }

  const deleteProject = async (id: string) => {
    try {
      const response = await projectsApi.deleteProject(id)
      if (response.success) {
        await fetchProjects() // Refresh the list
        return response
      }
      throw new Error(response.error || "Failed to delete project")
    } catch (err) {
      throw err
    }
  }

  const generateTestData = async (id: string) => {
    try {
      const response = await projectsApi.generateTestData(id)
      if (response.success) {
        await fetchProjects() // Refresh to update last execution date
        return response
      }
      throw new Error(response.error || "Failed to generate test data")
    } catch (err) {
      throw err
    }
  }

  return {
    projects,
    loading,
    error,
    pagination,
    refetch: fetchProjects,
    createProject,
    deleteProject,
    generateTestData,
  }
}

export function useTestConnection() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<TestConnectionResponse | null>(null)
  const [error, setError] = useState<string | null>(null)

  const testConnection = async (request: TestConnectionRequest) => {
    try {
      setLoading(true)
      setError(null)
      setResult(null)

      const response = await dataSourcesApi.testConnection(request)
      setResult(response)

      if (!response.success) {
        setError(response.message)
      }

      return response
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Connection test failed"
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }

  const reset = () => {
    setResult(null)
    setError(null)
    setLoading(false)
  }

  return {
    testConnection,
    loading,
    result,
    error,
    reset,
  }
}

export function useSchemaDiscovery() {
  const [loading, setLoading] = useState(false)
  const [dataSource, setDataSource] = useState<DataSource | null>(null)
  const [error, setError] = useState<string | null>(null)

  const discoverSchema = async (dataSourceId: string, connectionProperties: any) => {
    try {
      setLoading(true)
      setError(null)

      const response = await dataSourcesApi.discoverSchema({
        dataSourceId,
        connectionProperties,
      })

      if (response.success) {
        setDataSource(response.dataSource)
      } else {
        setError(response.error || "Schema discovery failed")
      }

      return response
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Schema discovery failed"
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }

  return {
    discoverSchema,
    loading,
    dataSource,
    error,
  }
}
