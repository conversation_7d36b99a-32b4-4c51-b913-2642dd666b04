﻿using System.Net;
using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Functions;

namespace Aubrant.TestDataGenerator.Generators.Functions{
    /// <summary>
    /// A generator function that produces random IPv4 addresses.
    /// It allows specifying octets to generate masked IP addresses.
    /// </summary>
    public class IPAddressFunction : BaseFunction
    {
        public override string Name => "IPv4";

        public override string Description => "Returns a random IP version 4 address optionally sending parameters to mask the output";
        public override DataType ReturnType => DataType.String;

        public IPAddressFunction() : base("Network")
        {
            Parameters.Add(new Parameter("Octet1", "The first octet of the IP address.", DataType.Integer));
            Parameters.Add(new Parameter("Octet2", "The second octet of the IP address.", DataType.Integer));
            Parameters.Add(new Parameter("Octet3", "The third octet of the IP address.", DataType.Integer));
            Parameters.Add(new Parameter("Octet4", "The fourth octet of the IP address.", DataType.Integer));
        }

        public override DataValue Generate(DataGeneratorContext context)
        {
            byte octet1 = (byte)(Parameters["Octet1"].Value.ToInt() ?? context.Random.Next(0, 255));
            byte octet2 = (byte)(Parameters["Octet2"].Value.ToInt() ?? context.Random.Next(0, 255));
            byte octet3 = (byte)(Parameters["Octet3"].Value.ToInt() ?? context.Random.Next(0, 255));
            byte octet4 = (byte)(Parameters["Octet4"].Value.ToInt() ?? context.Random.Next(0, 255));

            var address = new IPAddress([octet1, octet2, octet3, octet4]);
            return new DataValue(address.ToString());
        }
    }
}
