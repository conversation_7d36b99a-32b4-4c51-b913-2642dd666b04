# Intelligent Test Data Generator

## 💫 Overview

The **Intelligent Test Data Generator** is a modular, *AI-Enhanced* platform designed to automate the creation of `high-volume`, `realistic`, and `consistent` test data, across diverse data environments. It aims to address the growing complexity of modern systems that rely on heterogeneous data sources — including relational databases, NoSQL stores, and structured files — by enabling seamless, schema-aware, and rule-driven data generation.

### 🎯 Key Features

* **Multi-Project Support:** Allows users to configure, manage, and execute multiple projects simultaneously, enabling organized handling of diverse use cases with customizable schemas tailored to specific needs.
* **Schema-Aware Generation:** Automatically discovers and interprets schemas and relationships from connected data sources, inferring initial data generation rules based on data types, nullability, and foreign key references, with options for later modification via UI or AI-driven prompting.
* **AI-Powered Rule Engine:** Leverages AI to infer generation rules, request user input, and incorporate prompt-based customization.
* **Advanced Rule Customization UI:** Provides an intuitive interface for advanced users to manually modify generation rules, offering enhanced control and flexibility to support complex scenarios.
* **Referential Integrity:** Maintains consistency across related tables and data sources, supporting complex relationships and foreign key constraints.
* **Multi-Source Support:** Compatible with Relational Databases, NoSQL systems, and file-based data (e.g., XML, Excel).
* **Extensibility:** Supports built-in and runtime [functions](#functions), [patterns](#patterns), and [scripting](#scripts) (e.g., C#).
* **CI/CD Integration:** Easily integrates into DevOps pipelines, enabling shift-left testing and test-driven development.
* **Data Preview & Prompting:** Provides UI for data preview, refinement, and prompt-based adjustments.

### 📚 Core Layers

![Core Layers](CoreLayers.png)

### 📦 Core Modules

1. **Project Management** - Configure and create new projects.
2. **Data Source Setup** – Configure and connect to data sources.
3. **Schema Discovery** – Extracts schema and relationships from connected sources.
4. **Schema Mapping** – Enables users to edit the schema map, configure data generators for each field, preview generated data, and define custom functions and patterns for data generation.
5. **AI Rule Generator** – Generates intelligent data generation rules using AI and user prompts.
6. **Data Generators** – Generates data using built-in logic, dynamic AI, or user-defined scripting.
7. **User Interface (Data Preview)** – Visualizes generated data and provides statistical insights.
8. **Prompt Interface** – Allows users to refine data generation through natural language prompts.
9. **Function Manager** Enables users to define custom runtime functions, supporting parameters and two types — script-based or AI-driven — to extend built-in functionality for edge cases and specific scenarios.
10. **Pattern Manager** Allows users to create custom patterns for generating specialized data formats, extending built-in patterns (e.g., MAC addresses, phone numbers, IBAN codes, IP addresses) for specific use cases.

### ⇉ Data Flow Summary

1. **User Setup**: The user configures data sources via the UI.
2. **Schema Discovery**: The System extracts the schema and relationships.
3. **Rule Generation**: AI suggests rules; the user can refine via prompts or UI for better control.
4. **Data Generation**: The Engine generates data using rules and injects it into sources or other output types.
5. **Preview & Feedback**: User previews data and iterates via prompts or UI.
6. **Data publish**: ...

+++ Process Diagram:
![Data Flow](ProcessDiagram.svg)
+++

## 🛠️ Technical Specifications

### 🗂️ Project Management

#### 🏠 Home Page

The application enables users to create, manage, and oversee multiple projects efficiently. The starting page features a comprehensive, `filterable`, and `sortable` list of projects, allowing users to easily navigate and organize their work. It includes a `search` function by project name, along with `filters` for database types and owners to enhance usability. Users can create new projects, edit existing ones, or delete them as needed, with a prominent "New Project" button for quick access.

+++ Mockup
![Project List](Project_List.png)
+++

#### 📄 New Project

The user will be guided to configure a new Project, step by step, in a Wizard-like style UI, like this one:

+++ 🧙‍♂️ New Project Wizard

- ![Start new project wizard](Project_New1.png)
- ![Data Sources list](Project_New2.png)
- ![Add at least a Data Source](Project_New3.png)
- ![Configure Datasource](Project_New4.png)
- ![Test if can connect to Datasource and has permissions to grab Schema Objects, or at least to create, select, and insert tables](Project_New5.png)
- ![Go back to configure more Datasources and/or continue to next step](Project_New6.png)
- ![Schema Discovery allows to select objects that will be included in the project from the configured Datasources](Project_New7.png)
- ![User can filter and select or deselect the objects (Tables, Spreadsheets, Collections, etc) to include in the Schema Map](Project_New8.png)
- ![The user can see all available columns/fields and also relationships for the each DB Object](Project_New9.png)
- ![Relationship example](Project_New10.png)
+++

### 🔍 Schema Discovery

The **Schema Discovery** process is a critical internal function designed to analyze the structure of various data sources, such as relational databases, CSV or Excel files, and non-relational databases like MongoDB. This process identifies and presents to the user a comprehensive list of database objects, such as tables or collections, along with their respective fields or columns. For each object, it captures relevant metadata, including data types, nullability, field size, primary key status, default values, and foreign key constraints. Additionally, it determines the relationships between objects, such as one-to-one, one-to-many, many-to-one, or many-to-many associations.

Upon completion, the Schema Discovery process provides the user with a detailed catalog of discovered objects, enabling them to select which objects to include in their project. At this stage, the process does not analyze existing data within the objects or determine the data generation rules to be applied later, as these steps depend on the user’s selection of objects for the project.

> Notably, the same objects may be included in multiple projects for different purposes. For instance, in a `Human Resources` project, the `Employees` table might be included to dynamically generate employee data. Conversely, in a `Payroll` project, the same `Employees` table may be included solely as a reference for other objects, such as `Pay Slips` or `Vacation Records`, to maintain referential integrity. Thus, objects can serve as read-only references in some projects while being used for data generation in others, ensuring flexibility and referential consistency across use cases.

### 🔗 Schema Mapping

The `Schema Mapping` process is a pivotal stage in the configuration of a data generation project, where a comprehensive internal map is constructed to represent the schema of all selected objects across various data sources (`DataSources`). This map encapsulates critical information about the structure, relationships, and operational requirements of the objects, ensuring that generated data can be accurately published to each source according to its unique specifications. The key components of this process are outlined below:

* **Hierarchical and Contextual Organization**: The `Schema Mapping` process meticulously records the affiliation of each object to its respective data source, capturing details such as the schema to which a table belongs in relational databases, the name of a collection in non-relational databases, or the name of a spreadsheet in files like Excel. This organization is essential to ensure that generated data is published to the correct object.

* **Definition of Update and Generation Algorithms**: Each object is assigned a specific update or generation algorithm that dictates how its data will be managed within the project. These algorithms include:
  * **Read-Only**: For reference objects that require no data generation, preserving referential integrity.
  * **Full Insertion**: For new objects with no prior data, where records are generated and inserted from scratch.
  * **Truncate and Insert**: For objects where existing data is cleared before inserting newly generated records. 
  * **Append**: For objects with existing data, where new records are generated and added, adhering to integrity constraints such as primary keys (whether autogenerated or manually defined) to prevent conflicts with existing records.

* **Detailed Field Analysis**: The process conducts an in-depth examination of each column or field within the objects, collecting metadata about data types, names, constraints (e.g., nullability or default values), and interdependencies with other fields, either within the same object or across related objects. Leveraging artificial intelligence, a tailored data generator is preconfigured for each field, ensuring the production of coherent and valid data.

* **Interactive User Interface**: Upon completing the initial analysis, the system presents users with a dynamic interface to visualize and refine the generated Schema Map. Users can interact with the map manually through the user interface or utilize AI-driven prompts to make adjustments, providing unparalleled flexibility and control over the configuration process.

The outcome of the Schema Mapping process is a robust and adaptable model that serves as the foundation for data generation and publication, meticulously accommodating the unique characteristics of each data source and the specific requirements of the project.

+++ Mockups:

- ![Whole User Interface](SchemaMap1.gif)
- ![On the left the user will see the whole structure of the schema map and will be able to navigate through different datasources and objects like tables, and fields](SchemaMap2.gif)
- ![On the *middle/top* section the user will see an example of the generated data, for all columns of the selected table/object](SchemaMap3.gif)
- ![The right side allows the user to personalize manually the generation rules, and field settings](SchemaMap4.gif)
- ![The *middle/bottom* section will allow the user to interact with the Application using AI prompting](SchemaMap5.png)

+++

#### ▦ Tables Properties

> In Progress

#### ▥ Field Settings

> In Progress

##### 🔧 Field Properties

> In Progress

#### ⚙️ Generation Settings

In this section the user will be able to see and configure the Data Generator that will be used to generate the data for the column, as well, and configuring any particular parameter this Generator need.

A list of available Generators with more details can be found here:

##### ≡ Constant Generator
This generator assigns a fixed value to a field, ensuring compatibility with the field's data type. For instance, a status field might be set to 1 to denote "active," a text field to an empty string ("") or null, or an optional numeric field to 0 when unused. Similarly, a Date/Time field can be assigned a valid date or timestamp. The generator enforces data type compliance, and a tailored editor is provided for each field type, such as:

* *Checkboxes* for boolean fields.
* *Numeric input controls* for integer or decimal fields.
* *Textboxes* for string fields.
* *Date/time pickers* for date or timestamp fields.

This ensures intuitive configuration and accurate data generation aligned with the field's constraints.

+++ Examples:
<table>
  <tr>
    <th>Field Name</th>
    <th>Data Type</th>
    <th>Constant Value</th>
    <th>Editor Type</th>
  </tr>
  <tr>
    <td>Status</td>
    <td>Integer</td>
    <td>1</td>
    <td>Numeric Input</td>
  </tr>
  <tr>
    <td>IsActive</td>
    <td>Boolean</td>
    <td>True</td>
    <td>Checkbox</td>
  </tr>
  <tr>
    <td>CreatedDate</td>
    <td>Date</td>
    <td>2025-01-01</td>
    <td>Date Picker</td>
  </tr>
  <tr>
    <td>Country</td>
    <td>String</td>
    <td>USA</td>
    <td>Textbox</td>
  </tr>
</table>
+++

+++ Mockup:
![Constant](Generators_Constant_1.png)
+++

+++ Sample prompt:
- Constant Value: `3.14`.
- Use `null` for all records.
- Constant Value: `USA`.
+++

+++ Sample output:
![Sample Constant output](Generators_Constant_2.png)
+++

##### ⬆️ Auto Increment Generator
Allows to create numeric or date, datetime values that automatically increase their values. Useful for primary keys, or Creation Dates, for example.

**Parameters**:

- **Start**: Initial value of the Auto Increment generator, defaults to **1**, allows to specify which value to use as initial value, useful for inserting high values that don't interfere with existing data, just for testing and later cleanup.
- **Step**: Increment value, defaults to **1**.

> It will be the default generator for Identity columns, when creating the Schema Map, just to preview sample data, however the schema will be aware that Identity columns are readonly or autogenerated, and won't attempt to insert the value when publishing the data.

+++ Mockup:
![Auto Increment Generator](Generators_AutoIncrement_1.png)
+++
+++ Sample prompts:
- `Autoincremental values.`
- `Identity column.`
- `Consecutive number starting from 1000 and incremented by 100.`
+++
+++ Sample output:
![Sample AutoIncrement output](Generators_AutoIncrement_2.png)
+++

##### 🎲 Random Generator
The **Random Generator** produces *randomized values* for `Numeric`, `Date/Time`, and `Boolean` data types, ensuring compatibility with each field's constraints. It uses a uniform distribution to generate values within user-defined boundaries.

> Other types of advanced **Probability Distributions** will be available as well, as **Functions**, such as _Normal_, _Exponential_, _Binomial Distribution_, etc.

It will be configured via the following parameters:
  - **Min**: Specifies the minimum value for the generated data. Defaults to `0` for Numeric fields, `DateTime.MinValue` for Date/Time fields, or `False` for Boolean fields.
  - **Max**: Defines the maximum value for the generated data. This parameter is required for Numeric and Date/Time fields (e.g., `100` for an integer or `2025-12-31` for a date) and defaults to `True` for Boolean fields.
  A tailored editor, such as numeric inputs, date pickers, or checkboxes, facilitates parameter configuration, ensuring intuitive setup and precise data generation.

+++ Mockup:
![Random Generator](Generators_Random_1.png)
+++
+++ Sample prompts:
- For this field I will need a random value from `0` to `1`.
- A Random value from `X` to `Y`.
- Any value between `60` and `100`.
+++
+++ Sample output:
![Sample Random output](Generators_Random_2.png)
+++

##### 📜 Choice Generator
The Choice Generator enables selection from a predefined set of values, offering superior performance to AI-based generation when working with known value sets. It supports both simple value selection and weighted probability distributions.
It offers these key characteristics:

* **Predefined Value Selection**: Values are explicitly specified rather than dynamically generated.
* **Flexible Value Types**: Supports strings, numbers, and dates.
* **Duplication Allowed**: It will produce duplicate values (controlled via probabilities or random selection).
* **Probability Weighting**: Optional probability distribution control.
* **Range Support**: For numerical and date value ranges.
* **Performance Optimized**: Faster execution than AI generators as no runtime computation is needed.  AI prompt can be used to request the list of values, but it will be executed at design time, so at runtime it will only choose one of the generated values.

<table>
  <thead>
    <tr>
      <th>Feature</th>
      <th>Choice Generator</th>
      <th>AI-Based Generator</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Value Source</td>
      <td>Predefined list</td>
      <td>Dynamically generated</td>
    </tr>
    <tr>
      <td>Duplicates</td>
      <td>Expected (configurable)</td>
      <td>Typically avoided</td>
    </tr>
    <tr>
      <td>Performance</td>
      <td>Faster (O(1) selection)</td>
      <td>Slower (requires computation)</td>
    </tr>
    <tr>
      <td>Best For</td>
      <td>Known value sets that can be shared between records</td>
      <td>Creative/unpredictable output</td>
    </tr>
  </tbody>
</table>

+++ Mockups:

- ![Simple Options](Generators_Choice_1.png)
- ![Possible UI to edit simple options](Generators_Choice_2.png)
- ![Complex Options](Generators_Choice_3.png)
- ![UI mockup to edit complex options with Probability Distribution](Generators_Choice_4.png)
- ![Advance Options with Range and Probability Distribution](Generators_Choice_5.png)
- ![UI initially suggested to edit range type options](Generators_Choice_6.png)
+++
+++ Sample prompts:
- For this field I will need a predefined list of `IT related job positions`.
- The gender field will be 43% `M` => `Male`, 44% `F` => `Female`, and 13% `U` => `Undefined`.
+++
+++ Sample output:
![Choice Generator Output](Generators_Choice_7.png)
+++

##### ⇆ Relative Generator

**Overview**:
The Relative Generator efficiently creates values by applying deterministic offsets to reference fields in the same table. Designed for performance-critical operations, it executes as precompiled native code when handling simple offset logic.

**Key Features**:
- **Reference-Based Generation**: Derives values from other generated fields.
- **Optimized Performance**: Native execution for basic offset operations.
- **Temporal & Numerical Offsets**: Supports date arithmetic and numeric adjustments.
- **Null Handling**: Configurable null probability if the field allows nulls values.

+++ Parameter Specification:
**Required**:
```yaml
reference: FieldName # Target field for offset calculations
offset: 
  min: "+1y"         # Minimum offset (or fixed value)
  max: "+5y"         # Maximum offset
```

**Optional**:
```yaml
null_probability: 30 # 0-100%, default: 0
```
+++

+++ Example Configurations:
1. Basic Date Offset:
```yaml
- name: WarrantyExpiry
  type: DATE
  generation:
    type: relative
    reference: PurchaseDate
    offset: "+2y"  # Exactly 2 years later
```

2. Range-Based with Nulls:
```yaml
- name: TerminationDate
  type: DATE
  generation:
    type: relative
    reference: HireDate
    offset:
      min: "+1y"
      max: "+30y"
    null_probability: 90
```

3. Numeric Offset:
```yaml
- name: EstimatedPrice
  type: DECIMAL
  generation:
    type: relative
    reference: BasePrice
    offset: 
      min: "-10.5"
      max: "+25.0"
```

<table>
  <thead>
    <tr>
      <th>Scenario</th>
      <th>Relative Generator</th>
      <th>Script Generator</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Fixed date offset</td>
      <td>✅ Optimal</td>
      <td>⚠️ Overkill</td>
    </tr>
    <tr>
      <td>Price +10%</td>
      <td>❌ Cannot</td>
      <td>✅ Required</td>
    </tr>
    <tr>
      <td>Null probability</td>
      <td>✅ Native</td>
      <td>✅ Possible</td>
    </tr>
    <tr>
      <td>Multi-field logic</td>
      <td>❌ Cannot</td>
      <td>✅ Required</td>
    </tr>
  </tbody>
</table>
</style>

**Best Practices**:

**When to use Relative Generator:**
- For simple date/numeric offsets (3-5x faster than scripts).
- When you need basic null probability handling.
- For performance-critical field generation.

**When to switch to Script Generator:**
- When you need non-linear calculations (e.g., price +10%)
- For conditional generation logic.
- For complex multi-field dependencies.
+++

+++ Mockup:
- ![Proposed UI and Sample Output](Generators_Relative_1.png)
- ![Proposed UI](Generators_Relative_2.png)
+++

##### 📌 Reference Generator
**Overview**:
The **Reference Generator** creates relational integrity by linking fields to existing values in the same or different tables. It automatically handles dependency resolution through multi-pass generation sequencing.

**Key Features**:
* **Cross-Table References**: Maintains foreign key relationships.
* **Dependency Management**: Auto-detects generation order.
* **Circular Reference Support**: With nullable fallbacks.
* **Performance Optimization**: Batched reference resolution.

+++ Schema Configuration:
**Basic Reference**:
```yaml
fields:
  - name: ArtistId
    type: INTEGER
    generation:
      type: reference
      table: Artist       # Target table name
      field: ArtistId     # Target field name
      # Optional:
      null_probability: 5 # % chance of null (default 0)
```
**Circular Reference (Self-Referencing)**:
```yaml
fields:
  - name: ReportsTo
    type: INTEGER
    nullable: true
    generation:
      type: reference
      table: Employee     # Same table reference
      field: EmployeeId
      null_probability: 10 # CEO case
```
**Advanced Function-Based Reference**:
```yaml
fields:
  - name: Title
    type: STRING
    generation:
      type: function
      params:
        function: AlbumTitle
        arguments:
          - from: Artist    # Cross-table reference
            via: ArtistId   # Join field
            field: Name     # Source field
```
+++
+++ Generation Process:
1. Dependency Analysis:
   * Builds table dependency graph
   * Detects circular references
   * Orders tables by relation depth
2. Execution Phases:
![Execution Phases](Generators_Reference_1.svg)
1. Constraint Handling:
   * Applies null probability checks
   * Validates referencial integrity
+++
+++ Mockup:
![Reference Generator](Generators_Reference_2.gif)
+++

##### {} Script Generator
**Overview**:
The Script Generator enables dynamic data generation through code execution, leveraging AI-assisted script creation and Microsoft's C# Scripting Engine (`Microsoft.CodeAnalysis.CSharp.Scripting`). Supports both simple expressions and complex multi-line scripts.

**Key Features**:
* **AI-Powered Script Generation**: Convert natural language prompts to executable code
* **Dual Syntax Support**:
  * Simplified expressions (no semicolons/returns required)
  * Full multiline C# statements (with explicit returns)
* **Context Awareness**: Access to all record fields and built-in functions and patterns during generation
* **Extended Functionality**: Custom imports and extension methods

+++ Implementation Examples:
++++ 1. Email Generation:
  **AI Prompt**:
  > "Combine FirstName and LastName with random domain (gmail.com, hotmail.com, domain.com)"
  
  **Script**:
  ```c#
  string domain = new[] { "gmail.com", "hotmail.com", "domain.com" }.RandomElement();
  return $"{Fields.FirstName.ToLower()}.{Fields.LastName.ToLower()}@{domain}";
  ```
  
  <table><thead>
    <tr>
      <th>ID</th>
      <th>FirstName</th>
      <th>LastName</th>
      <th>Email</th>
    </tr></thead>
  <tbody>
    <tr>
      <td>1</td>
      <td>Carlos</td>
      <td>Garcia</td>
      <td><EMAIL></td>
    </tr>
  </tbody>
  </table>
++++
++++ 2. Hire Date Calculation:
**Script**:
```c#
int minAge = 18;
int maxAge = 48;
int hireAge = Random.Next(minAge, maxAge);
return Fields.BirthDate.AddYears(hireAge).AddDays(Random.Next(0, 365));
```
++++
++++ 3. Other examples:
```c#
// Generaring any built-in pattern and applying any transformation in script:
string usaPhone = Patterns.UsaPhoneNumber;
string fakePhone = Regex.Replace(usaPhone, @"^\+1\s(\d{3}-\d{3}-\d{4})$", $"{506} $1");
return fakeInternationalPhone;
----------------------------------------------------------------------
// Calling a function and applying any transformation to the result:
string city = Functions.City("USA", "TX");
return city.ToUpper();
----------------------------------------------------------------------
// Using simple expression and field in current table name Gender
Functions.FirstName(Fields.Gender, "es")
```
++++
+++

+++ Best Practices

**When to Use Scripts:**
* ✅ Complex field interdependencies
* ✅ Custom data transformations
* ✅ Conditional logic requirements

**When to Avoid:**
* ❌ Simple static values (use Constant Generator)
* ❌ Basic randomization (use Random Generator)

**AI Prompting Tips:**

* Specify exact field references:
"Use ProductCode + '-' + WarehouseId"

* Clarify formatting needs:
"Phone numbers should use (XXX) XXX-XXXX format"
+++

+++ Mockup:
![Script Generator UI](Generators_Script_1.png)
+++

##### 🧩 Pattern Generator

The Pattern Generator creates formatted data values with strict structural requirements. It supports both built-in optimized patterns and customizable runtime patterns.

These are especially useful for fields such as:

<table>
<tbody>
  <tr>
    <th>Unique Identifiers</th>
    <th>MAC Addresses</th>
    <td>IP4 / IP6</td>
    <td>IBAN</td>
  </tr>
  <tr>
    <td>Social Security Numbers</td>
    <td>Card Numbers</td>
    <td>Postal Codes</td>
    <td>License Plate Numbers</td>
  </tr>
  <tr>
    <td>Passwords</td>
    <td>Coordinates</td>
    <td>Hexadecimal Color Codes</td>
    <td>ISBN</td>
  </tr>
  <tr>
    <td>Phone Numbers</td>
    <td>Time Values</td>
    <td>Application Version Numbers</td>
    <td>Car Vin</td>
  </tr>
</tbody>
</table>

+++ Pattern Types:
Patterns, like functions, are categorized into two main types:

1. **Built-In Patterns**:
* **Performance-optimized**: Precompiled for speed.
* **Customizable**: Can be designed to enforce valid values (e.g., valid dates).

To support AI-driven generation, each pattern should encapsulate metadata in a Pattern class, similar to runtime patterns.

2. **Runtime Patterns**:
* Defined at runtime by users or AI.
* Extend or personalize built-in patterns.
* Stored in project configuration.
+++

+++ Pattern Structure:
Each pattern consists of four main components:

<table><thead>
  <tr>
    <th>Component</th>
    <th>Description</th>
  </tr></thead>
<tbody>
  <tr>
    <td>Name</td>
    <td>Identifier for referencing the pattern</td>
  </tr>
  <tr>
    <td>Description</td>
    <td>Human-readable explanation</td>
  </tr>
  <tr>
    <td>Pattern Expression</td>
    <td>The format string using tokens</td>
  </tr>
  <tr>
    <td>Tokens</td>
    <td>Building blocks of the pattern</td>
  </tr>
</tbody>
</table>

++++ Token Types:
<table><thead>
  <tr>
    <th>Token Type</th>
    <th>Description</th>
  </tr></thead>
<tbody>
  <tr>
    <td>hex</td>
    <td>Random hexadecimal values</td>
  </tr>
  <tr>
    <td>digits</td>
    <td>Random positive integers</td>
  </tr>
  <tr>
    <td>letters</td>
    <td>Alphabetical characters</td>
  </tr>
  <tr>
    <td>alphanum</td>
    <td>Alphanumeric characters</td>
  </tr>
  <tr>
    <td>choice</td>
    <td>Predefined list of valid values</td>
  </tr>
  <tr>
    <td>optional</td>
    <td>Token that may or may not appear, must include sub-patterns</td>
  </tr>
</tbody>
</table>
++++
+++

+++ Examples:
++++ Sample Pattern Definition for Unique Identifier:
![Unique Identifier](Generators_Pattern_7.png)
++++

++++ UI Examples:
- ![Pattern usage in Field](Generators_Pattern_1.png)
- ![Pattern Schema Example](Generators_Pattern_2.png)
- ![Pattern Editor](Generators_Pattern_4.png)
- ![Pattern Editor - Ranges](Generators_Pattern_3.png)
- ![Pattern Editor - Choice Token](Generators_Pattern_5.png)
- ![Other examples](Generators_Pattern_6.png)
++++

++++ Sample Pattern Definitions:
```yaml
patterns:
  # Pattern are a powerful type of generators the can create data based on components or tokens of specific types, useful for example for GUID, Mac Address, IP4, IP6, IBAN, Card Numbers, etc
  # Similar to functions Patterns can be defined in code and built-in for better performance, but the engine will support to create runtime patterns as well.
  # Patterns can be accesed from scripts
  - name: UUID
    description: Standard UUID (version 4) format
    pattern: "{H8}-{H4}-{H4}-{H4}-{H12}"
    tokens:
      H8:
        type: hex
        length: 8
        case: lower
      H4:
        type: hex
        length: 4
        case: lower
      H12:
        type: hex
  - name: PhoneNumber
    description: Standard international phone number with country and area code
    pattern: "+{C} ({A}) {P}-{L}"
    tokens:
      C:
        type: choice
        options: ["1", "44", "506", "91"]
      A:
        type: digits
        length: 3
      P:
        type: digits
        length: 3
      L:
        type: digits
        length: 4
  - name: Date # Date pattern will be more likely created as a Built-In precompiled pattern, however I added this example here to demonstrate the power of these patterns
    description: Valid date in "MMM/dd/yyyy" format
    pattern: "{MMM}/{DD}/{YYYY}"
    tokens:
      MMM:
        type: choice
        options:
          [
            "Jan",
            "Feb",
            "Mar",
            "Apr",
            "May",
            "Jun",
            "Jul",
            "Aug",
            "Sep",
            "Oct",
            "Nov",
            "Dec",
          ]
      DD:
        type: digits
        length: 2
        range: [1, 31]
      YYYY:
        type: digits
        length: 4
        range: [1950, 2030]
  - name: IBAN
    description: International Bank Account Number (generic format)
    pattern: "{CC}{CK}{BODY}"
    tokens:
      CC:
        type: letters
        case: upper
        length: 2
      CK:
        type: digits
        length: 2
      BODY:
        type: alphanum
        length: 16 # You can adjust this based on country-specific rules
  - name: USPostalCode
    description: U.S. ZIP code in 5-digit or ZIP+4 format
    pattern: "{ZIP5}{PLUS4}"
    tokens:
      ZIP5:
        type: digits
        length: 5
      PLUS4:
        type: optional
        pattern: "-{D4}"
      D4:
        type: digits
        length: 4
```
++++
+++

##### ƒ(x) Function Generator

H

### ℹ️ Additional Details

+++ References:
1. [Mokaroo.](https://mockaroo.com/)
2. [Fabricate](https://fabricate.tonic.ai/)
3. [Synthesized](https://www.synthesized.io/)
+++
