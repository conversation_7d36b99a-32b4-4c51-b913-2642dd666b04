using Aubrant.TestDataGenerator.Core.Enums;
using System.Text.Json.Serialization;

namespace Aubrant.TestDataGenerator.Core.Patterns
{
    public class DigitsToken : Token
    {
        public int Length { get; }
        public List<TokenRange> Ranges { get; set; }

        [JsonConstructor]
        public DigitsToken(string name, int length, params TokenRange[] ranges) : this(name, string.Empty, length, ranges)
        {
        }

        public DigitsToken(string name, string description, int length, params TokenRange[] ranges) : base(name, description)
        {
            if (length <= 0) throw new ArgumentException("Length must be positive.");
            Length = length;

            if (ranges?.Any() == true)
            {
                Ranges = ranges.Select(r => new TokenRange(r.Min, r.Max)).ToList();
            }
            else
            {
                Ranges = new List<TokenRange>();
            }
        }

        public override DataValue Generate(DataGeneratorContext context)
        {
            if (Ranges?.Any() == true)
            {
                int rangeCount = Ranges.Count;
                var range = Ranges[context.Random.Next(0, rangeCount - 1)];

                int min = range.Min;
                int max = range.Max;
                int value = context.Random.Next(min, max + 1);

                return new DataValue(DataType.String, value.ToString($"D{Length}"));
            }

            var digits = new char[Length];

            for (int i = 0; i < Length; i++)
            {
                digits[i] = (char)('0' + context.Random.Next(0, 9));
            }

            return new DataValue(DataType.String, new string(digits));
        }
    }
}