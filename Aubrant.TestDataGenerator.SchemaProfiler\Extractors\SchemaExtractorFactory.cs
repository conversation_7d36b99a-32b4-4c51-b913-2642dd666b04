using Aubrant.TestDataGenerator.Data.Enums;
using Aubrant.TestDataGenerator.SchemaProfiler.Common;
using Aubrant.TestDataGenerator.SchemaProfiler.Logging;

namespace Aubrant.TestDataGenerator.SchemaProfiler.Extractors
{
    /// <summary>
    /// Factory for creating specific IDataSourceSchemaExtractor implementations based on the data source provider.
    /// </summary>
    public class SchemaExtractorFactory
    {
        private readonly ILogger _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="SchemaExtractorFactory"/> class.
        /// </summary>
        /// <param name="logger">Optional. An ILogger instance for logging. If null, a NullLogger will be used.</param>
        public SchemaExtractorFactory(ILogger? logger = null)
        {
            _logger = logger ?? new NullLogger();
        }

        /// <summary>
        /// Creates an instance of <see cref="IDataSourceSchemaExtractor"/> based on the specified provider.
        /// </summary>
        /// <param name="provider">The data source provider type.</param>
        /// <param name="options">The schema extraction options.</param>
        /// <returns>An implementation of <see cref="IDataSourceSchemaExtractor"/>.</returns>
        public IDataSourceSchemaExtractor CreateExtractor(DataSourceProvider provider, SchemaExtractionOptions? options)
        {
            options = options ?? new SchemaExtractionOptions();
            _logger.LogInfo($"Creating extractor for {provider} with SampleSize: {options?.SampleSize.ToString() ?? "Default"}");
            return provider switch
            {
                DataSourceProvider.CSV => new CsvSchemaExtractor(options, _logger),
                DataSourceProvider.SQLite => new SQLiteSchemaExtractor(options, _logger),
                DataSourceProvider.SQLServer => new SqlServerSchemaExtractor(options, _logger),
                DataSourceProvider.MongoDB => new MongoDbSchemaExtractor(options, _logger),
                DataSourceProvider.Excel => new ExcelSchemaExtractor(options, _logger),
                DataSourceProvider.Neo4j => new Neo4jSchemaExtractor(options, _logger),
                _ => throw new ArgumentOutOfRangeException(nameof(provider), $"Unsupported data source provider: {provider}")
            };
        }
    }
}