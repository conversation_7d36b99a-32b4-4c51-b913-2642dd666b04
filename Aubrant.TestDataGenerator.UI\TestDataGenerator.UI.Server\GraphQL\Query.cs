using Aubrant.TestDataGenerator.Data;
using DataSourceTypeEnum = Aubrant.TestDataGenerator.Data.Enums.DataSourceType;

namespace Aubrant.TestDataGenerator.UI.Server.GraphQL
{
    public class Query
    {
        [UseProjection]
        [UseFiltering]
        [UseSorting]
        public IQueryable<Project> GetProjects(DatabaseContext context, string? nameContains, DataSourceTypeEnum? dataSourceType, string? author)
        {
            var query = context.Projects.AsQueryable();

            if (!string.IsNullOrEmpty(nameContains))
            {
                query = query.Where(p => p.Name.Contains(nameContains));
            }

            if (dataSourceType.HasValue)
            {
                query = query.Where(p => p.DataSources.Any(d => d.Type == dataSourceType.Value));
            }

            if (!string.IsNullOrEmpty(author))
            {
                query = query.Where(p => p.Author == author);
            }

            return query;
        }

        [UseFirstOrDefault]
        [UseProjection]
        public IQueryable<Project> GetProject(DatabaseContext context, int id)
        {
            return context.Projects.Where(p => p.Id == id);
        }

        [UseFirstOrDefault]
        [UseProjection]
        public IQueryable<DataSource> GetDataSource(DatabaseContext context, int id)
        {
            return context.DataSources.Where(d => d.Id == id);
        }

        [UseProjection]
        [UseFiltering]
        [UseSorting]
        public IQueryable<DataSource> GetDataSources(DatabaseContext context, int projectId)
        {
            return context.DataSources.Where(d => d.ProjectId == projectId);
        }

        [UseFirstOrDefault]
        [UseProjection]
        public IQueryable<Entity> GetEntity(DatabaseContext context, int id)
        {
            return context.Entities.Where(e => e.Id == id);
        }

        [UseProjection]
        [UseFiltering]
        [UseSorting]
        public IQueryable<Entity> GetEntities(DatabaseContext context, int dataSourceId)
        {
            return context.Entities.Where(e => e.DataSourceId == dataSourceId);
        }

        [UseFirstOrDefault]
        [UseProjection]
        public IQueryable<Field> GetField(DatabaseContext context, int id)
        {
            return context.Fields.Where(f => f.Id == id);
        }

        [UseProjection]
        [UseFiltering]
        [UseSorting]
        public IQueryable<Field> GetFields(DatabaseContext context, int entityId)
        {
            return context.Fields.Where(f => f.EntityId == entityId);
        }
    }
}
