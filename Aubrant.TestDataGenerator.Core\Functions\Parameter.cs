using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core;

namespace Aubrant.TestDataGenerator.Core.Functions
{
    public class Parameter
    {
        /// <summary>
        /// Parameter name.
        /// </summary>
        public string Name { get; }

        /// <summary>
        /// Parameter description.
        /// </summary>
        public string Description { get; }

        /// <summary>
        /// Type of the parameter.
        /// </summary>
        public DataType Type { get; }

        /// <summary>
        /// Constant or current value of the parameter.
        /// </summary>
        public DataValue Value { get; set; }

        /// <summary>
        /// Field Nme for the parameter
        /// </summary>
        public string? Field { get; set; }

        public Parameter(string name, string description, DataType type, DataValue? defaultValue = null)
        {
            Name = name ?? throw new ArgumentNullException(nameof(name));
            Description = description ?? string.Empty;
            Type = type;
            Value = defaultValue ?? new DataValue(Type, null);
        }

        public Parameter(string name, string description, DataType type, DataValue value, DataValue? defaultValue = null) : this(name, description, type, defaultValue)
        {
            Value = value ?? throw new ArgumentNullException(nameof(value));
        }
    }
}