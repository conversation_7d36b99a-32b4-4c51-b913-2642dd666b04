﻿using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Patterns;

namespace Aubrant.TestDataGenerator.Generators.Patterns
{
    public class IBANPattern : BasePattern
    {
        public IBANPattern() : base(
            name: "IBAN",
            description: "International Bank Account Number (generic format)",
            pattern: "{CC}{CK}{BODY}",
            tokens: new List<Token>
            {
            new LettersToken("CC", "Country code", 2, CaseOption.Upper),
            new DigitsToken("CK", "Check digits", 2),
            new AlphaNumToken("BODY", "Account number", 16)
            })
        { }
    }
}
