using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Functions;
using Bogus;

namespace Aubrant.TestDataGenerator.Generators.Functions{
    /// <summary>
    /// A generator function that produces realistic full names based on specified locales and a format.
    /// It utilizes the Bogus library for name generation.
    /// </summary>
    public class FullNameFunction : BaseFunction
    {
        public override string Name => "FullName";
        public override string Description => "Generates a realistic full name based on `locale`, optional `gender`, and a `format` string.";
        public override DataType ReturnType => DataType.String;

        /// <summary>
        /// Initializes a new instance of the FullNameFunctionGenerator class.
        /// Defines 'locale', optional 'gender', and 'format' parameters.
        /// </summary>
        public FullNameFunction() : base("Person")
        {
            Parameters.Add(new Parameter("locale", "The output locale.", DataType.String, new DataValue("en"))); // Default locale is English
            Parameters.Add(new Parameter("gender", "The gender of the person.", DataType.String, null)); // Optional gender parameter
            Parameters.Add(new Parameter("format", "The format of the full name.", DataType.String, new DataValue("{firstName} {lastName}"))); // Default format
        }

        /// <summary>
        /// Generates a random full name using the Bogus library, considering the specified locale, gender, and format.
        /// </summary>
        /// <returns>A DataValue containing the generated full name as a string.</returns>
        /// <exception cref="InvalidOperationException">Thrown if the locale parameter is missing or invalid.</exception>
        /// <exception cref="ArgumentException">Thrown if an invalid gender string is provided.</exception>
        public override DataValue Generate(DataGeneratorContext context)
        {
            string locale = Parameters["locale"].Value?.ToString() ??
                            throw new InvalidOperationException("Locale parameter is missing or invalid.");
            string? genderString = Parameters["gender"].Value?.ToString();
            string format = Parameters["format"].Value?.ToString() ??
                            throw new InvalidOperationException("Format parameter is missing or invalid.");

            Bogus.DataSets.Name.Gender? gender = null;
            if (!string.IsNullOrWhiteSpace(genderString))
            {
                if (genderString.Equals("male", StringComparison.OrdinalIgnoreCase) || (genderString.Equals("m", StringComparison.OrdinalIgnoreCase)))
                {
                    gender = Bogus.DataSets.Name.Gender.Male;
                }
                else if (genderString.Equals("female", StringComparison.OrdinalIgnoreCase) || (genderString.Equals("f", StringComparison.OrdinalIgnoreCase)))
                {
                    gender = Bogus.DataSets.Name.Gender.Female;
                }
                else
                {
                    throw new ArgumentException($"Invalid gender specified: '{genderString}'. Expected 'Male', 'M', 'Female', 'F', or empty/null.");
                }
            }

            try
            {
                var faker = new Faker(locale);
                string firstName;
                string lastName;

                if (gender.HasValue)
                {
                    firstName = faker.Name.FirstName(gender.Value);
                    lastName = faker.Name.LastName(gender.Value);
                }
                else
                {
                    firstName = faker.Name.FirstName();
                    lastName = faker.Name.LastName();
                }

                string fullName = format
                    .Replace("{firstName}", firstName)
                    .Replace("{lastName}", lastName);

                return new DataValue(DataType.String, fullName);
            }
            catch (FormatException ex)
            {
                throw new InvalidOperationException($"Invalid locale '{locale}' provided for FullNameFunctionGenerator.", ex);
            }
        }
    }
}