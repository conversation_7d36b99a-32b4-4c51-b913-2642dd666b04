﻿using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Interfaces;
using System.Text.Json;

namespace Aubrant.TestDataGenerator.Generators
{
    /// <summary>
    /// Implements the IGenerator interface to randomly select a DataValue
    /// from a predefined set of options, which can include simple values,
    /// labeled values with probabilities, or ranges with probabilities.
    /// </summary>
    public class ChoiceGenerator : IRuntimeGenerator
    {
        public string Name => "Choice";

        public string Description => "Selects a value from a predefined list of options, which can include direct values, numerical or date ranges, and supports probabilistic distribution. Use this generator when a field's data must come from a known, finite set of possibilities, or when specific values need to appear with controlled frequencies.";

        public DataType ReturnType => DataType.Any;

        /// <summary>
        /// List of Options
        /// </summary>
        [System.ComponentModel.Description("List of Options")]
        public List<ChoiceOption> Options { get; set; }

        /// <summary>
        /// Parameterless constructor required for Semantic Analyzer
        /// </summary>
        public ChoiceGenerator()
        {
            Options = new List<ChoiceOption>();
        }

        /// <summary>
        /// Initializes a new instance of the ChoiceGenerator class with the given configurations.
        /// </summary>
        /// <param name="options">A list of OptionConfiguration objects defining the choices.</param>
        /// <exception cref="ArgumentNullException">Thrown if options is null.</exception>
        /// <exception cref="ArgumentException">Thrown if options list is empty or probabilities are invalid.</exception>
        public ChoiceGenerator(List<ChoiceOption> options) : this()
        {
            InitializeOptions(options);
        }

        private void InitializeOptions(List<ChoiceOption> options)
        {
            if (!options?.Any() != false)
            {
                throw new ArgumentException("Options list cannot be empty.", nameof(options));
            }

            Options = options;

            bool hasProbabilities = options.Any(o => o.Probability > 0);
            double totalProbabilityWeight = 0;

            if (hasProbabilities)
            {
                totalProbabilityWeight = options.Sum(o => o.Probability);
                if (totalProbabilityWeight <= 0)
                {
                    throw new ArgumentException("Total probability must be greater than 0 if probabilities are specified.", nameof(options));
                }
            }
            else
            {
                totalProbabilityWeight = options.Count;
            }

            double probability = 0;
            foreach (var option in options)
            {
                double relativeProbability;

                if (hasProbabilities)
                {
                    relativeProbability = option.Probability / totalProbabilityWeight;
                }
                else
                {
                    relativeProbability = 1.0 / totalProbabilityWeight;
                }

                probability += relativeProbability;

                // Updated to use RangeConfiguration
                if (option.Range != null)
                {
                    DataValue? rangeMinValue = option.Range?.Min;
                    DataValue? rangeMaxValue = option.Range?.Max;

                    if (rangeMinValue == null || rangeMaxValue == null)
                    {
                        throw new ArgumentException($"Invalid range format or unsupported value in range: [{option.Range?.Min.Value}, {option.Range?.Max.Value}].", nameof(options));
                    }

                    // Validate that ranges are only supported for Date, Integer, and Decimal DataValue types
                    if (rangeMinValue.Type != DataType.DateTime &&
                        rangeMinValue.Type != DataType.Integer &&
                        rangeMinValue.Type != DataType.Decimal)
                    {
                        throw new ArgumentException($"Ranges are only supported for Date, Integer, and Decimal data types. Found type: {rangeMinValue.Type} for range start value: {option.Range?.Min}.", nameof(options));
                    }

                    if (rangeMaxValue.Type != DataType.DateTime &&
                        rangeMaxValue.Type != DataType.Integer &&
                        rangeMaxValue.Type != DataType.Decimal)
                    {
                        throw new ArgumentException($"Ranges are only supported for Date, Integer, and Decimal data types. Found type: {rangeMaxValue.Type} for range end value: {option.Range?.Max}.", nameof(options));
                    }

                    // Also ensure both ends of the range are of compatible types
                    if (rangeMinValue.Type != rangeMaxValue.Type)
                    {
                        throw new ArgumentException($"Range start and end values must be of the same type. Found {rangeMinValue.Type} and {rangeMaxValue.Type} for range: [{option.Range?.Min}, {option.Range?.Max}].", nameof(options));
                    }

                    Options.Add(new ChoiceOption((rangeMinValue, rangeMaxValue), probability));
                }
                else if (option.Value != null)
                {
                    DataValue? directDataValue = option.Value;

                    if (directDataValue == null)
                    {
                        throw new ArgumentException($"Unsupported value type for direct option: {option.Value?.GetType().Name} with value: {option.Value}.", nameof(options));
                    }

                    Options.Add(new ChoiceOption(directDataValue, probability));
                }
                else
                {
                    throw new ArgumentException("Each option must specify either a 'value' or a 'range'.", nameof(options));
                }
            }
        }

        /// <summary>
        /// Initializes a new instance of the ChoiceGenerator class with a params array of options.
        /// This provides a convenient way to pass options directly without explicitly creating a List.
        /// </summary>
        /// <param name="optionsArray">A params array of OptionConfiguration objects.</param>
        public ChoiceGenerator(params ChoiceOption[] options)
            : this(options?.ToList()!) // Convert params array to List and call the primary constructor
        { }

        /// <summary>
        /// Generates a DataValue based on the configured options and their probabilities.
        /// For range options, it generates a random value within the specified range.
        /// </summary>
        /// <returns>A DataValue containing the randomly chosen or generated item.</returns>
        public DataValue Generate(DataGeneratorContext context)
        {
            double roll = context.Random.NextDouble();

            foreach (var option in Options)
            {
                if (roll < option.Probability)
                {
                    if (option.Range?.Min != null && option.Range?.Max != null)
                    {
                        switch (option.Range.Min.Type)
                        {
                            case DataType.DateTime:
                                DateTime startDate = option.Range.Min.ToDate()!.Value; // Null-forgiving as validated
                                DateTime endDate = option.Range.Max.ToDate()!.Value;     // Null-forgiving as validated

                                if (startDate > endDate)
                                {
                                    return new DataValue(DataType.String, $"Internal Error: Invalid Date Range after validation: {startDate} to {endDate}");
                                }

                                long ticksDiff = endDate.Ticks - startDate.Ticks;
                                long randomTicks = (long)(context.Random.NextDouble() * ticksDiff);
                                DateTime generatedDate = startDate.AddTicks(randomTicks);
                                return new DataValue(generatedDate);

                            case DataType.Integer:
                                int startInt = option.Range.Min.ToInt()!.Value; // Null-forgiving as validated
                                int endInt = option.Range.Max.ToInt()!.Value;     // Null-forgiving as validated

                                if (startInt > endInt)
                                {
                                    return new DataValue(DataType.String, $"Internal Error: Invalid Integer Range after validation: {startInt} to {endInt}");
                                }

                                // Random.Next(minValue, maxValue) returns a random integer within a specified range.
                                // The lower bound is inclusive, and the upper bound is exclusive.
                                // So to include endInt, we need to add 1 to it.
                                int generatedInt = context.Random.Next(startInt, endInt + 1);
                                return new DataValue(generatedInt);

                            case DataType.Decimal:
                                decimal startDec = option.Range.Min.ToDecimal()!.Value; // Null-forgiving as validated
                                decimal endDec = option.Range.Max.ToDecimal()!.Value;     // Null-forgiving as validated

                                if (startDec > endDec)
                                {
                                    return new DataValue(DataType.String, $"Internal Error: Invalid Decimal Range after validation: {startDec} to {endDec}");
                                }

                                decimal generatedDec = startDec + (decimal)(context.Random.NextDouble() * (double)(endDec - startDec));
                                return new DataValue(generatedDec);

                            default:
                                // This case should ideally not be reached due to constructor validation.
                                return new DataValue(DataType.String, $"Internal Error: Unexpected range type after validation: {option.Range.Min.Type}");
                        }
                    }
                    else
                    {
                        // Direct value option, already stored as DataValue
                        return option.Value!; // Null-forgiving as constructor ensures it's not null for direct values
                    }
                }
            }

            return new DataValue(DataType.String, "Error: No option could be selected based on probabilities.");
        }

        public void Parse(DataType dataType, IDictionary<string, object> settings)
        {
            if (settings.TryGetValue("Options", out object? optionsObject))
            {
                if (optionsObject is JsonElement optionsElement && optionsElement.ValueKind == JsonValueKind.Array)
                {
                    List<ChoiceOption> parsedOptions = new List<ChoiceOption>();
                    foreach (var optionElement in optionsElement.EnumerateArray())
                    {
                        DataValue? value = null;
                        DataRange? range = null;
                        double probability = 0;
                        string? label = null;

                        if (optionElement.TryGetProperty("Value", out var valueElement))
                        {
                            value = new DataValue(dataType, valueElement);
                        }
                        else if (optionElement.TryGetProperty("Range", out var rangeElement) && rangeElement.ValueKind == JsonValueKind.Object)
                        {
                            DataValue? min = null;
                            DataValue? max = null;
                            //*-*
                            if (rangeElement.TryGetProperty("Min", out var minElement))
                            {
                                min = new DataValue(dataType, minElement);
                            }
                            if (rangeElement.TryGetProperty("Max", out var maxElement))
                            {
                                max = new DataValue(dataType, maxElement);
                            }
                            range = new DataRange(min, max);
                        }

                        if (optionElement.TryGetProperty("Probability", out var probabilityElement) && probabilityElement.ValueKind == JsonValueKind.Number)
                        {
                            probability = probabilityElement.GetDouble();
                        }

                        if (optionElement.TryGetProperty("Label", out var labelElement) && labelElement.ValueKind == JsonValueKind.String)
                        {
                            label = labelElement.GetString();
                        }

                        // Create ChoiceOption based on whether it's a value or a range
                        if (value != null)
                        {
                            parsedOptions.Add(new ChoiceOption(value, probability, label));
                        }
                        else if (range != null)
                        {
                            parsedOptions.Add(new ChoiceOption((range.Min, range.Max), probability, label));
                        }
                        else
                        {
                            throw new JsonException("Each option must specify either a 'Value' or a 'Range'.");
                        }
                    }

                    Options.AddRange(parsedOptions);
                }
            }
        }
    }
}
