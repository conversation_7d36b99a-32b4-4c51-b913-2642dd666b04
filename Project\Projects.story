Feature: Projects Page

As a system user,
I want to view and manage projects
So that I can find, organize, and work with relevant projects efficiently.

Scenario: Empty State
  Given I am on the projects page
  When there are no projects
  Then I see an empty state message and a "New Project" button

Scenario: Loading State
  Given I navigate to the projects page
  When the projects are loading
  Then I see a loading indicator

Sc<PERSON><PERSON>: Viewing Projects
  Given I have access to projects
  When I open the Projects page
  Then I see a list containing these columns:
    | Name                  |
    | Data Sources          |
    | Owner                 |
    | Creation Date         |
    | Last Execution Date   |
  And I see action buttons for each project

Scenario: Sorting Projects
  Given I am on the projects page with multiple projects
  When I click the "Name" column header
  Then projects are listed in alphabetical order (A → Z)
  When I click the "Name" header again
  Then projects are listed in reverse alphabetical order (Z → A)

Scenario: Paging Through Projects
  Given I am on the projects page with more projects than the page limit
  When I click the "Next" page button
  Then I see the next set of projects in the grid

Scenario: Filtering Projects by Name
  Given I am on the projects page
  When I enter "ProjectX" in the name filter
  Then I see only projects with "ProjectX" in their name

Scenario: Filtering Projects by Datasource Type
  Given I am on the projects page
  When I select "SQL" in the datasource type filter
  Then I see only projects with "SQL" in their DataSources

Scenario: Filtering Projects by Owner
  Given I am on the projects page
  When I select "John Doe" in the owner filter
  Then I see only projects owned by "<PERSON>e"

Scenario: Starting a Project
  When I click "Run" on a project named "Smart Invoice Matching"
  Then the project begins processing
  And I see a status indicator next to the project

Scenario: Editing a Project
  Given I am on the projects page
  When I click the "Edit" action on a project  
  Then I am redirected to the project edit page

Scenario: Deleting a Project
  Given I am on the projects page
  When I click the "Delete" action on a project
  And I confirm the deletion
  Then the project is removed from the list

Scenario: Opening the New Project Wizard
  Given I am on the projects page
  When I click the "New Project" button
  Then I am redirected to the new project wizard page
