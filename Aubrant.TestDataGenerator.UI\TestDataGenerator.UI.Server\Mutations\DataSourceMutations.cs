using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.Data.Enums;
using Aubrant.TestDataGenerator.UI.Server.GraphQL;

namespace Aubrant.TestDataGenerator.UI.Server.Mutations
{
    [ExtendObjectType(typeof(Mutation))]
    public class DataSourceMutations
    {
        public async Task<DataSource> CreateDataSource(int projectId, string name, string? description, DataSourceType type, DataSourceProvider provider, string connectionString, [Service] DatabaseContext context)
        {
            var dataSource = new DataSource
            {
                ProjectId = projectId,
                Name = name,
                Description = description,
                Type = type,
                Provider = provider,
                ConnectionString = connectionString
            };

            context.DataSources.Add(dataSource);
            await context.SaveChangesAsync();

            return dataSource;
        }

        public async Task<DataSource> UpdateDataSource(int id, string? name, string? description, DataSourceType? type, DataSourceProvider? provider, string? connectionString, [Service] DatabaseContext context)
        {
            var dataSource = await context.DataSources.FindAsync(id);

            if (dataSource == null)
            {
                throw new System.Exception("DataSource not found.");
            }

            if (name != null)
            {
                dataSource.Name = name;
            }

            if (description != null)
            {
                dataSource.Description = description;
            }

            if (type.HasValue)
            {
                dataSource.Type = type.Value;
            }

            if (provider.HasValue)
            {
                dataSource.Provider = provider.Value;
            }

            if (connectionString != null)
            {
                dataSource.ConnectionString = connectionString;
            }

            await context.SaveChangesAsync();

            return dataSource;
        }

        public async Task<bool> DeleteDataSource(int id, [Service] DatabaseContext context)
        {
            var dataSource = await context.DataSources.FindAsync(id);

            if (dataSource == null)
            {
                return false;
            }

            context.DataSources.Remove(dataSource);
            await context.SaveChangesAsync();

            return true;
        }
    }
}
