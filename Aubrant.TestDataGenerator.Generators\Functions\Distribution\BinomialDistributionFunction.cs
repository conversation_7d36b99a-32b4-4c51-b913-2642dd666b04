using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Functions;

namespace Aubrant.TestDataGenerator.Generators.Functions{
    /// <summary>
    /// A generator function that produces random integer values following a Binomial distribution.
    /// </summary>
    public class BinomialDistributionFunction : BaseFunction
    {
        public override string Name => "Binomial Distribution";
        public override string Description => "Generates random integers from a Binomial distribution.";
        public override DataType ReturnType => DataType.Integer;

        /// <summary>
        /// Initializes a new instance of the BinomialDistributionFunction class.
        /// Defines parameters for Trials (n) and Probability (p) of the binomial distribution.
        /// </summary>
        public BinomialDistributionFunction() : base("Statistical")
        {
            Parameters.Add(new Parameter("Trials", "The number of trials.", DataType.Integer, new DataValue(10))); // Default trials (n) is 10
            Parameters.Add(new Parameter("Probability", "The probability of success.", DataType.Decimal, new DataValue(0.5M))); // Default probability (p) is 0.5
        }

        /// <summary>
        /// Generates a random integer value from a Binomial distribution based on the configured Trials and Probability.
        /// </summary>
        /// <param name="context">The data generator context.</param>
        /// <returns>A DataValue containing the binomially distributed integer number.</returns>
        /// <exception cref="InvalidOperationException">Thrown if Trials or Probability parameters are missing or invalid.</exception>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if Trials is negative or Probability is not between 0 and 1.</exception>
        public override DataValue Generate(DataGeneratorContext context)
        {
            int trials = Parameters["Trials"].Value?.ToInt() ??
                         throw new InvalidOperationException("Trials parameter is missing or invalid.");
            decimal probability = Parameters["Probability"].Value?.ToDecimal() ??
                                  throw new InvalidOperationException("Probability parameter is missing or invalid.");

            if (trials < 0)
            {
                throw new ArgumentOutOfRangeException(nameof(trials), "Trials must be non-negative for Binomial distribution.");
            }
            if (probability < 0 || probability > 1)
            {
                throw new ArgumentOutOfRangeException(nameof(probability), "Probability must be between 0 and 1 for Binomial distribution.");
            }

            int successes = 0;
            for (int i = 0; i < trials; i++)
            {
                if (context.Random.NextDouble() < (double)probability)
                {
                    successes++;
                }
            }

            return new DataValue(DataType.Integer, successes);
        }
    }
}
