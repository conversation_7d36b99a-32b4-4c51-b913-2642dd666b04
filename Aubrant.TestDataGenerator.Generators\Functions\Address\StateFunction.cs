using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Functions;
using Aubrant.TestDataGenerator.Core.Utils;
using Bogus;

namespace Aubrant.TestDataGenerator.Generators.Functions
{
    /// <summary>
    /// A generator function that produces realistic state or province names based on the specified country and output locale.
    /// </summary>
    public class StateFunction : BaseFunction
    {
        public override string Name => "State";
        public override string Description => "Generates a realistic state/province name based on the specified country and output locale.";
        public override DataType ReturnType => DataType.String;

        /// <summary>
        /// Initializes a new instance of the StateFunction class.
        /// Defines 'country' and 'locale' parameters.
        /// </summary>
        public StateFunction() : base("Location")
        {
            Parameters.Add(new Parameter("country", "The country to generate the state in.", DataType.String, new DataValue("US"))); // Default country is US
            Parameters.Add(new Parameter("locale", "The output locale.", DataType.String, new DataValue("en")));  // Default output locale is English
        }

        /// <summary>
        /// Generates a random state or province name using the Bogus library, considering the specified country and output locale.
        /// </summary>
        /// <returns>A DataValue containing the generated state/province name as a string.</returns>
        /// <exception cref="InvalidOperationException">Thrown if any parameter is missing or invalid.</exception>
        public override DataValue Generate(DataGeneratorContext context)
        {
            string countryInput = Parameters["country"].Value?.ToString() ??
                                  throw new InvalidOperationException("Country parameter is missing or invalid.");
            string outputLocale = Parameters["locale"].Value?.ToString() ??
                                  throw new InvalidOperationException("Locale parameter is missing or invalid.");

            try
            {
                // Try to get a realistic state from our curated data
                var state = GeoDataRepository.GetRandomState(countryInput, context.Random);
                if (state != null)
                {
                    return new DataValue(DataType.String, state.Name);
                }

                // Fallback to Bogus.NET if no specific state found in curated data for the country
                var faker = new Faker(outputLocale);
                string bogusState = faker.Address.State();
                return new DataValue(DataType.String, bogusState);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error generating state for country '{countryInput}' with locale '{outputLocale}': {ex.Message}", ex);
            }
        }
    }
}
