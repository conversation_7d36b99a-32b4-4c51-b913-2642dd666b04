"use client"

import { useState, useEffect, useMemo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
    X,
    Search,
} from "lucide-react"

import { useToast } from "@/hooks/use-toast"
import AppHeader from "./components/app-header"
import { sampleProjects } from "./lib/mock-data/sampleProjects"
import type { Project } from "./types/database"
import { DataGrid, type Column } from "./components/data-grid"
import { ActionButtons } from "./components/action-buttons";
import { DATABASE_TYPE_GROUPS, getDatabaseTypeIcon } from "./lib/utils/database-helpers.tsx";
import { formatDateTime } from "./lib/utils/date-helpers";

// --- Constants and Utility Functions ---

const ALL_DB_TYPES = "All Database Types"
const ALL_OWNERS = "All Owners"

type SortField = keyof Project
type SortDirection = "asc" | "desc"

// --- Pre-computed values from static data ---
const uniqueOwners = Array.from(new Set(sampleProjects.map((p) => p.Author)))
const uniqueDatabaseTypes = Array.from(new Set(sampleProjects.flatMap((p) => p.DataSources.map((ds) => ds.Provider))))

export default function ProjectsPage() {
    const [searchTerm, setSearchTerm] = useState("")
    const [databaseTypeFilter, setDatabaseTypeFilter] = useState(ALL_DB_TYPES)
    const [ownerFilter, setOwnerFilter] = useState(ALL_OWNERS)
    const [currentPage, setCurrentPage] = useState(1)
    const [itemsPerPage, setItemsPerPage] = useState(5)

    
    const [projects, setProjects] = useState<Project[]>(sampleProjects)
    const [sortField, setSortField] = useState<SortField>("Name")
    const [sortDirection, setSortDirection] = useState<SortDirection>("asc")
    const [loadingActions, setLoadingActions] = useState<Record<string, boolean>>({});
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);
    const [projectToDelete, setProjectToDelete] = useState<Project | null>(null);

    const { toast } = useToast()

    const clearSearch = () => {
        setSearchTerm("")
    }

    const handleEdit = (project: Project) => {
        window.location.href = `/projects/${project.Id}/edit`
    }

    const handleDeleteClick = (project: Project) => {
        setProjectToDelete(project);
        setShowDeleteDialog(true);
    };

    const handleDeleteConfirm = async () => {
        if (!projectToDelete) return;

        const projectId = projectToDelete.Id;
        setShowDeleteDialog(false);
        setLoadingActions((prev) => ({ ...prev, [`delete-${projectId}`]: true }));

        try {
            setProjects((prev) => prev.filter((p) => p.Id !== projectId));

            toast({
                title: "Success",
                description: "Project deleted successfully",
            });
        } catch (error) {
            toast({
                title: "Error",
                description: error instanceof Error ? error.message : "Failed to delete project",
                variant: "destructive",
            });
        } finally {
            setLoadingActions((prev) => ({ ...prev, [`delete-${projectId}`]: false }));
            setProjectToDelete(null);
        }
    };

    const handleGenerate = async (project: Project) => {
        setLoadingActions((prev) => ({ ...prev, [`generate-${project.Id}`]: true }))

        try {
            setProjects((prev) =>
                prev.map((p) => (p.Id === project.Id ? { ...p, LastExecutionDate: new Date() } : p)),
            );

            toast({
                title: "Success",
                description: "Test data generation started",
            })
        } catch (error) {
            toast({
                title: "Error",
                description: error instanceof Error ? error.message : "Failed to generate test data",
                variant: "destructive",
            })
        } finally {
            setLoadingActions((prev) => ({ ...prev, [`generate-${project.Id}`]: false }))
        }
    }

    const handlePageChange = (page: number) => {
        setCurrentPage(page)
    }

    const handleItemsPerPageChange = (value: string) => {
        setItemsPerPage(Number.parseInt(value))
        setCurrentPage(1)
    }

    const handleNewProject = () => {
        // TODO: Replace with client-side routing (e.g., React Router) for smoother navigation
        window.location.href = "/projects/new"
    }

    const handleSort = (field: SortField) => {
        if (sortField === field) {
            setSortDirection(sortDirection === "asc" ? "desc" : "asc")
        } else {
            setSortField(field)
            setSortDirection("asc")
        }
    }

    const filteredProjects = useMemo(() => {
        return projects.filter((project) => {
            const matchesSearch = project.Name.toLowerCase().includes(searchTerm.toLowerCase())
            const matchesDatabase =
                databaseTypeFilter === ALL_DB_TYPES ||
                project.DataSources.some((ds) => ds.Provider === databaseTypeFilter)
            const matchesOwner = ownerFilter === ALL_OWNERS || project.Author === ownerFilter
            return matchesSearch && matchesDatabase && matchesOwner
        })
    }, [projects, searchTerm, databaseTypeFilter, ownerFilter])

    const sortedProjects = useMemo(() => {
        return [...filteredProjects].sort((a, b) => {
            const aValue = a[sortField]
            const bValue = b[sortField]

            if (aValue < bValue) {
                return sortDirection === "asc" ? -1 : 1
            } else if (aValue > bValue) {
                return sortDirection === "asc" ? 1 : -1
            } else {
                return 0
            }
        })
    }, [filteredProjects, sortField, sortDirection])

    const paginatedProjects = useMemo(() => {
        const startIndex = (currentPage - 1) * itemsPerPage
        return sortedProjects.slice(startIndex, startIndex + itemsPerPage)
    }, [sortedProjects, currentPage, itemsPerPage])

    useEffect(() => {
        setCurrentPage(1)
    }, [searchTerm, databaseTypeFilter, ownerFilter])

    const columns: Column<Project>[] = [
        { key: "Name", label: "Name", sortable: true },
        {
            key: "DataSources",
            label: "Data Sources",
            sortable: true,
            render: (project) => (
                <div className="flex flex-wrap gap-1">
                    {project.DataSources.map((ds, index) => (
                        <div key={ds.Id} className="flex items-center gap-1">
                            {getDatabaseTypeIcon(ds.Provider)}
                            <span className="text-xs">{ds.Provider}</span>
                            {index < project.DataSources.length - 1 && <span className="text-gray-400">,</span>}
                        </div>
                    ))}
                </div>
            ),
        },
        { key: "Author", label: "Owner", sortable: true },
        { key: "CreatedDate", label: "Creation Date", sortable: true },
        { key: "LastExecutionDate", label: "Last Execution Date", sortable: true, render: (project) => formatDateTime(project.LastExecutionDate) },
        {
            key: "actions",
            label: "Actions",
            align: "right",
            render: (project) => (
                <ActionButtons
                    project={project}
                    loadingActions={loadingActions}
                    onGenerate={handleGenerate}
                    onEdit={handleEdit}
                    onDelete={handleDeleteClick}
                />
            ),
        },
    ]

    return (
        <div className="container mx-auto p-6 space-y-6">
            <AppHeader />

            <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-semibold text-gray-800">Projects</h2>
                <Button className="bg-blue-600 hover:bg-blue-700 text-white" onClick={handleNewProject}>
                    New Project
                </Button>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 p-4 bg-gray-50 rounded-lg">
                <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                        placeholder="Search by Project Name"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 pr-10"
                    />
                    {searchTerm && (
                        <button
                            onClick={clearSearch}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                        >
                            <X className="h-4 w-4" />
                        </button>
                    )}
                </div>

                <Select value={databaseTypeFilter} onValueChange={setDatabaseTypeFilter}>
                    <SelectTrigger className="w-full sm:w-56">
                        <SelectValue placeholder="Data Source Type" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value={ALL_DB_TYPES}>All Data Source Types</SelectItem>
                        {DATABASE_TYPE_GROUPS.map((group) => (
                            <div key={group.label}>
                                <div className="px-2 py-1.5 text-sm font-semibold text-gray-500 bg-gray-50">{group.label}</div>
                                {uniqueDatabaseTypes
                                    .filter((type) => group.types.includes(type))
                                    .map((type) => (
                                        <SelectItem key={type} value={type} className="pl-6">
                                            <div className="flex items-center gap-2">
                                                {getDatabaseTypeIcon(type)}
                                                {type}
                                            </div>
                                        </SelectItem>
                                    ))}
                            </div>
                        ))}
                    </SelectContent>
                </Select>

                <Select value={ownerFilter} onValueChange={setOwnerFilter}>
                    <SelectTrigger className="w-full sm:w-48">
                        <SelectValue placeholder="Owner" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value={ALL_OWNERS}>All Owners</SelectItem>
                        {uniqueOwners.map((owner) => (
                            <SelectItem key={owner} value={owner}>
                                {owner}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            </div>

            <DataGrid
                data={paginatedProjects}
                columns={columns}
                rowKey={"Id"}
                sortField={sortField}
                sortDirection={sortDirection}
                onSort={handleSort}
                showPagination
                currentPage={currentPage}
                itemsPerPage={itemsPerPage}
                totalItems={sortedProjects.length}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
                emptyMessage="No projects found matching your filters."
            />

            <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action cannot be undone. This will permanently delete the project
                            <span className="font-semibold"> {projectToDelete?.Name}</span> and remove its data from our
                            servers.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={handleDeleteConfirm}>Continue</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    )
}