﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Aubrant.TestDataGenerator.Data\Aubrant.TestDataGenerator.Data.csproj" />
    <ProjectReference Include="..\Aubrant.TestDataGenerator.Core\Aubrant.TestDataGenerator.Core.csproj" />
    <ProjectReference Include="..\Aubrant.TestDataGenerator.Generators\Aubrant.TestDataGenerator.Generators.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="LINQPad.Runtime" Version="8.3.7" />
    <PackageReference Include="Microsoft.SemanticKernel" Version="1.61.0" />
    <PackageReference Include="NJsonSchema" Version="11.4.0" />
    <PackageReference Include="NJsonSchema.NewtonsoftJson" Version="11.4.0" />
  </ItemGroup>

</Project>
