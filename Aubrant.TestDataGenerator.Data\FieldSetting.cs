using Aubrant.TestDataGenerator.Data.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Aubrant.TestDataGenerator.Data
{
    /// <summary>
    /// Represents a field (column) within an entity.
    /// </summary>
    public class FieldSetting
    {
        [Key]
        public int FieldId { get; set; }

        public GeneratorType Type { get; set; }

        public string? Name { get; set; }

        public string? Settings { get; set; }


        [ForeignKey("FieldId")]
        public virtual Field? Field { get; set; }
    }
}