﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Bogus" Version="35.6.3" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp.Scripting" Version="4.14.0" />
    <PackageReference Include="Microsoft.SemanticKernel" Version="1.61.0" />
    <PackageReference Include="Microsoft.SemanticKernel.Connectors.Google" Version="1.61.0-alpha" />
    <PackageReference Include="Microsoft.SemanticKernel.Connectors.Ollama" Version="1.61.0-alpha" />
    <PackageReference Include="Microsoft.SemanticKernel.Connectors.MistralAI" Version="1.61.0-alpha" />
    <PackageReference Include="Microsoft.SemanticKernel.PromptTemplates.Handlebars" Version="1.61.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Aubrant.TestDataGenerator.Core\Aubrant.TestDataGenerator.Core.csproj" />
    <ProjectReference Include="..\Aubrant.TestDataGenerator.Data\Aubrant.TestDataGenerator.Data.csproj" />
  </ItemGroup>

</Project>
