using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.UI.Server.GraphQL;

namespace Aubrant.TestDataGenerator.UI.Server.Mutations
{
    [ExtendObjectType(typeof(Mutation))]
    public class FieldMutations
    {
        public async Task<Field> CreateField(int entityId, string name, DataType type, string? nativeType, int? maxLength, bool isNullable, bool isPrimaryKey, bool isIdentity, bool isUnique, [Service] DatabaseContext context)
        {
            var field = new Field
            {
                EntityId = entityId,
                Name = name,
                Type = type,
                NativeType = nativeType,
                MaxLength = maxLength,
                IsNullable = isNullable,
                IsPrimaryKey = isPrimaryKey,
                IsIdentity = isIdentity,
                IsUnique = isUnique
            };

            context.Fields.Add(field);
            await context.SaveChangesAsync();

            return field;
        }

        public async Task<Field> UpdateField(int id, string? name, DataType? type, string? nativeType, int? maxLength, bool? isNullable, bool? isPrimaryKey, bool? isIdentity, bool? isUnique, [Service] DatabaseContext context)
        {
            var field = await context.Fields.FindAsync(id);

            if (field == null)
            {
                throw new System.Exception("Field not found.");
            }

            if (name != null)
            {
                field.Name = name;
            }

            if (type.HasValue)
            {
                field.Type = type.Value;
            }

            if (nativeType != null)
            {
                field.NativeType = nativeType;
            }

            if (maxLength.HasValue)
            {
                field.MaxLength = maxLength.Value;
            }

            if (isNullable.HasValue)
            {
                field.IsNullable = isNullable.Value;
            }

            if (isPrimaryKey.HasValue)
            {
                field.IsPrimaryKey = isPrimaryKey.Value;
            }

            if (isIdentity.HasValue)
            {
                field.IsIdentity = isIdentity.Value;
            }

            if (isUnique.HasValue)
            {
                field.IsUnique = isUnique.Value;
            }

            await context.SaveChangesAsync();

            return field;
        }

        public async Task<bool> DeleteField(int id, [Service] DatabaseContext context)
        {
            var field = await context.Fields.FindAsync(id);

            if (field == null)
            {
                return false;
            }

            context.Fields.Remove(field);
            await context.SaveChangesAsync();

            return true;
        }
    }
}
