"use client"

import type React from "react"
import type { ProcessedRelationship } from "../types/database";

interface RelationshipConnectionsProps {
  processedRelationships: ProcessedRelationship[]
  onRelationshipClick: (relationship: ProcessedRelationship, e: React.MouseEvent) => void
}

export const RelationshipConnections: React.FC<RelationshipConnectionsProps> = ({ processedRelationships, onRelationshipClick }) => {
  return (
    <>
      {processedRelationships.map((relationship, index) => {
        const { sourceEntity, targetEntity, cardinality, sourcePath, isSelected } = relationship

        const connectionKey = `${sourceEntity.Name}-${targetEntity.Name}-${index}`

        // Determine colors based on selection state
        const lineColor = isSelected ? "#3b82f6" : "#64748b" // slate-600
        const labelColor = isSelected ? "#3b82f6" : "#000000" // black
        const entityWidth = 320
        // Determine connection sides based on relative positions (same logic as connection path)
        const sourceIsLeft = sourceEntity.position.x + entityWidth / 2 < targetEntity.position.x + entityWidth / 2

        let sourceX, targetX
        if (sourceIsLeft) {
            sourceX = sourceEntity.position.x + entityWidth // Right edge of source
            targetX = targetEntity.position.x // Left edge of target
        } else {
            sourceX = sourceEntity.position.x // Left edge of source
            targetX = targetEntity.position.x + entityWidth // Right edge of target
        }

        return (
          <g key={connectionKey}>
            {/* Invisible clickable path */}
            <path
              d={sourcePath}
              stroke="transparent"
              strokeWidth="12"
              fill="none"
              className="cursor-pointer"
              onClick={(e) => onRelationshipClick(relationship, e)}
            />

            {/* Visible connection path */}
            <path
              d={sourcePath}
              stroke={lineColor}
              strokeWidth={isSelected ? "3" : "2"}
              fill="none"
              className="pointer-events-none"
            />

            {/* Source cardinality label */}
            <text
              x={sourceIsLeft ? sourceX + 15 : sourceX - 15}
              y={relationship.sourcePoint.y - 8}
              fontSize="14"
              fontWeight="bold"
              fill={labelColor}
              textAnchor="middle"
              className="select-none pointer-events-none dark:fill-[#e2e8f0] /* slate-200 */"
              style={{ fontFamily: "Arial, sans-serif" }}
            >
              {cardinality.source.symbol}
            </text>

            {/* Target cardinality label */}
            <text
              x={sourceIsLeft ? targetX - 15 : targetX + 15}
              y={relationship.targetPoint.y - 8}
              fontSize="14"
              fontWeight="bold"
              fill={labelColor}
              textAnchor="middle"
              className="select-none pointer-events-none dark:fill-[#e2e8f0] /* slate-200 */"
              style={{ fontFamily: "Arial, sans-serif" }}
            >
              {cardinality.target.symbol}
            </text>
          </g>
        )
      })}
    </>
  )
}
