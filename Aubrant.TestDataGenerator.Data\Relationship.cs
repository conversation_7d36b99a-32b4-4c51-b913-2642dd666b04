using Aubrant.TestDataGenerator.Data.Enums;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Aubrant.TestDataGenerator.Data
{
    /// <summary>
    /// Represents a relationship between two entities.
    /// </summary>
    public class Relationship
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int SourceEntityId { get; set; }

        /// <summary>
        /// Gets or sets the name of the field in the source entity that participates in the relationship.
        /// </summary>
        public string? SourceField { get; set; }
        
        /// <summary>
        /// Gets or sets the name of the target entity in the relationship.
        /// </summary>
        public string? TargetEntity { get; set; }

        /// <summary>
        /// Gets or sets the name of the field in the target entity that participates in the relationship.
        /// </summary>
        public string? TargetField { get; set; }

        /// <summary>
        /// Gets or sets the cardinality of the relationship (e.g., OneToOne, OneToMany).
        /// </summary>
        [JsonConverter(typeof(StringEnumConverter))]
        public Cardinality Cardinality { get; set; }

        [ForeignKey("SourceEntityId")]
        public virtual Entity? SourceEntity { get; set; }
    }
}