using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Functions;
using Aubrant.TestDataGenerator.Core.Utils;
using Bogus;

namespace Aubrant.TestDataGenerator.Generators.Functions{
    /// <summary>
    /// A generator function that produces realistic county names based on the specified country, optional state, and output locale.
    /// </summary>
    public class CountyFunction : BaseFunction
    {
        public override string Name => "County";
        public override string Description => "Generates a realistic county name based on the specified country, optional state, and output locale.";
        public override DataType ReturnType => DataType.String;

        /// <summary>
        /// Initializes a new instance of the CountyFunction class.
        /// Defines 'country', 'locale', and optional 'state' parameters.
        /// </summary>
        public CountyFunction() : base("Location")
        {
            Parameters.Add(new Parameter("country", "The country to generate the county in.", DataType.String, new DataValue("US"))); // Default country is US
            Parameters.Add(new Parameter("locale", "The output locale.", DataType.String, new DataValue("en")));  // Default output locale is English
            Parameters.Add(new Parameter("state", "The state to generate the county in.", DataType.String, null)); // Optional state parameter
        }

        /// <summary>
        /// Generates a random county name using the GeoDataRepository or Bogus library, considering the specified country, optional state, and output locale.
        /// </summary>
        /// <returns>A DataValue containing the generated county name as a string.</returns>
        /// <exception cref="InvalidOperationException">Thrown if any parameter is missing or invalid.</exception>
        public override DataValue Generate(DataGeneratorContext context)
        {
            string countryInput = Parameters["country"].Value?.ToString() ??
                                  throw new InvalidOperationException("Country parameter is missing or invalid.");
            string outputLocale = Parameters["locale"].Value?.ToString() ??
                                  throw new InvalidOperationException("Locale parameter is missing or invalid.");
            string? stateInput = Parameters["state"].Value?.ToString();

            try
            {
                string? county = null;

                // Try to get a realistic county from our curated data
                if (!string.IsNullOrWhiteSpace(stateInput))
                {
                    county = GeoDataRepository.GetRandomCounty(countryInput, context.Random, stateInput);
                }
                else
                {
                    county = GeoDataRepository.GetRandomCounty(countryInput, context.Random);
                }

                if (county != null)
                {
                    return new DataValue(DataType.String, county);
                }

                // Fallback to Bogus.NET if no specific county found in curated data for the country/state
                // Bogus.NET does not have a direct County generator, so we might need a workaround or return a generic value.
                // For now, we'll return a generic placeholder or throw an exception if realism is critical.
                var faker = new Faker(outputLocale);
                // As Bogus doesn't have a direct County, we might use something like City or Address line 2 as a fallback
                // For now, let's just return a generic string to indicate no specific county was found.
                return new DataValue(DataType.String, faker.Address.County()); // Bogus.Address.County() might exist in some locales
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error generating county for country '{countryInput}' with state '{stateInput}' and locale '{outputLocale}': {ex.Message}", ex);
            }
        }
    }
}
