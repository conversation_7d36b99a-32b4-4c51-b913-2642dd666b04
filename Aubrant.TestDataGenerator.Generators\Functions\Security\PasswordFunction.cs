using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Functions;
using System.Text;

namespace Aubrant.TestDataGenerator.Generators.Functions
{
    /// <summary>
    /// A generator function that produces random passwords with configurable complexity.
    /// </summary>
    public class PasswordFunction : BaseFunction
    {
        public override string Name => "Password";
        public override string Description => "Generates a random password with specified length and complexity.";
        public override DataType ReturnType => DataType.String;

        private const string LowercaseChars = "abcdefghijklmnopqrstuvwxyz";
        private const string UppercaseChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        private const string NumberChars = "0123456789";
        private const string SymbolChars = "!@#$%^&*()_+-=[]{}|;:,.<>?";

        /// <summary>
        /// Initializes a new instance of the PasswordFunction class.
        /// </summary>
        public PasswordFunction() : base("Security")
        {
            Parameters.Add(new Parameter("Length", "The length of the password.", DataType.Integer, new DataValue(12)));
            Parameters.Add(new Parameter("IncludeUppercase", "Whether to include uppercase characters.", DataType.Boolean, new DataValue(true)));
            Parameters.Add(new Parameter("IncludeNumbers", "Whether to include numbers.", DataType.Boolean, new DataValue(true)));
            Parameters.Add(new Parameter("IncludeSymbols", "Whether to include symbols.", DataType.Boolean, new DataValue(true)));
        }

        /// <summary>
        /// Generates a random password based on the configured parameters.
        /// </summary>
        public override DataValue Generate(DataGeneratorContext context)
        {
            int length = Parameters["Length"].Value?.ToInt() ?? 12;
            bool includeUppercase = Parameters["IncludeUppercase"].Value?.ToBoolean() ?? true;
            bool includeNumbers = Parameters["IncludeNumbers"].Value?.ToBoolean() ?? true;
            bool includeSymbols = Parameters["IncludeSymbols"].Value?.ToBoolean() ?? true;

            if (length <= 0)
            {
                throw new ArgumentOutOfRangeException(nameof(length), "Length must be positive.");
            }

            var charPool = new StringBuilder(LowercaseChars);
            var password = new StringBuilder();

            // Build the character pool and ensure at least one of each required character type is included
            if (includeUppercase)
            {
                charPool.Append(UppercaseChars);
                password.Append(UppercaseChars[context.Random.Next(UppercaseChars.Length)]);
            }
            if (includeNumbers)
            {
                charPool.Append(NumberChars);
                password.Append(NumberChars[context.Random.Next(NumberChars.Length)]);
            }
            if (includeSymbols)
            {
                charPool.Append(SymbolChars);
                password.Append(SymbolChars[context.Random.Next(SymbolChars.Length)]);
            }

            // Add at least one lowercase character
            password.Append(LowercaseChars[context.Random.Next(LowercaseChars.Length)]);

            if (length < password.Length)
            {
                throw new ArgumentException("Password length is too short to include all required character types.");
            }

            // Fill the rest of the password length with random characters from the pool
            for (int i = password.Length; i < length; i++)
            {
                password.Append(charPool[context.Random.Next(charPool.Length)]);
            }

            // Shuffle the generated password to ensure randomness
            var shuffledPassword = new string(password.ToString().ToCharArray().OrderBy(c => context.Random.Next()).ToArray());

            return new DataValue(DataType.String, shuffledPassword);
        }
    }
}
