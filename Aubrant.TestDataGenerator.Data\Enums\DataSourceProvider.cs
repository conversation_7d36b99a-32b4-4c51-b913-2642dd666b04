namespace Aubrant.TestDataGenerator.Data.Enums
{
    /// <summary>
    /// Defines the specific provider for a data source.
    /// </summary>
    public enum DataSourceProvider
    {
        /// <summary>Comma Separated Values file.</summary>
        CSV,
        /// <summary>Microsoft SQL Server database.</summary>
        SQLServer,
        /// <summary>SQLite database.</summary>
        SQLite,
        /// <summary>MongoDB database.</summary>
        MongoDB,
        /// <summary>Microsoft Excel file.</summary>
        Excel,
        /// <summary>Neo4j graph database.</summary>
        Neo4j
    }
}