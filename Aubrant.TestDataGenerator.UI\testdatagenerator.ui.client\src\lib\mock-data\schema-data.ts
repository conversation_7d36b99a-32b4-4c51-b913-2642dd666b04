export interface TableColumn {
  name: string
  type: string
  isPrimaryKey: boolean
  isNotNull: boolean
}

export interface TableRelationship {
  type: "OneToMany" | "ManyToOne" | "OneToOne" | "ManyToMany"
  targetTable?: string
  foreignKey?: string
  targetCollection?: string
}

export interface DatabaseTable {
  name: string
  schema: string
  columns: TableColumn[]
  relationships: TableRelationship[]
}

export interface Collection {
  name: string
  fields: TableColumn[]
  relationships: TableRelationship[]
}

export interface SchemaDataSource {
  id: string
  name: string
  type: string
  provider: string
  schemas?: {
    name: string
    tables: DatabaseTable[]
  }[]
  collections?: Collection[]
}

export const sampleDataSources: SchemaDataSource[] = [
  {
    id: "ds-sql-server",
    name: "Main Database",
    type: "Relational Database",
    provider: "SQL Server",
    schemas: [
      {
        name: "dbo",
        tables: [
          {
            name: "Album",
            schema: "dbo",
            columns: [
              { name: "AlbumId", type: "INTEGER", isPrimaryKey: true, isNotNull: true },
              { name: "Title", type: "NVARCHAR(160)", isPrimaryKey: false, isNotNull: true },
              { name: "ArtistId", type: "INTEGER", isPrimaryKey: false, isNotNull: true },
            ],
            relationships: [
              { type: "ManyToOne", targetTable: "Artist", foreignKey: "ArtistId" },
              { type: "OneToMany", targetTable: "Track", foreignKey: "AlbumId" },
            ],
          },
          {
            name: "Artist",
            schema: "dbo",
            columns: [
              { name: "ArtistId", type: "INTEGER", isPrimaryKey: true, isNotNull: true },
              { name: "Name", type: "NVARCHAR(120)", isPrimaryKey: false, isNotNull: false },
            ],
            relationships: [{ type: "OneToMany", targetTable: "Album", foreignKey: "ArtistId" }],
          },
          {
            name: "Customer",
            schema: "dbo",
            columns: [
              { name: "CustomerId", type: "INTEGER", isPrimaryKey: true, isNotNull: true },
              { name: "FirstName", type: "NVARCHAR(40)", isPrimaryKey: false, isNotNull: true },
              { name: "LastName", type: "NVARCHAR(20)", isPrimaryKey: false, isNotNull: true },
              { name: "Company", type: "NVARCHAR(80)", isPrimaryKey: false, isNotNull: false },
              { name: "Address", type: "NVARCHAR(70)", isPrimaryKey: false, isNotNull: false },
              { name: "City", type: "NVARCHAR(40)", isPrimaryKey: false, isNotNull: false },
              { name: "State", type: "NVARCHAR(40)", isPrimaryKey: false, isNotNull: false },
              { name: "Country", type: "NVARCHAR(40)", isPrimaryKey: false, isNotNull: false },
              { name: "PostalCode", type: "NVARCHAR(10)", isPrimaryKey: false, isNotNull: false },
              { name: "Phone", type: "NVARCHAR(24)", isPrimaryKey: false, isNotNull: false },
              { name: "Fax", type: "NVARCHAR(24)", isPrimaryKey: false, isNotNull: false },
              { name: "Email", type: "NVARCHAR(60)", isPrimaryKey: false, isNotNull: true },
              { name: "SupportRepId", type: "INTEGER", isPrimaryKey: false, isNotNull: false },
            ],
            relationships: [
              { type: "ManyToOne", targetTable: "Employee", foreignKey: "SupportRepId" },
              { type: "OneToMany", targetTable: "Invoice", foreignKey: "CustomerId" },
            ],
          },
          {
            name: "Employee",
            schema: "dbo",
            columns: [
              { name: "EmployeeId", type: "INTEGER", isPrimaryKey: true, isNotNull: true },
              { name: "FirstName", type: "NVARCHAR(20)", isPrimaryKey: false, isNotNull: true },
              { name: "LastName", type: "NVARCHAR(20)", isPrimaryKey: false, isNotNull: true },
              { name: "Title", type: "NVARCHAR(30)", isPrimaryKey: false, isNotNull: false },
              { name: "Gender", type: "CHAR(1)", isPrimaryKey: false, isNotNull: false },
              { name: "ReportsTo", type: "INTEGER", isPrimaryKey: false, isNotNull: false },
              { name: "BirthDate", type: "DATETIME", isPrimaryKey: false, isNotNull: false },
              { name: "HireDate", type: "DATETIME", isPrimaryKey: false, isNotNull: false },
              { name: "Address", type: "NVARCHAR(70)", isPrimaryKey: false, isNotNull: false },
              { name: "City", type: "NVARCHAR(40)", isPrimaryKey: false, isNotNull: false },
              { name: "State", type: "NVARCHAR(40)", isPrimaryKey: false, isNotNull: false },
              { name: "Country", type: "NVARCHAR(40)", isPrimaryKey: false, isNotNull: false },
              { name: "PostalCode", type: "NVARCHAR(10)", isPrimaryKey: false, isNotNull: false },
              { name: "Phone", type: "NVARCHAR(24)", isPrimaryKey: false, isNotNull: false },
              { name: "Fax", type: "NVARCHAR(24)", isPrimaryKey: false, isNotNull: false },
              { name: "Email", type: "NVARCHAR(60)", isPrimaryKey: false, isNotNull: false },
            ],
            relationships: [
              { type: "ManyToOne", targetTable: "Employee", foreignKey: "ReportsTo" },
              { type: "OneToMany", targetTable: "Customer", foreignKey: "SupportRepId" },
            ],
          },
          {
            name: "Invoice",
            schema: "dbo",
            columns: [
              { name: "InvoiceId", type: "INTEGER", isPrimaryKey: true, isNotNull: true },
              { name: "CustomerId", type: "INTEGER", isPrimaryKey: false, isNotNull: true },
              { name: "InvoiceDate", type: "DATETIME", isPrimaryKey: false, isNotNull: true },
              { name: "BillingAddress", type: "NVARCHAR(70)", isPrimaryKey: false, isNotNull: false },
              { name: "BillingCity", type: "NVARCHAR(40)", isPrimaryKey: false, isNotNull: false },
              { name: "BillingState", type: "NVARCHAR(40)", isPrimaryKey: false, isNotNull: false },
              { name: "BillingCountry", type: "NVARCHAR(40)", isPrimaryKey: false, isNotNull: false },
              { name: "BillingPostalCode", type: "NVARCHAR(10)", isPrimaryKey: false, isNotNull: false },
              { name: "Total", type: "NUMERIC(10,2)", isPrimaryKey: false, isNotNull: true },
            ],
            relationships: [
              { type: "ManyToOne", targetTable: "Customer", foreignKey: "CustomerId" },
              { type: "OneToMany", targetTable: "InvoiceLine", foreignKey: "InvoiceId" },
            ],
          },
          {
            name: "Track",
            schema: "dbo",
            columns: [
              { name: "TrackId", type: "INTEGER", isPrimaryKey: true, isNotNull: true },
              { name: "Name", type: "NVARCHAR(200)", isPrimaryKey: false, isNotNull: true },
              { name: "AlbumId", type: "INTEGER", isPrimaryKey: false, isNotNull: false },
              { name: "MediaTypeId", type: "INTEGER", isPrimaryKey: false, isNotNull: true },
              { name: "GenreId", type: "INTEGER", isPrimaryKey: false, isNotNull: false },
              { name: "Composer", type: "NVARCHAR(220)", isPrimaryKey: false, isNotNull: false },
              { name: "Milliseconds", type: "INTEGER", isPrimaryKey: false, isNotNull: true },
              { name: "Bytes", type: "INTEGER", isPrimaryKey: false, isNotNull: false },
              { name: "UnitPrice", type: "NUMERIC(10,2)", isPrimaryKey: false, isNotNull: true },
            ],
            relationships: [
              { type: "ManyToOne", targetTable: "Album", foreignKey: "AlbumId" },
              { type: "OneToMany", targetTable: "InvoiceLine", foreignKey: "TrackId" },
            ],
          },
        ],
      },
      {
        name: "auth",
        tables: [
          {
            name: "User",
            schema: "auth",
            columns: [
              { name: "UserId", type: "INTEGER", isPrimaryKey: true, isNotNull: true },
              { name: "Username", type: "NVARCHAR(50)", isPrimaryKey: false, isNotNull: true },
              { name: "Email", type: "NVARCHAR(100)", isPrimaryKey: false, isNotNull: true },
              { name: "PasswordHash", type: "NVARCHAR(255)", isPrimaryKey: false, isNotNull: true },
              { name: "FirstName", type: "NVARCHAR(50)", isPrimaryKey: false, isNotNull: false },
              { name: "LastName", type: "NVARCHAR(50)", isPrimaryKey: false, isNotNull: false },
              { name: "IsActive", type: "BIT", isPrimaryKey: false, isNotNull: true },
              { name: "CreatedAt", type: "DATETIME", isPrimaryKey: false, isNotNull: true },
              { name: "UpdatedAt", type: "DATETIME", isPrimaryKey: false, isNotNull: false },
            ],
            relationships: [
              { type: "ManyToMany", targetTable: "Role", foreignKey: "UserId" },
              { type: "OneToMany", targetTable: "UserSession", foreignKey: "UserId" },
            ],
          },
          {
            name: "Role",
            schema: "auth",
            columns: [
              { name: "RoleId", type: "INTEGER", isPrimaryKey: true, isNotNull: true },
              { name: "RoleName", type: "NVARCHAR(50)", isPrimaryKey: false, isNotNull: true },
              { name: "Description", type: "NVARCHAR(255)", isPrimaryKey: false, isNotNull: false },
              { name: "IsActive", type: "BIT", isPrimaryKey: false, isNotNull: true },
              { name: "CreatedAt", type: "DATETIME", isPrimaryKey: false, isNotNull: true },
            ],
            relationships: [
              { type: "ManyToMany", targetTable: "User", foreignKey: "RoleId" },
              { type: "ManyToMany", targetTable: "Permission", foreignKey: "RoleId" },
            ],
          },
          {
            name: "Permission",
            schema: "auth",
            columns: [
              { name: "PermissionId", type: "INTEGER", isPrimaryKey: true, isNotNull: true },
              { name: "PermissionName", type: "NVARCHAR(50)", isPrimaryKey: false, isNotNull: true },
              { name: "Description", type: "NVARCHAR(255)", isPrimaryKey: false, isNotNull: false },
              { name: "Resource", type: "NVARCHAR(100)", isPrimaryKey: false, isNotNull: true },
              { name: "Action", type: "NVARCHAR(50)", isPrimaryKey: false, isNotNull: true },
              { name: "IsActive", type: "BIT", isPrimaryKey: false, isNotNull: true },
            ],
            relationships: [{ type: "ManyToMany", targetTable: "Role", foreignKey: "PermissionId" }],
          },
        ],
      },
    ],
  },
  {
    id: "ds-mongodb",
    name: "Analytics Database",
    type: "NoSQL Database",
    provider: "MongoDB",
    collections: [
      {
        name: "users",
        fields: [
          { name: "_id", type: "ObjectId", isPrimaryKey: true, isNotNull: true },
          { name: "username", type: "String", isPrimaryKey: false, isNotNull: true },
          { name: "email", type: "String", isPrimaryKey: false, isNotNull: true },
          { name: "profile", type: "Object", isPrimaryKey: false, isNotNull: false },
          { name: "preferences", type: "Array", isPrimaryKey: false, isNotNull: false },
          { name: "createdAt", type: "Date", isPrimaryKey: false, isNotNull: true },
          { name: "lastLogin", type: "Date", isPrimaryKey: false, isNotNull: false },
        ],
        relationships: [
          { type: "OneToMany", targetCollection: "sessions", foreignKey: "userId" },
          { type: "OneToMany", targetCollection: "analytics", foreignKey: "userId" },
        ],
      },
      {
        name: "sessions",
        fields: [
          { name: "_id", type: "ObjectId", isPrimaryKey: true, isNotNull: true },
          { name: "userId", type: "ObjectId", isPrimaryKey: false, isNotNull: true },
          { name: "sessionToken", type: "String", isPrimaryKey: false, isNotNull: true },
          { name: "deviceInfo", type: "Object", isPrimaryKey: false, isNotNull: false },
          { name: "ipAddress", type: "String", isPrimaryKey: false, isNotNull: false },
          { name: "startTime", type: "Date", isPrimaryKey: false, isNotNull: true },
          { name: "endTime", type: "Date", isPrimaryKey: false, isNotNull: false },
          { name: "isActive", type: "Boolean", isPrimaryKey: false, isNotNull: true },
        ],
        relationships: [{ type: "ManyToOne", targetCollection: "users", foreignKey: "userId" }],
      },
      {
        name: "analytics",
        fields: [
          { name: "_id", type: "ObjectId", isPrimaryKey: true, isNotNull: true },
          { name: "userId", type: "ObjectId", isPrimaryKey: false, isNotNull: true },
          { name: "eventType", type: "String", isPrimaryKey: false, isNotNull: true },
          { name: "eventData", type: "Object", isPrimaryKey: false, isNotNull: false },
          { name: "metadata", type: "Object", isPrimaryKey: false, isNotNull: false },
          { name: "timestamp", type: "Date", isPrimaryKey: false, isNotNull: true },
          { name: "source", type: "String", isPrimaryKey: false, isNotNull: false },
        ],
        relationships: [{ type: "ManyToOne", targetCollection: "users", foreignKey: "userId" }],
      },
      {
        name: "products",
        fields: [
          { name: "_id", type: "ObjectId", isPrimaryKey: true, isNotNull: true },
          { name: "name", type: "String", isPrimaryKey: false, isNotNull: true },
          { name: "description", type: "String", isPrimaryKey: false, isNotNull: false },
          { name: "price", type: "Number", isPrimaryKey: false, isNotNull: true },
          { name: "category", type: "String", isPrimaryKey: false, isNotNull: true },
          { name: "tags", type: "Array", isPrimaryKey: false, isNotNull: false },
          { name: "inventory", type: "Object", isPrimaryKey: false, isNotNull: false },
          { name: "createdAt", type: "Date", isPrimaryKey: false, isNotNull: true },
          { name: "updatedAt", type: "Date", isPrimaryKey: false, isNotNull: false },
        ],
        relationships: [],
      },
    ],
  },
]
