using Aubrant.TestDataGenerator.Data.Enums;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Aubrant.TestDataGenerator.Data
{
    /// <summary>
    /// Represents a data source, containing its metadata and extracted entities.
    /// </summary>
    public class DataSource
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int ProjectId { get; set; }

        [Required]
        public required string Name { get; set; }

        /// <summary>
        /// Gets or sets a description for the data source.
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Gets or sets the general type of the data source (e.g., File, RelationalDatabase).
        /// </summary>
        [Required]
        [JsonConverter(typeof(StringEnumConverter))]
        public required DataSourceType Type { get; set; }

        /// <summary>
        /// Gets or sets the specific provider of the data source (e.g., CSV, SQLServer).
        /// </summary>
        [Required]
        [JsonConverter(typeof(StringEnumConverter))]
        public required DataSourceProvider Provider { get; set; }

        /// <summary>
        /// Gets or sets the connection string or file path for the data source.
        /// </summary>
        [Required]
        public required string ConnectionString { get; set; }

        [ForeignKey("ProjectId")]
        public virtual Project? Project { get; set; }

        /// <summary>
        /// Gets or sets the list of entities (tables/collections) found in this data source.
        /// </summary>
        public virtual ICollection<Entity> Entities { get; set; } = new List<Entity>();

        /// <summary>
        /// Serializes the DataSource object to a JSON string.
        /// </summary>
        /// <returns>A JSON string representation of the DataSource.</returns>
        public string Serialize() => JsonConvert.SerializeObject(this, Formatting.Indented);

        /// <summary>
        /// Saves the serialized DataSource object to a specified file path.
        /// </summary>
        /// <param name="filePath">The absolute path to the file where the DataSource will be saved.</param>
        public void SaveToFile(string filePath)
        {
            var json = Serialize();
            File.WriteAllText(filePath, json);
        }

        /// <summary>
        /// Loads and deserializes a DataSource object from a specified file path.
        /// </summary>
        /// <param name="filePath">The absolute path to the file from which the DataSource will be loaded.</param>
        /// <returns>A deserialized DataSource object.</returns>
        public static DataSource? LoadFromFile(string filePath)
        {
            var json = File.ReadAllText(filePath);
            return JsonConvert.DeserializeObject<DataSource>(json);
        }
    }
}