using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.Data.Enums;
using Aubrant.TestDataGenerator.Generators.Scripts;
using Aubrant.TestDataGenerator.SchemaProfiler;
using Microsoft.Data.Sqlite;
using System.Data;

namespace Aubrant.TestDataGenerator.Tests
{
    public class CreateAndInitializeSQLiteDatabase
    {
        [Fact(DisplayName = "1. Create internal SQLite Database")]
        public async Task CreateDatabase()
        {
            string dbPath = Path.Combine(Directory.GetParent(AppDomain.CurrentDomain.BaseDirectory).Parent.Parent.Parent.Parent.FullName, "Aubrant.TestDataGenerator.Data\\Database\\TestDataGenerator.db");

            File.Delete(dbPath);
            string connectionString = $"Data Source={dbPath}";

            // Initialize SQLitePCLRaw
            SQLitePCL.Batteries.Init();

            // Create DB Connection
            using (var connection = new SqliteConnection(connectionString))
            {
                connection.Open();

                var command = connection.CreateCommand();
                command.CommandText =
                """
				DROP TABLE IF EXISTS Relationships;
				DROP TABLE IF EXISTS KeyConstraints;
				DROP TABLE IF EXISTS FieldSettings;
				DROP TABLE IF EXISTS Fields;
				DROP TABLE IF EXISTS Entities;
				DROP TABLE IF EXISTS DataSources;
				DROP TABLE IF EXISTS Projects;
		
				CREATE TABLE IF NOT EXISTS Projects (
					Id INTEGER PRIMARY KEY AUTOINCREMENT,
					Name TEXT NOT NULL,
					Description TEXT,
					Author TEXT,
					CreatedDate DATETIME NOT NULL
				);
		
				CREATE TABLE IF NOT EXISTS DataSources (
					Id INTEGER PRIMARY KEY AUTOINCREMENT,
					ProjectId INTEGER NOT NULL,
					Name TEXT NOT NULL,
					Description TEXT,
					Type INTEGER NOT NULL CHECK(Type IN (0, 1, 2)), -- 0: File, 1: RelationalDatabase, 2: NoSQLDatabase
					Provider INTEGER NOT NULL CHECK(Provider IN (0, 1, 2, 3, 4, 5)), -- 0: CSV, 1: Excel, 2: SQLite, 3: SQLServer, 4: MongoDB, 5: Neo4J
					ConnectionString TEXT NOT NULL,
					FOREIGN KEY (ProjectId) REFERENCES Projects(Id)
				);

				CREATE TABLE IF NOT EXISTS Entities (
					Id INTEGER PRIMARY KEY AUTOINCREMENT,
					DataSourceId INTEGER NOT NULL,
					Name TEXT,
					Schema TEXT,
					SyncStrategy INTEGER CHECK(SyncStrategy IN (0, 1, 2)), -- 0: Readonly, 1: TruncateAndInsert, 2: Append
					FOREIGN KEY (DataSourceId) REFERENCES DataSources(Id)
				);

				CREATE TABLE IF NOT EXISTS Fields (
					Id INTEGER PRIMARY KEY AUTOINCREMENT,
					EntityId INTEGER NOT NULL,
					Name TEXT,
					Type INTEGER,
					NativeType TEXT,
					MaxLength INTEGER,
					IsNullable BOOLEAN NOT NULL,
					IsPrimaryKey BOOLEAN NOT NULL,
					IsIdentity BOOLEAN NOT NULL,
					IsUnique BOOLEAN NOT NULL,
					FOREIGN KEY (EntityId) REFERENCES Entities(Id)
				);

				CREATE TABLE IF NOT EXISTS FieldSettings (
					FieldId INTEGER PRIMARY KEY,
					Type INTEGER CHECK(Type IN (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10)), -- 0: None, 1: Constant, 2: AutoIncrement, 3: Random, 4: Choice, 5: Relative, 6: Reference, 7: Pattern, 8: Script, 9: AI, 10: Function
					Name TEXT, -- Function/Pattern Name
					Settings TEXT, -- JSON content with generator settings, like parameters, tokens, etc
					FOREIGN KEY (FieldId) REFERENCES Fields(Id)
				);

				CREATE TABLE IF NOT EXISTS KeyConstraints (
					Id INTEGER PRIMARY KEY AUTOINCREMENT,
					EntityId INTEGER NOT NULL,
					KeyType INTEGER CHECK(KeyType IN (0, 1)), -- 0: Primary, 1: Foreign
					Columns TEXT NOT NULL,
					ReferencedEntity TEXT,
					ReferencedColumns TEXT,
					FOREIGN KEY (EntityId) REFERENCES Entities(Id)
				);

				CREATE TABLE IF NOT EXISTS Relationships (
					Id INTEGER PRIMARY KEY AUTOINCREMENT,
					SourceEntityId INTEGER NOT NULL,
					SourceField TEXT,
					TargetEntity TEXT,
					TargetField TEXT,
					Cardinality INTEGER NOT NULL CHECK(Cardinality IN (0, 1, 2)), -- 0: OneToOne, 1: OneToMany, 2: ManyToOne
					FOREIGN KEY (SourceEntityId) REFERENCES Entities(Id)
				);
				""";
                int result = command.ExecuteNonQuery();

                Assert.True(result == 0, "Database not created successfully");
            }

            await InitDatabase();
        }

        public async Task InitDatabase()
        {
            using (var db = new DatabaseContext())
            {
                // Ensure the database is created
                Assert.True(db.Database.CanConnect(), "Database doesn't exists");

                var newProject = new Project
                {
                    Name = "Test Data Generator - Project 1",
                    Description = "First Project with Northwind Small DB, with Schema Analysis loaded from Sai's Excel",
                    Author = "Heiner Morales",
                    CreatedDate = DateTime.Now
                };

                db.Projects.Add(newProject);

                await db.SaveChangesAsync();

                Assert.True(db.Projects.Any(p => p.Name == newProject.Name), "Project not created successfully");

                #region Analyze Sample Database and Initialize internal Project

                string sampleDbPath = Path.Combine(Path.GetDirectoryName(db.DbPath), "northwind_small.sqlite");

                var sqliteManager = new DataSourceProfiler(DataSourceProvider.SQLite, $"Data Source={sampleDbPath}");
                var sqliteSchema = await sqliteManager.GetSchemaAsync();

                Assert.NotNull(sqliteSchema);
                Assert.NotEmpty(sqliteSchema.Entities);

                sampleDbPath = Path.Combine(Path.GetDirectoryName(db.DbPath), "SemanticAnalysis_20250808.xlsx");
                var excelManager = new DataSourceProfiler(DataSourceProvider.Excel, sampleDbPath);
                var excelSchema = await excelManager.GetSchemaAsync();
                await excelManager.LoadSampleDataAsync(excelSchema, int.MaxValue);

                Assert.NotNull(excelSchema);
                Assert.NotEmpty(excelSchema.Entities);

                newProject.DataSources.Add(new DataSource
                {
                    Name = sqliteSchema.Name,
                    Description = sqliteSchema.Description,
                    Type = sqliteSchema.Type,
                    Provider = sqliteSchema.Provider,
                    ConnectionString = sqliteSchema.ConnectionString,
                    Entities = sqliteSchema.Entities.Select(e => new Entity
                    {
                        Name = e.Name,
                        SyncStrategy = 0,
                        Fields = e.Fields.Select(f =>
                        {
                            var (generatorType, generatorName, generationSettings) = GetGetGeneratorType(excelSchema, e.Name, f.Name, f.Type, f.MaxLength, f.IsNullable, f.IsIdentity, f.IsPrimaryKey, f.IsUnique);

                            return new Field
                            {
                                Name = f.Name,
                                Type = f.Type == DataType.String && f.Name.EndsWith("date", StringComparison.OrdinalIgnoreCase) ? DataType.DateTime : f.Type,
                                NativeType = f.NativeType,
                                MaxLength = f.MaxLength,
                                IsNullable = f.IsNullable,
                                IsIdentity = f.IsIdentity,
                                IsPrimaryKey = f.IsPrimaryKey,
                                IsUnique = f.IsUnique,
                                FieldSetting = new FieldSetting
                                {
                                    Type = generatorType,
                                    Name = generatorName,
                                    Settings = generationSettings
                                }
                            };
                        }).ToList(),
                        Relationships = e.Relationships.Select(r => new Relationship
                        {
                            SourceField = r.SourceField,
                            TargetEntity = r.TargetEntity,
                            TargetField = r.TargetField
                        }).ToList()
                    }).ToList()
                });

                db.SaveChanges();
                Assert.True(db.DataSources.Any(ds => ds.Name == sqliteSchema.Name), "Data source not created successfully");

                #endregion

                await SecondDB();
            }
        }

        public async Task SecondDB()
        {
            using (var db = new DatabaseContext())
            {
                // Ensure the database is created
                Assert.True(db.Database.CanConnect(), "Database doesn't exists");

                var newProject = new Project
                {
                    Name = "Test Data Generator - Project 2",
                    Description = "Second Project with Northwind Full DB, in Schema Discovery Stage - Pending Schema Analysis",
                    Author = "Heiner Morales",
                    CreatedDate = DateTime.Now
                };

                db.Projects.Add(newProject);

                await db.SaveChangesAsync();

                Assert.True(db.Projects.Any(p => p.Name == newProject.Name), "Project not created successfully");

                #region Analyze Sample Database and Initialize internal Project

                string sampleDbPath = Path.Combine(Path.GetDirectoryName(db.DbPath), "northwind.db");

                var sqliteManager = new DataSourceProfiler(DataSourceProvider.SQLite, $"Data Source={sampleDbPath}");
                var sqliteSchema = await sqliteManager.GetSchemaAsync();

                Assert.NotNull(sqliteSchema);
                Assert.NotEmpty(sqliteSchema.Entities);

                newProject.DataSources.Add(new DataSource
                {
                    Name = sqliteSchema.Name,
                    Description = sqliteSchema.Description,
                    Type = sqliteSchema.Type,
                    Provider = sqliteSchema.Provider,
                    ConnectionString = sqliteSchema.ConnectionString,
                    Entities = sqliteSchema.Entities.Select(e => new Entity
                    {
                        Name = e.Name,
                        SyncStrategy = 0,
                        Fields = e.Fields.Select(f =>
                        {
                            var (generatorType, generatorName, generationSettings) = GetGetGeneratorType(null, e.Name, f.Name, f.Type, f.MaxLength, f.IsNullable, f.IsIdentity, f.IsPrimaryKey, f.IsUnique);

                            return new Field
                            {
                                Name = f.Name,
                                Type = f.Type,
                                NativeType = f.NativeType,
                                MaxLength = f.MaxLength,
                                IsNullable = f.IsNullable,
                                IsIdentity = f.IsIdentity,
                                IsPrimaryKey = f.IsPrimaryKey,
                                IsUnique = f.IsUnique,
                                FieldSetting = new FieldSetting
                                {
                                    Type = generatorType,
                                    Name = generatorName,
                                    Settings = generationSettings
                                }
                            };
                        }).ToList(),
                        Relationships = e.Relationships.Select(r => new Relationship
                        {
                            SourceField = r.SourceField,
                            TargetEntity = r.TargetEntity,
                            TargetField = r.TargetField
                        }).ToList()
                    }).ToList()
                });

                db.SaveChanges();
                Assert.True(db.DataSources.Any(ds => ds.Name == sqliteSchema.Name), "Data source not created successfully");

                #endregion
            }
        }

        private (GeneratorType generatorType, string generatorName, string generationSettings) GetGetGeneratorType(DataSource data, string entityName, string name, DataType type, int? maxLength, bool isNullable, bool isIdentity, bool isPrimaryKey, bool isUnique)
        {
            GeneratorType generatorType = GeneratorType.None;
            string generatorName = string.Empty;
            string generationSettings = "{}";

            if (data != null)
            {
                var entity = data.Entities.FirstOrDefault(e => e.Name == entityName);
                var row = entity.Data.AsEnumerable().FirstOrDefault(r => r.Field<string>("Field Name") == name);

                if (row != null)
                {
                    generatorType = Enum.Parse<GeneratorType>(row.Field<string>("Assigned Generator Type"));
                    generatorName = row.Field<string>("Generator Name");
                    generationSettings = row.Field<string>("Settings/Options");
                }
            }
            else if (isIdentity || (isPrimaryKey && type is DataType.Integer))
            {
                generatorType = GeneratorType.AutoIncrement;
                generationSettings = new { Start = 1, Step = 1 }.ToJsonString();
            }
            else if (type is DataType.Integer or DataType.Decimal)
            {
                generatorType = GeneratorType.None;
            }

            generatorName = generatorType switch
            {
                GeneratorType.Function or GeneratorType.Pattern => generatorName,
                _ => null
            };

            return (generatorType, generatorName, generationSettings);
        }
    }
}