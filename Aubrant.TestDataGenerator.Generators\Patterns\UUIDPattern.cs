﻿using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Patterns;

namespace Aubrant.TestDataGenerator.Generators.Patterns
{
    public class UUIDPattern : BasePattern
    {
        public UUIDPattern() : base(
            name: "UUID",
            description: "Standard UUID (version 4) format",
            pattern: "{H8}-{H4}-{H4}-{H4}-{H12}",
            tokens: new List<Token>
            {
            new HexToken("H8", "First segment (8 hex digits)", 8, CaseOption.Lower),
            new HexToken("H4", "Middle segment (4 hex digits)", 4, CaseOption.Lower),
            new HexToken("H12", "Last segment (12 hex digits)", 12, CaseOption.Lower)
            })
        { }
    }
}