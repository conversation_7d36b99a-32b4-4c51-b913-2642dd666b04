using Aubrant.TestDataGenerator.Core.Patterns;
using System.Reflection;

namespace Aubrant.TestDataGenerator.Generators.Patterns
{
    /// <summary>
    /// Provides a registry for discovering and instantiating data generation patterns.
    /// </summary>
    public static class PatternRegistry
    {
        private static readonly Lazy<IReadOnlyDictionary<string, Type>> _patterns = new Lazy<IReadOnlyDictionary<string, Type>>(
            () =>
            {
                var basePatternType = typeof(BasePattern);
                return Assembly.GetExecutingAssembly().GetTypes()
                    .Where(t => t.IsClass && !t.IsAbstract && basePatternType.IsAssignableFrom(t) && t != typeof(RuntimePattern))
                    .Select(t =>
                    {
                        try
                        {
                            var instance = (BasePattern)Activator.CreateInstance(t)!;
                            return new { Type = t, Name = instance.Name };
                        }
                        catch
                        {
                            // Ignore types that cannot be instantiated with a parameterless constructor
                            return null;
                        }
                    })
                    .Where(x => x != null)
                    .ToDictionary(x => x.Name, x => x.Type, StringComparer.OrdinalIgnoreCase); // Case-insensitive matching
            });

        /// <summary>
        /// Gets a read-only dictionary of all registered patterns, keyed by their Name property.
        /// </summary>
        public static IReadOnlyDictionary<string, Type> Patterns => _patterns.Value;

        /// <summary>
        /// Tries to create an instance of a registered pattern by its name.
        /// </summary>
        /// <param name="patternName">The name of the pattern to create.</param>
        /// <param name="instance">When this method returns, contains the pattern instance if found; otherwise, null.</param>
        /// <returns>True if the pattern was found and instantiated; otherwise, false.</returns>
        public static bool TryCreatePattern(string patternName, out BasePattern? instance)
        {
            if (Patterns.TryGetValue(patternName, out var type))
            {
                try
                {
                    instance = (BasePattern)Activator.CreateInstance(type)!;
                    return true;
                }
                catch
                {
                    instance = null;
                    return false;
                }
            }

            instance = null;
            return false;
        }
    }
}
