﻿using System.Collections;

namespace Aubrant.TestDataGenerator.Core.Functions
{
    /// <summary>
    /// Represents a collection of Parameter objects, allowing retrieval by name.
    /// </summary>
    public class ParameterCollection : IEnumerable<Parameter>
    {
        // Internal dictionary to store parameters, using their name as the key for quick lookup.
        private readonly Dictionary<string, Parameter> _parameters;

        /// <summary>
        /// Gets the number of parameters contained in the collection.
        /// </summary>
        public int Count => _parameters.Count;

        /// <summary>
        /// Initializes a new instance of the <see cref="ParameterCollection"/> class.
        /// </summary>
        public ParameterCollection()
        {
            _parameters = new Dictionary<string, Parameter>(StringComparer.OrdinalIgnoreCase); // Use OrdinalIgnoreCase for case-insensitive name matching
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ParameterCollection"/> class with an initial set of parameters.
        /// </summary>
        /// <param name="parameters">An enumerable collection of parameters to add.</param>
        public ParameterCollection(IEnumerable<Parameter> parameters) : this()
        {
            if (parameters == null)
            {
                throw new ArgumentNullException(nameof(parameters));
            }

            foreach (var parameter in parameters)
            {
                Add(parameter); // Use the Add method to ensure uniqueness and proper dictionary population
            }
        }

        /// <summary>
        /// Gets a parameter by its name.
        /// </summary>
        /// <param name="name">The name of the parameter to get.</param>
        /// <returns>The <see cref="Parameter"/> with the specified name.</returns>
        /// <exception cref="KeyNotFoundException">Thrown when a parameter with the specified name is not found.</exception>
        public Parameter this[string name]
        {
            get
            {
                if (TryGet(name, out Parameter? parameter))
                {
                    return parameter!; // We know it's not null if TryGet returns true
                }

                throw new KeyNotFoundException($"Parameter with name '{name}' not found in the collection.");
            }
        }

        /// <summary>
        /// Adds a parameter to the collection.
        /// </summary>
        /// <param name="parameter">The parameter to add.</param>
        /// <exception cref="ArgumentNullException">Thrown if the parameter is null.</exception>
        /// <exception cref="ArgumentException">Thrown if a parameter with the same name already exists in the collection.</exception>
        public void Add(Parameter parameter)
        {
            if (parameter == null)
            {
                throw new ArgumentNullException(nameof(parameter));
            }

            if (_parameters.ContainsKey(parameter.Name))
            {
                throw new ArgumentException($"A parameter with the name '{parameter.Name}' already exists in the collection.", nameof(parameter));
            }

            _parameters.Add(parameter.Name, parameter);
        }

        /// <summary>
        /// Attempts to get a parameter by its name.
        /// </summary>
        /// <param name="name">The name of the parameter to get.</param>
        /// <param name="parameter">When this method returns, contains the parameter with the specified name, if found; otherwise, null.</param>
        /// <returns><c>true</c> if the parameter was found; otherwise, <c>false</c>.</returns>
        public bool TryGet(string name, out Parameter? parameter)
        {
            return _parameters.TryGetValue(name, out parameter);
        }

        /// <summary>
        /// Determines whether the collection contains a parameter with the specified name.
        /// </summary>
        /// <param name="name">The name of the parameter to locate.</param>
        /// <returns><c>true</c> if the collection contains a parameter with the specified name; otherwise, <c>false</c>.</returns>
        public bool Contains(string name)
        {
            return _parameters.ContainsKey(name);
        }

        /// <summary>
        /// Determines whether the collection contains a specific parameter instance.
        /// </summary>
        /// <param name="parameter">The parameter instance to locate.</param>
        /// <returns><c>true</c> if the collection contains the specified parameter instance; otherwise, <c>false</c>.</returns>
        public bool Contains(Parameter parameter)
        {
            if (parameter == null)
            {
                return false;
            }
            return _parameters.TryGetValue(parameter.Name, out var existingParameter) && ReferenceEquals(parameter, existingParameter);
        }

        /// <summary>
        /// Removes the parameter with the specified name from the collection.
        /// </summary>
        /// <param name="name">The name of the parameter to remove.</param>
        /// <returns><c>true</c> if the parameter was successfully removed; otherwise, <c>false</c>.</returns>
        public bool Remove(string name)
        {
            return _parameters.Remove(name);
        }

        /// <summary>
        /// Removes a specific parameter instance from the collection.
        /// </summary>
        /// <param name="parameter">The parameter instance to remove.</param>
        /// <returns><c>true</c> if the parameter was successfully removed; otherwise, <c>false</c>.</returns>
        public bool Remove(Parameter parameter)
        {
            if (parameter == null)
            {
                return false;
            }
            // Only remove if the parameter instance matches the one stored for that name
            if (_parameters.TryGetValue(parameter.Name, out var existingParameter) && ReferenceEquals(parameter, existingParameter))
            {
                return _parameters.Remove(parameter.Name);
            }
            return false;
        }

        /// <summary>
        /// Removes all parameters from the collection.
        /// </summary>
        public void Clear()
        {
            _parameters.Clear();
        }

        /// <summary>
        /// Returns an enumerator that iterates through the collection of parameters.
        /// </summary>
        /// <returns>An enumerator that can be used to iterate through the collection.</returns>
        public IEnumerator<Parameter> GetEnumerator()
        {
            return _parameters.Values.GetEnumerator();
        }

        /// <summary>
        /// Returns an enumerator that iterates through a collection.
        /// </summary>
        /// <returns>An <see cref="IEnumerator"/> object that can be used to iterate through the collection.</returns>
        IEnumerator IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }
    }
}
