using System.Collections.Generic;

namespace Aubrant.TestDataGenerator.Core.Utils.Models
{
    public class State
    {
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public List<City> Cities { get; set; } = new List<City>();
        public List<string> Counties { get; set; } = new List<string>();
        public List<string> PostalCodePatterns { get; set; } = new List<string>();
        public double? CenterLatitude { get; set; }
        public double? CenterLongitude { get; set; }
        public double? RadiusKm { get; set; }
    }
}
