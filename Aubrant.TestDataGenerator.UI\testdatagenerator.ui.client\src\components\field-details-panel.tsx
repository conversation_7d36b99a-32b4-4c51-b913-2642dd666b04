import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import type { Field, Entity } from "../types/database";

interface FieldDetailsPanelProps {
  selectedField: Field
  selectedEntity: Entity
}

export function FieldDetailsPanel({
  selectedField,
  selectedEntity,
}: FieldDetailsPanelProps) {
  return (
    <div className="space-y-4">
      <div>
        <h3 className="font-semibold text-base mb-2">Field Details</h3>
        <div className="space-y-2">
          <div>
            <label className="text-sm font-medium">Name</label>
            <p className="text-sm text-muted-foreground">
              {selectedField.Name}
            </p>
          </div>
          <div>
            <label className="text-sm font-medium">Type</label>
            <p className="text-sm text-muted-foreground">
              {selectedField.Type || selectedField.NativeType}
            </p>
          </div>
          <div>
            <label className="text-sm font-medium">Native Type</label>
            <p className="text-sm text-muted-foreground">
              {selectedField.NativeType}
            </p>
          </div>
          {selectedField.MaxLength && (
            <div>
              <label className="text-sm font-medium">Max Length</label>
              <p className="text-sm text-muted-foreground">
                {selectedField.MaxLength}
              </p>
            </div>
          )}
          <Separator />
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm">Primary Key</span>
              <Badge
                variant={selectedField.IsPrimaryKey ? "default" : "outline"}
              >
                {selectedField.IsPrimaryKey ? "Yes" : "No"}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Identity</span>
              <Badge
                variant={selectedField.IsIdentity ? "default" : "outline"}
              >
                {selectedField.IsIdentity ? "Yes" : "No"}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Nullable</span>
              <Badge
                variant={selectedField.IsNullable ? "default" : "outline"}
              >
                {selectedField.IsNullable ? "Yes" : "No"}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Unique</span>
              <Badge
                variant={selectedField.IsUnique ? "default" : "outline"}
              >
                {selectedField.IsUnique ? "Yes" : "No"}
              </Badge>
            </div>
          </div>
        </div>
      </div>
      <Separator />
      <div>
        <h4 className="font-medium mb-2">
          Table: {selectedEntity.Schema}.{selectedEntity.Name}
        </h4>
        <p className="text-sm text-muted-foreground">
          {selectedEntity.Fields.length} fields,{" "}
          {selectedEntity.Relationships.length} relationships
        </p>
      </div>
    </div>
  )
}
