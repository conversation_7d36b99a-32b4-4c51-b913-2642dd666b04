﻿using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Functions;
using Aubrant.TestDataGenerator.Core.Interfaces;
using Aubrant.TestDataGenerator.Core.Patterns;
using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.Data.Enums;
using Aubrant.TestDataGenerator.Data.ReferentialIntegrity;
using Aubrant.TestDataGenerator.Generators;
using Aubrant.TestDataGenerator.Generators.Functions;
using Aubrant.TestDataGenerator.Generators.Patterns;
using Microsoft.SemanticKernel;
using System.Data;
using System.Reflection;
using System.Text.Json;

namespace Aubrant.TestDataGenerator.SemanticAnalizer
{
    /// <summary>
    /// Enhanced semantic analyzer with relational integrity awareness and comprehensive generator support.
    /// Maintains API compatibility with the original SemanticAnalyzer while providing advanced relational features.
    /// </summary>
    public class SemanticAnalyzer
    {
        private readonly RelationalSemanticAnalyzer _relationalAnalyzer;
        private readonly ISemanticAnalyzerCache _cache;
        private readonly IDependencyGraphBuilder _dependencyGraphBuilder;

        /// <summary>
        /// Initializes a new instance of the SemanticAnalyzer with enhanced relational capabilities.
        /// Maintains compatibility with the original constructor signature.
        /// </summary>
        /// <param name="modelId">The AI model identifier (supports OpenAI models)</param>
        /// <param name="apiKey">The API key for the AI service</param>
        public SemanticAnalyzer(string modelId, string apiKey)
        {
            _cache = new InMemorySemanticAnalyzerCache();
            _dependencyGraphBuilder = new DependencyGraphBuilder();
            _relationalAnalyzer = new RelationalSemanticAnalyzer(modelId, apiKey, _cache, _dependencyGraphBuilder);
        }

        /// <summary>
        /// Analyzes the data source and suggests appropriate generators for all fields.
        /// Enhanced with relational integrity awareness and comprehensive generator support.
        /// Maintains compatibility with the original method signature.
        /// </summary>
        /// <param name="dataSource">The data source to analyze</param>
        public async Task Analyze(DataSource dataSource)
        {
            // Use enhanced relational analysis with default options for backward compatibility
            var options = new RelationalAnalysisOptions
            {
                BatchSize = 8,
                EnableRelationalHeuristics = false,  // Use AI-only approach for best results
                InferForeignKeysFromNaming = true,   // Enable smart FK detection
                ValidateReferences = true
            };

            var result = await _relationalAnalyzer.AnalyzeAsync(dataSource, options);

            if (!result.Success)
            {
                throw new InvalidOperationException($"Semantic analysis failed: {result.ErrorMessage}");
            }
        }
    }

    /// <summary>
    /// Enhanced relational semantic analyzer with comprehensive generator support and dependency awareness.
    /// This is the core implementation that powers the enhanced SemanticAnalyzer.
    /// </summary>
    internal class RelationalSemanticAnalyzer
    {
        private readonly Kernel _kernel;
        private readonly ISemanticAnalyzerCache _cache;
        private readonly IDependencyGraphBuilder _dependencyGraphBuilder;

        public RelationalSemanticAnalyzer(string modelId, string apiKey, ISemanticAnalyzerCache cache, IDependencyGraphBuilder dependencyGraphBuilder)
        {
            var builder = Kernel.CreateBuilder();
            builder.AddOpenAIChatCompletion(modelId, apiKey);
            _kernel = builder.Build();
            _cache = cache;
            _dependencyGraphBuilder = dependencyGraphBuilder;
        }

        public async Task<RelationalAnalysisResult> AnalyzeAsync(DataSource dataSource, RelationalAnalysisOptions options)
        {
            var result = new RelationalAnalysisResult();

            try
            {
                // Step 1: Build dependency graph for relational awareness
                var dependencyGraph = _dependencyGraphBuilder.BuildDependencyGraph(dataSource);
                result.DependencyGraph = dependencyGraph;
                result.RecommendedGenerationOrder = dependencyGraph.TopologicalOrder.Select(n => n.Entity.Name!).ToList();



                // Step 2: Apply enhanced heuristic analysis with relational awareness (if enabled)
                if (options.EnableRelationalHeuristics)
                {
                    ApplyRelationalHeuristics(dataSource, dependencyGraph, result);
                }

                // Step 3: Apply AI-powered semantic analysis with full generator knowledge
                await AnalyzeFieldsWithRelationalContext(dataSource, dependencyGraph, options, result);

                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        private void ApplyRelationalHeuristics(DataSource dataSource, DependencyGraph dependencyGraph, RelationalAnalysisResult result)
        {
            foreach (var entity in dataSource.Entities)
            {
                foreach (var field in entity.Fields)
                {
                    // Primary key auto-increment detection
                    if (field.IsPrimaryKey && field.IsIdentity)
                    {
                        field.FieldSetting = new FieldSetting
                        {
                            Type = GeneratorType.AutoIncrement,
                            Name = "AutoIncrement",
                            Settings = "{}"
                        };
                        result.HeuristicMappings++;
                        continue;
                    }

                    // Foreign key detection
                    if (IsLikelyForeignKey(field, dataSource))
                    {
                        var targetEntity = InferTargetEntity(field, dataSource);
                        if (targetEntity != null)
                        {
                            field.FieldSetting = new FieldSetting
                            {
                                Type = GeneratorType.Reference,
                                Name = "Reference",
                                Settings = JsonSerializer.Serialize(new
                                {
                                    TargetEntity = targetEntity.Name,
                                    TargetField = "Id"
                                })
                            };
                            result.HeuristicMappings++;
                            result.ForeignKeyMappings++;
                        }
                    }
                }
            }
        }

        private bool IsLikelyForeignKey(Field field, DataSource dataSource)
        {
            var fieldName = field.Name?.ToLowerInvariant() ?? "";
            return fieldName.EndsWith("id") && !field.IsPrimaryKey;
        }

        private Entity? InferTargetEntity(Field field, DataSource dataSource)
        {
            var fieldName = field.Name?.ToLowerInvariant() ?? "";
            if (fieldName.EndsWith("id"))
            {
                var entityPrefix = fieldName.Substring(0, fieldName.Length - 2);
                return dataSource.Entities.FirstOrDefault(e =>
                    e.Name?.ToLowerInvariant() == entityPrefix ||
                    e.Name?.ToLowerInvariant() == entityPrefix + "s" ||
                    e.Name?.ToLowerInvariant() == entityPrefix.TrimEnd('s'));
            }
            return null;
        }

        private async Task AnalyzeFieldsWithRelationalContext(DataSource dataSource, DependencyGraph dependencyGraph, RelationalAnalysisOptions options, RelationalAnalysisResult result)
        {
            var fieldsNeedingAI = new List<(Field Field, Entity Entity, EntityNode? Node)>();

            // Collect ALL fields for AI analysis (not just unassigned ones)
            foreach (var entity in dataSource.Entities)
            {
                var node = dependencyGraph.Nodes.GetValueOrDefault(entity.Name!);
                foreach (var field in entity.Fields)
                {
                    // Process ALL fields with AI, regardless of existing assignments
                    fieldsNeedingAI.Add((field, entity, node));
                }
            }

            if (!fieldsNeedingAI.Any()) return;

            // Group by entity and process each entity separately (like original approach)
            var entitiesWithFields = fieldsNeedingAI.GroupBy(item => item.Entity).ToList();

            foreach (var entityGroup in entitiesWithFields)
            {
                var entity = entityGroup.Key;
                var entityFields = entityGroup.ToList();

                // Process fields in batches within the same entity
                var fieldBatches = entityFields.Chunk(options.BatchSize);

                foreach (var batch in fieldBatches)
                {
                    await ProcessRelationalBatch(batch, dependencyGraph, options, result);
                }
            }

            // SAFETY NET: Only assign generators to truly unassigned fields (should be rare now)
            await EnsureAllFieldsHaveGenerators(dataSource, dependencyGraph, result);
        }

        private async Task EnsureAllFieldsHaveGenerators(DataSource dataSource, DependencyGraph dependencyGraph, RelationalAnalysisResult result)
        {
            var unassignedFields = new List<(Field Field, Entity Entity)>();

            foreach (var entity in dataSource.Entities)
            {
                foreach (var field in entity.Fields)
                {
                    if (field.FieldSetting == null)
                    {
                        unassignedFields.Add((field, entity));
                    }
                }
            }

            if (unassignedFields.Any())
            {
                foreach (var (field, entity) in unassignedFields)
                {
                    // Use AI to intelligently select the best generator
                    await AssignGeneratorUsingAI(field, entity, dependencyGraph, result);
                }
            }
        }

        private async Task AssignGeneratorUsingAI(Field field, Entity entity, DependencyGraph dependencyGraph, RelationalAnalysisResult result)
        {
            try
            {
                var prompt = CreateIntelligentGeneratorSelectionPrompt(field, entity, dependencyGraph);
                var kernelFunction = _kernel.CreateFunctionFromPrompt(prompt);
                var response = await kernelFunction.InvokeAsync(_kernel);
                var responseText = response.ToString();

                result.ApiCalls++;

                // Parse the AI response to get generator selection
                var generatorSelection = ParseGeneratorSelectionResponse(responseText);

                if (generatorSelection != null)
                {
                    field.FieldSetting = new FieldSetting
                    {
                        Type = Enum.Parse<GeneratorType>(generatorSelection.Type),
                        Name = generatorSelection.Name ?? generatorSelection.Type,
                        Settings = JsonSerializer.Serialize(generatorSelection.Settings)
                    };
                    result.AiMappings++;
                }
                else
                {
                    // Fallback to AI generator if parsing fails
                    field.FieldSetting = new FieldSetting
                    {
                        Type = GeneratorType.AI,
                        Name = "AI",
                        Settings = JsonSerializer.Serialize(new
                        {
                            Prompt = $"Generate realistic data for {field.Name} in {entity.Name}",
                            MaxTokens = 100
                        })
                    };
                    result.AiMappings++;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error in AI generator selection for {field.Name}: {ex.Message}");

                // Fallback to AI generator
                field.FieldSetting = new FieldSetting
                {
                    Type = GeneratorType.AI,
                    Name = "AI",
                    Settings = JsonSerializer.Serialize(new
                    {
                        Prompt = $"Generate realistic data for {field.Name} in {entity.Name}",
                        MaxTokens = 100
                    })
                };
                result.AiMappings++;
            }
        }

        private string CreateIntelligentGeneratorSelectionPrompt(Field field, Entity entity, DependencyGraph dependencyGraph)
        {
            var sampleData = GetSampleDataForField(field, entity);
            dependencyGraph.Nodes.TryGetValue(entity.Name!, out var entityNode);

            return $@"
## Task: Intelligent Generator Selection

You are an expert data generation system. Analyze the field information and select the most appropriate generator from the available options.

## Field Information:
- **Field Name**: {field.Name}
- **Data Type**: {field.Type} ({field.NativeType})
- **Max Length**: {field.MaxLength?.ToString() ?? "N/A"}
- **Nullable**: {field.IsNullable}
- **Primary Key**: {field.IsPrimaryKey}
- **Identity**: {field.IsIdentity}
- **Unique**: {field.IsUnique}
- **Sample Data**: {sampleData}

## Entity Context:
- **Entity**: {entity.Name} (Schema: {entity.Schema})
- **Related Entities**: {string.Join(", ", entityNode?.Dependencies ?? new HashSet<string>())}
- **Referenced By**: {string.Join(", ", entityNode?.Dependents ?? new HashSet<string>())}

## Available Generators:
{GetGeneratorsAsJson()}

## Your Task:
Analyze the field characteristics, sample data patterns, and entity relationships to select the BEST generator. Consider:
- Field name semantics and patterns
- Data type compatibility
- Sample data patterns and formats
- Relational context (foreign keys, references)
- Realistic data generation requirements

## Response Format:
Return a JSON object with this exact structure:
```json
{{
  ""Type"": ""GeneratorType"",
  ""Name"": ""GeneratorName"",
  ""Settings"": {{
    // Appropriate settings for the selected generator
    // Examples:
    // AutoIncrement: {{""Start"": 1, ""Step"": 1}}
    // AI: {{""Prompt"": ""specific prompt"", ""MaxTokens"": 100}}
    // Reference: {{""TargetEntity"": ""EntityName"", ""TargetField"": ""FieldName""}}
    // Random: {{""Min"": 1, ""Max"": 100, ""DataType"": ""Integer""}}
    // Pattern: {{""Pattern"": ""###-###-####""}}
    // Choice: {{""Options"": [{{""Value"": ""option1"", ""Probability"": 0.5}}]}}
  }}
}}
```

Select the generator that will produce the most realistic and appropriate data for this field.";
        }

        private SuggestedGeneratorOutput? ParseGeneratorSelectionResponse(string? responseText)
        {
            if (string.IsNullOrEmpty(responseText))
                return null;

            try
            {
                // Clean response - handle multiple possible formats
                var cleanedResponse = responseText.Trim();

                // Handle markdown code blocks
                if (cleanedResponse.StartsWith("```json") && cleanedResponse.EndsWith("```"))
                    cleanedResponse = cleanedResponse.Substring(7, cleanedResponse.Length - 10).Trim();
                else if (cleanedResponse.StartsWith("```") && cleanedResponse.EndsWith("```"))
                    cleanedResponse = cleanedResponse.Substring(3, cleanedResponse.Length - 6).Trim();

                // Try to extract JSON from text that might have explanatory text
                var jsonStart = cleanedResponse.IndexOf('{');
                var jsonEnd = cleanedResponse.LastIndexOf('}');
                if (jsonStart >= 0 && jsonEnd > jsonStart)
                {
                    cleanedResponse = cleanedResponse.Substring(jsonStart, jsonEnd - jsonStart + 1);
                }

                // Deserialize to SuggestedGeneratorOutput
                var result = JsonSerializer.Deserialize<SuggestedGeneratorOutput>(cleanedResponse, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error parsing generator selection response: {ex.Message}");
                Console.WriteLine($"Response: {responseText}");
                return null;
            }
        }

        private string GetSchemaContextForBatch(IEnumerable<(Field Field, Entity Entity, EntityNode? Node)> batch, DependencyGraph dependencyGraph)
        {
            var currentEntity = batch.First().Entity;
            var relatedEntities = new HashSet<string>();

            // Get all entities that this entity depends on or that depend on it
            if (dependencyGraph.Nodes.TryGetValue(currentEntity.Name!, out var currentNode))
            {
                relatedEntities.UnionWith(currentNode.Dependencies);
                relatedEntities.UnionWith(currentNode.Dependents);
            }

            // Also check field names for potential foreign key relationships
            foreach (var (field, _, _) in batch)
            {
                var fieldName = field.Name?.ToLowerInvariant() ?? "";

                // Look for foreign key patterns like CustomerId, EmployeeId, etc.
                if (fieldName.EndsWith("id") && fieldName.Length > 2)
                {
                    var potentialEntityName = fieldName.Substring(0, fieldName.Length - 2);

                    // Find matching entity in the dependency graph
                    var matchingEntity = dependencyGraph.Nodes.Keys
                        .FirstOrDefault(entityName =>
                            entityName.Equals(potentialEntityName, StringComparison.OrdinalIgnoreCase));

                    if (!string.IsNullOrEmpty(matchingEntity))
                    {
                        relatedEntities.Add(matchingEntity);
                    }
                }
            }

            // Build schema context for related entities
            var schemaContext = new List<object>();

            foreach (var entityName in relatedEntities)
            {
                if (dependencyGraph.Nodes.TryGetValue(entityName, out var entityNode) && entityNode.Entity != null)
                {
                    var entityInfo = new
                    {
                        EntityName = entityName,
                        PrimaryKeys = entityNode.Entity.Fields
                            .Where(f => f.IsPrimaryKey)
                            .Select(f => new { f.Name, f.Type, f.NativeType })
                            .ToList(),
                        AllFields = entityNode.Entity.Fields
                            .Select(f => new { f.Name, f.Type, f.NativeType, f.IsPrimaryKey, f.IsIdentity })
                            .ToList()
                    };

                    schemaContext.Add(entityInfo);
                }
            }

            return JsonSerializer.Serialize(schemaContext, new JsonSerializerOptions { WriteIndented = true });
        }

        private async Task ProcessRelationalBatch(IEnumerable<(Field Field, Entity Entity, EntityNode? Node)> batch, DependencyGraph dependencyGraph, RelationalAnalysisOptions options, RelationalAnalysisResult result)
        {
            var entity = batch.First().Entity;
            var fieldNames = string.Join(", ", batch.Select(b => b.Field.Name));

            try
            {
                var prompt = CreateRelationalBatchPrompt(batch, dependencyGraph);
                var kernelFunction = _kernel.CreateFunctionFromPrompt(prompt);
                var response = await kernelFunction.InvokeAsync(_kernel);
                var responseText = response.ToString();

                result.ApiCalls++;
                await ParseRelationalBatchResponse(responseText, batch, dependencyGraph, result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error processing relational batch for {entity.Name}: {ex.Message}");

                // Apply AI fallback for failed batch
                foreach (var (field, _, _) in batch)
                {
                    if (field.FieldSetting == null)
                    {
                        field.FieldSetting = new FieldSetting
                        {
                            Type = GeneratorType.AI,
                            Name = "AI",
                            Settings = JsonSerializer.Serialize(new { Prompt = $"Generate realistic {field.Name}" })
                        };
                        result.AiMappings++;
                    }
                }
            }
        }

        private string CreateRelationalBatchPrompt(IEnumerable<(Field Field, Entity Entity, EntityNode? Node)> batch, DependencyGraph dependencyGraph)
        {
            // Get the entity (all fields in batch should be from same entity now)
            var entity = batch.First().Entity;
            var entityNode = batch.First().Node;

            var fieldsInfo = batch.Select(item => new
            {
                FieldName = item.Field.Name,
                DataType = item.Field.Type.ToString(),
                NativeType = item.Field.NativeType,
                MaxLength = item.Field.MaxLength?.ToString() ?? "",
                IsNullable = item.Field.IsNullable.ToString(),
                IsPrimaryKey = item.Field.IsPrimaryKey.ToString(),
                IsIdentity = item.Field.IsIdentity.ToString(),
                IsUnique = item.Field.IsUnique.ToString(),
                SampleData = GetSampleDataForField(item.Field, item.Entity)
            });

            var fieldsJson = JsonSerializer.Serialize(fieldsInfo, new JsonSerializerOptions { WriteIndented = true });

            return $@"
You are an expert database field analyzer. You MUST analyze ALL fields provided and return a generator assignment for EVERY SINGLE FIELD.

## CRITICAL REQUIREMENT:
- You MUST return exactly {batch.Count()} field assignments
- Every field in the input MUST have a corresponding entry in your response
- NO FIELD can be skipped or omitted

## Available Generators:
{GetGeneratorsAsJson()}

## Entity Context:
Entity: {entity.Name} (Schema: {entity.Schema})
Related Entities: {string.Join(", ", entityNode?.Dependencies ?? new HashSet<string>())}
Referenced By: {string.Join(", ", entityNode?.Dependents ?? new HashSet<string>())}

## Complete Database Schema Context:
{GetSchemaContextForBatch(batch, dependencyGraph)}

## Fields to Analyze (ALL {batch.Count()} fields):
{fieldsJson}

## Your Task:
Analyze each field intelligently considering:
- Field name, data type, and sample data
- Available generator options and their capabilities
- Relational context (primary keys, foreign keys, etc.)
- Most appropriate generator for realistic data generation

Select the best generator from the available options and provide detailed, realistic settings.

## Response Format:
Return a JSON array with detailed settings for each field:

[
  {{
    ""FieldName"": ""field1"",
    ""Type"": ""GeneratorType"",
    ""Name"": ""GeneratorName"",
    ""Settings"": {{
      // Include appropriate settings based on the generator you choose:
      // AutoIncrement: {{""Start"": 1, ""Step"": 1}}
      // Function: {{}} or specific parameters
      // AI: {{""Prompt"": ""detailed prompt"", ""MaxTokens"": 100}}
      // Reference: {{""TargetEntity"": ""EntityName"", ""TargetField"": ""FieldName""}}
      // Random: {{""Min"": 1, ""Max"": 100, ""DataType"": ""Integer""}}
      // Pattern: {{""Pattern"": ""###-###-####""}}
      // Choice: {{""Options"": [{{""Value"": ""option1"", ""Probability"": 0.5}}]}}
    }}
  }}
]

MANDATORY: Your response must contain exactly {batch.Count()} field assignments. Double-check that every field from the input appears in your output.

Analysis Result:";
        }

        private string GetSampleDataForField(Field field, Entity entity)
        {
            if (field.Name == null) return "";

            return string.Join(",", entity.Data.AsEnumerable()
                .Select(row => (row[field.Name] as object)?.ToString() ?? "")
                .Where(v => !string.IsNullOrEmpty(v))
                .Take(3));
        }

        private string GetGeneratorsAsJson()
        {
            var generators = new List<GeneratorInfo>();

            // Get functions from the registry (BaseFunction implementations)
            foreach (var func in FunctionRegistry.Functions)
            {
                var dummyContext = new DataGeneratorContext { Row = new DataTable().NewRow() };
                if (FunctionRegistry.TryCreateFunction(func.Key, dummyContext, out var instance))
                {
                    generators.Add(new GeneratorInfo
                    {
                        Name = instance!.Name,
                        Description = instance.Description,
                        ReturnType = instance.ReturnType.ToString(),
                        Summary = $"Function generator: {instance.Name}",
                        Parameters = instance.Parameters.Select(p => new ParameterInfo
                        {
                            Name = p.Name,
                            Type = p.Type.ToString(),
                            IsOptional = false, // Functions typically have required parameters
                            DefaultValue = p.Value?.ToString(),
                            Summary = p.Description
                        }).ToList()
                    });
                }
            }

            //Patterns
            var assembly = Assembly.GetAssembly(typeof(RuntimePattern));
            if (assembly != null)
            {
                var generatorTypes = assembly
                                    .GetTypes()
                                    .Where(t => t.IsClass && !t.IsAbstract && typeof(BasePattern).IsAssignableFrom(t))
                                    .ToList();

                foreach (var type in generatorTypes)
                {
                    try
                    {
                        var instance = (BasePattern)Activator.CreateInstance(type)!;
                        generators.Add(new GeneratorInfo
                        {
                            Name = instance!.Name,
                            Description = instance.Description,
                            ReturnType = nameof(DataType.String),
                            Summary = $"Pattern generator: {instance.Name} - Pattern: {instance.Pattern}",
                            Parameters = new List<ParameterInfo>
                            {
                                new ParameterInfo
                                {
                                    Name = "Pattern",
                                    Type = "String",
                                    IsOptional = false,
                                    DefaultValue = instance.Pattern,
                                    Summary = "The pattern template for data generation"
                                }
                            }
                        });
                    }
                    catch (Exception)
                    {
                    }
                }
            }

            // Get other IGenerator implementations (not BaseFunction)
            assembly = Assembly.GetAssembly(typeof(RandomGenerator)); // Get the assembly where IGenerator is defined
            if (assembly != null)
            {
                var generatorTypes = assembly
                                    .GetTypes()
                                    .Where(t => t.IsClass && !t.IsAbstract && typeof(IGenerator).IsAssignableFrom(t) && !typeof(BaseFunction).IsAssignableFrom(t) && !typeof(BasePattern).IsAssignableFrom(t))
                                    .ToList();

                foreach (var type in generatorTypes)
                {
                    try
                    {
                        var instance = (IGenerator)Activator.CreateInstance(type)!;
                        var parameters = GetGeneratorParameters(type);

                        generators.Add(new GeneratorInfo
                        {
                            Name = instance.Name,
                            Description = instance.Description,
                            ReturnType = instance.ReturnType.ToString(),
                            Summary = $"Generator: {instance.Name}",
                            Parameters = parameters
                        });
                    }
                    catch (Exception)
                    {
                    }
                }
            }

            return JsonSerializer.Serialize(new { Generators = generators }, new JsonSerializerOptions { WriteIndented = true });
        }

        private async Task ParseRelationalBatchResponse(
            string? responseText,
            IEnumerable<(Field Field, Entity Entity, EntityNode? Node)> batch,
            DependencyGraph dependencyGraph,
            RelationalAnalysisResult result)
        {
            if (string.IsNullOrEmpty(responseText))
            {
                Console.WriteLine("❌ Empty response from AI - applying AI fallback to all fields");

                // Apply AI fallback for empty response
                foreach (var (field, entity, _) in batch)
                {
                    if (field.FieldSetting == null)
                    {
                        // Use AI to intelligently select the best generator as fallback
                        await AssignGeneratorUsingAI(field, entity, dependencyGraph, result);
                    }
                }
                return;
            }

            // Clean response - handle multiple possible formats (like original approach)
            var cleanedResponse = responseText.Trim();

            // Handle markdown code blocks
            if (cleanedResponse.StartsWith("```json") && cleanedResponse.EndsWith("```"))
                cleanedResponse = cleanedResponse.Substring(7, cleanedResponse.Length - 10).Trim();
            else if (cleanedResponse.StartsWith("```") && cleanedResponse.EndsWith("```"))
                cleanedResponse = cleanedResponse.Substring(3, cleanedResponse.Length - 6).Trim();

            // Try to extract JSON from text that might have explanatory text
            var jsonStart = cleanedResponse.IndexOf('[');
            var jsonEnd = cleanedResponse.LastIndexOf(']');
            if (jsonStart >= 0 && jsonEnd > jsonStart)
            {
                cleanedResponse = cleanedResponse.Substring(jsonStart, jsonEnd - jsonStart + 1);
            }

            try
            {
                var batchResults = JsonSerializer.Deserialize<BatchFieldResult[]>(cleanedResponse);
                if (batchResults == null)
                {
                    Console.WriteLine("❌ Failed to deserialize JSON - result is null");
                    return;
                }

                // Create field dictionary using just field names (no duplicates within same entity)
                var fieldDict = batch.ToDictionary(
                    item => item.Field.Name ?? "",
                    item => item.Field
                );

                // VALIDATION: Check if AI returned all fields
                var expectedFieldCount = batch.Count();
                var returnedFieldCount = batchResults.Length;

                if (returnedFieldCount != expectedFieldCount)
                {
                    Console.WriteLine($"⚠️ AI returned {returnedFieldCount} fields but expected {expectedFieldCount}");
                }

                foreach (var batchResult in batchResults)
                {
                    var fieldName = batchResult.FieldName ?? "";

                    if (fieldDict.TryGetValue(fieldName, out var field))
                    {
                        // Enhanced settings processing with detailed configuration
                        var settingsJson = "{}";
                        if (batchResult.Settings != null)
                        {
                            try
                            {
                                // Ensure settings are properly formatted
                                settingsJson = JsonSerializer.Serialize(batchResult.Settings, new JsonSerializerOptions
                                {
                                    WriteIndented = true,
                                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                                });
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"⚠️ Settings serialization error for {fieldName}: {ex.Message}");
                                settingsJson = "{}";
                            }
                        }

                        // Parse generator type with fallback
                        var generatorType = GeneratorType.AI; // Default fallback
                        if (!string.IsNullOrEmpty(batchResult.Type))
                        {
                            if (Enum.TryParse<GeneratorType>(batchResult.Type, true, out var parsedType))
                            {
                                generatorType = parsedType;
                            }
                            else
                            {
                                Console.WriteLine($"⚠️ Unknown generator type '{batchResult.Type}' for {fieldName}, using AI fallback");
                            }
                        }

                        field.FieldSetting = new FieldSetting
                        {
                            Type = generatorType,
                            Name = batchResult.Name ?? generatorType.ToString(),
                            Settings = settingsJson
                        };

                        result.AiMappings++;

                        // Track specific mapping types
                        if (field.FieldSetting.Type == GeneratorType.Reference)
                        {
                            result.AiDetectedForeignKeys++;
                        }
                    }
                    else
                    {
                        Console.WriteLine($"❌ Field not found in batch: {fieldName}");
                    }
                }
            }
            catch (JsonException ex)
            {
                Console.WriteLine($"❌ JSON parsing error: {ex.Message}");
                // Try fallback parsing like the original approach
                ProcessIndividualFieldsFromBatchResponse(responseText, batch);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Unexpected error: {ex.Message}");
            }
        }

        private void ProcessIndividualFieldsFromBatchResponse(string responseText, IEnumerable<(Field Field, Entity Entity, EntityNode? Node)> batch)
        {
            // Attempt to extract individual JSON objects from malformed batch response
            // This is a fallback when the AI doesn't return proper JSON array format


            foreach (var (field, entity, node) in batch)
            {
                try
                {
                    // Simple pattern matching to find field-specific responses
                    var fieldPattern = $"\"FieldName\":\\s*\"{field.Name}\"";
                    var regex = new System.Text.RegularExpressions.Regex(fieldPattern);
                    var match = regex.Match(responseText);

                    if (match.Success)
                    {
                        // Try to extract the JSON object for this field
                        var startIndex = responseText.LastIndexOf('{', match.Index);
                        var endIndex = responseText.IndexOf('}', match.Index);

                        if (startIndex >= 0 && endIndex > startIndex)
                        {
                            var fieldJson = responseText.Substring(startIndex, endIndex - startIndex + 1);


                            var result = JsonSerializer.Deserialize<BatchFieldResult>(fieldJson);
                            if (result != null)
                            {
                                field.FieldSetting = new FieldSetting
                                {
                                    Type = Enum.Parse<GeneratorType>(result.Type ?? "Random", true),
                                    Name = result.Name,
                                    Settings = JsonSerializer.Serialize(result.Settings ?? new object())
                                };

                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Fallback failed for {field.Name}: {ex.Message}");
                }
            }
        }

        private object GetTokenProperties(Token token)
        {
            return token switch
            {
                DigitsToken digitsToken => new
                {
                    Type = "Digits",
                    digitsToken.Name,
                    digitsToken.Description,
                    digitsToken.Length,
                    Ranges = digitsToken.Ranges?.Select(r => new { r.Min, r.Max }).ToList()
                },
                ChoiceToken choiceToken => new
                {
                    Type = "Choice",
                    choiceToken.Name,
                    choiceToken.Description,
                    choiceToken.Options
                },
                HexToken hexToken => new
                {
                    Type = "Hexadecimal",
                    hexToken.Name,
                    hexToken.Description,
                    hexToken.Length,
                    hexToken.Case
                },
                AlphaNumToken alphaNumToken => new
                {
                    Type = "Alphanumeric",
                    alphaNumToken.Name,
                    alphaNumToken.Description,
                    alphaNumToken.Length
                },
                LettersToken lettersToken => new
                {
                    Type = "Letters",
                    lettersToken.Name,
                    lettersToken.Description,
                    lettersToken.Length
                },
                OptionalToken optionalToken => new
                {
                    Type = "Optional",
                    optionalToken.Name,
                    optionalToken.Description,
                    optionalToken.Pattern,
                    InnerToken = GetTokenProperties(optionalToken.InnerToken)
                },
                _ => new
                {
                    token.Name,
                    token.Description
                }
            };
        }

        private List<ParameterInfo> GetGeneratorParameters(Type type)
        {
            var excludedPropertyNames = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                "Name",
                "Description",
                "ReturnType"
            };

            return type.GetProperties()
                       .Where(p => !excludedPropertyNames.Contains(p.Name))
                       .Select(p => new ParameterInfo
                       {
                           Name = p.Name,
                           Type = p.PropertyType.Name,
                           IsOptional = p.PropertyType.IsGenericType && p.PropertyType.GetGenericTypeDefinition() == typeof(Nullable<>),
                           DefaultValue = null,
                           Summary = p.GetCustomAttribute<System.ComponentModel.DescriptionAttribute>()?.Description ?? $"Parameter for {p.Name}"
                       })
                       .ToList();
        }

        private object GetProperties(Type type)
        {
            var excludedPropertyNames = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                "Name",
                "Description",
                "ReturnType"
            };

            return type.GetProperties()
                       .Where(p => !excludedPropertyNames.Contains(p.Name))
                       .ToDictionary<PropertyInfo, string, object>(p => p.Name, p =>
                       {
                           if (p.PropertyType.IsGenericType && p.PropertyType.GetGenericTypeDefinition() == typeof(List<>))
                           {
                               var itemType = p.PropertyType.GetGenericArguments()[0];
                               return new
                               {
                                   Type = "array",
                                   Description = p.GetCustomAttribute<System.ComponentModel.DescriptionAttribute>()?.Description,
                                   Items = new
                                   {
                                       Type = "object",
                                       Properties = GetProperties(itemType)
                                   }
                               };
                           }
                           else if (!IsSimpleType(p.PropertyType))
                           {
                               return new
                               {
                                   Type = "object",
                                   Description = p.GetCustomAttribute<System.ComponentModel.DescriptionAttribute>()?.Description,
                                   Properties = GetProperties(p.PropertyType)
                               };
                           }
                           else
                           {
                               return new
                               {
                                   Type = p.PropertyType.Name,
                                   Description = p.GetCustomAttribute<System.ComponentModel.DescriptionAttribute>()?.Description
                               };
                           }
                       }, StringComparer.OrdinalIgnoreCase);
        }

        private bool IsSimpleType(Type type)
        {
            return type.IsPrimitive || type.IsEnum || type == typeof(string) || type == typeof(DataValue) || type == typeof(DateTime) || type == typeof(decimal);
        }
    }

    // Data models for batch processing (maintains compatibility with original)
    public class BatchFieldResult
    {
        public string? FieldName { get; set; }
        public string? Type { get; set; }
        public string? Name { get; set; }
        public object? Settings { get; set; }
    }

    /// <summary>
    /// Configuration options for relational semantic analysis
    /// </summary>
    public class RelationalAnalysisOptions
    {
        /// <summary>
        /// Number of fields to process in each batch
        /// </summary>
        public int BatchSize { get; set; } = 8;

        /// <summary>
        /// Whether to enable relational heuristics for basic field detection
        /// </summary>
        public bool EnableRelationalHeuristics { get; set; } = false;

        /// <summary>
        /// Whether to infer foreign keys from naming conventions
        /// </summary>
        public bool InferForeignKeysFromNaming { get; set; } = true;

        /// <summary>
        /// Whether to validate reference integrity
        /// </summary>
        public bool ValidateReferences { get; set; } = true;
    }

    /// <summary>
    /// Results from relational semantic analysis
    /// </summary>
    public class RelationalAnalysisResult
    {
        /// <summary>
        /// Whether the analysis completed successfully
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Error message if analysis failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Number of foreign key mappings detected
        /// </summary>
        public int ForeignKeyMappings { get; set; }

        /// <summary>
        /// Number of inferred foreign key mappings
        /// </summary>
        public int InferredForeignKeyMappings { get; set; }

        /// <summary>
        /// Number of AI-generated mappings
        /// </summary>
        public int AiMappings { get; set; }

        /// <summary>
        /// Number of heuristic-based mappings
        /// </summary>
        public int HeuristicMappings { get; set; }

        /// <summary>
        /// Number of foreign keys detected by AI
        /// </summary>
        public int AiDetectedForeignKeys { get; set; }

        /// <summary>
        /// Number of API calls made during analysis
        /// </summary>
        public int ApiCalls { get; set; }

        /// <summary>
        /// Dependency graph for the data source
        /// </summary>
        public DependencyGraph? DependencyGraph { get; set; }

        /// <summary>
        /// Recommended order for data generation
        /// </summary>
        public List<string> RecommendedGenerationOrder { get; set; } = new();
    }
}