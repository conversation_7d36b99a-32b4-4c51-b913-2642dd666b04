﻿using Aubrant.TestDataGenerator.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Aubrant.TestDataGenerator.SemanticAnalizer
{ /// <summary>
  /// Interface for caching semantic analysis results to improve performance
  /// </summary>
    public interface ISemanticAnalyzerCache
    {
        /// <summary>
        /// Gets cached analysis result for a field signature
        /// </summary>
        /// <param name="fieldSignature">Unique signature identifying the field characteristics</param>
        /// <returns>Cached field setting or null if not found</returns>
        Task<FieldSetting?> GetCachedResultAsync(string fieldSignature);

        /// <summary>
        /// Stores analysis result in cache
        /// </summary>
        /// <param name="fieldSignature">Unique signature identifying the field characteristics</param>
        /// <param name="fieldSetting">The analysis result to cache</param>
        Task SetCachedResultAsync(string fieldSignature, FieldSetting fieldSetting);

        /// <summary>
        /// Gets cached batch analysis result
        /// </summary>
        /// <param name="batchSignature">Unique signature for the batch of fields</param>
        /// <returns>Cached batch results or null if not found</returns>
        Task<Dictionary<string, FieldSetting>?> GetCachedBatchResultAsync(string batchSignature);

        /// <summary>
        /// Stores batch analysis result in cache
        /// </summary>
        /// <param name="batchSignature">Unique signature for the batch of fields</param>
        /// <param name="results">The batch analysis results to cache</param>
        Task SetCachedBatchResultAsync(string batchSignature, Dictionary<string, FieldSetting> results);

        /// <summary>
        /// Clears all cached results
        /// </summary>
        Task ClearCacheAsync();
    }
}
