﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Aubrant.TestDataGenerator.Data.ReferentialIntegrity
{
    /// <summary>
    /// Interface for building dependency graphs from database schema relationships
    /// </summary>
    public interface IDependencyGraphBuilder
    {
        /// <summary>
        /// Builds a dependency graph from the data source schema
        /// </summary>
        /// <param name="dataSource">The data source containing entities and relationships</param>
        /// <returns>A dependency graph representing table generation order</returns>
        DependencyGraph BuildDependencyGraph(DataSource dataSource);

        /// <summary>
        /// Validates the dependency graph for circular references and other issues
        /// </summary>
        /// <param name="graph">The dependency graph to validate</param>
        /// <returns>Validation result with any issues found</returns>
        DependencyGraphValidationResult ValidateGraph(DependencyGraph graph);
    }

    /// <summary>
    /// Represents a dependency graph for table generation ordering
    /// </summary>
    public class DependencyGraph
    {
        /// <summary>
        /// Gets the entities in topological order (dependencies first)
        /// </summary>
        public List<EntityNode> TopologicalOrder { get; set; } = new();

        /// <summary>
        /// Gets all entity nodes in the graph
        /// </summary>
        public Dictionary<string, EntityNode> Nodes { get; set; } = new();

        /// <summary>
        /// Gets circular reference groups that need special handling
        /// </summary>
        public List<CircularReferenceGroup> CircularReferences { get; set; } = new();

        /// <summary>
        /// Gets the maximum dependency depth in the graph
        /// </summary>
        public int MaxDepth { get; set; }
    }

    /// <summary>
    /// Represents a node in the dependency graph
    /// </summary>
    public class EntityNode
    {
        /// <summary>
        /// The entity this node represents
        /// </summary>
        public Entity Entity { get; set; } = null!;

        /// <summary>
        /// Entities that this entity depends on (must be generated first)
        /// </summary>
        public HashSet<string> Dependencies { get; set; } = new();

        /// <summary>
        /// Entities that depend on this entity
        /// </summary>
        public HashSet<string> Dependents { get; set; } = new();

        /// <summary>
        /// The depth level in the dependency hierarchy (0 = no dependencies)
        /// </summary>
        public int DependencyLevel { get; set; }

        /// <summary>
        /// Whether this entity is part of a circular reference
        /// </summary>
        public bool IsInCircularReference { get; set; }

        /// <summary>
        /// Foreign key relationships from this entity
        /// </summary>
        public List<ForeignKeyRelationship> ForeignKeyRelationships { get; set; } = new();
    }

    /// <summary>
    /// Represents a foreign key relationship for reference generation
    /// </summary>
    public class ForeignKeyRelationship
    {
        /// <summary>
        /// The source field (foreign key)
        /// </summary>
        public Field SourceField { get; set; } = null!;

        /// <summary>
        /// The target entity name
        /// </summary>
        public string TargetEntityName { get; set; } = null!;

        /// <summary>
        /// The target field name (usually primary key)
        /// </summary>
        public string TargetFieldName { get; set; } = null!;

        /// <summary>
        /// Whether this relationship allows null values
        /// </summary>
        public bool IsNullable { get; set; }

        /// <summary>
        /// The cardinality of the relationship
        /// </summary>
        public Aubrant.TestDataGenerator.Data.Enums.Cardinality Cardinality { get; set; }

        /// <summary>
        /// Whether this is a self-referencing relationship
        /// </summary>
        public bool IsSelfReferencing => SourceField.Entity.Name == TargetEntityName;
    }

    /// <summary>
    /// Represents a group of entities with circular references
    /// </summary>
    public class CircularReferenceGroup
    {
        /// <summary>
        /// Entities involved in the circular reference
        /// </summary>
        public List<string> EntityNames { get; set; } = new();

        /// <summary>
        /// The relationships that form the circular reference
        /// </summary>
        public List<ForeignKeyRelationship> CircularRelationships { get; set; } = new();

        /// <summary>
        /// Strategy for handling this circular reference
        /// </summary>
        public CircularReferenceStrategy Strategy { get; set; }
    }

    /// <summary>
    /// Strategies for handling circular references
    /// </summary>
    public enum CircularReferenceStrategy
    {
        /// <summary>
        /// Generate with null values first, then update in second pass
        /// </summary>
        NullFirstThenUpdate,

        /// <summary>
        /// Use partial generation with deferred foreign key assignment
        /// </summary>
        DeferredAssignment,

        /// <summary>
        /// Generate a subset first, then reference within the group
        /// </summary>
        SubsetFirst
    }

    /// <summary>
    /// Result of dependency graph validation
    /// </summary>
    public class DependencyGraphValidationResult
    {
        /// <summary>
        /// Whether the graph is valid
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Validation errors found
        /// </summary>
        public List<string> Errors { get; set; } = new();

        /// <summary>
        /// Validation warnings
        /// </summary>
        public List<string> Warnings { get; set; } = new();

        /// <summary>
        /// Suggested fixes for issues
        /// </summary>
        public List<string> Suggestions { get; set; } = new();
    }
}
