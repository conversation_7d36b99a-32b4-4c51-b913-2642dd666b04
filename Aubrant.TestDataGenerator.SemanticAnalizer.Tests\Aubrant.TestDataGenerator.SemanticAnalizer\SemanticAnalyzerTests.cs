using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.Data.Enums;
using Aubrant.TestDataGenerator.SchemaProfiler;
using Aubrant.TestDataGenerator.SemanticAnalizer;

namespace Aubrant.TestDataGenerator.Tests
{
    public class SemanticAnalyzerTests
    {
        [Fact]
        public async Task Analyze_Should_Suggest_Generators_For_Northwind_Schema()
        {
            using (var db = new DatabaseContext())
            {
                string dbPath = Path.Combine(Path.GetDirectoryName(db.DbPath), "northwind_small.sqlite");
                var profiler = new DataSourceProfiler(DataSourceProvider.SQLite, @$"Data Source={dbPath}");

                var dataSource = db.Projects.FirstOrDefault()?.DataSources.FirstOrDefault();

                if (dataSource == null)
                {
                    dataSource = await profiler.GetSchemaAsync();
                }

                Assert.NotNull(dataSource);
                Assert.NotEmpty(dataSource.Entities);

                await profiler.LoadSampleDataAsync(dataSource, 5);

                var analyzer = new SemanticAnalyzer("gemini-1.5-flash", "AIzaSyCH1WoqEbjsJCX1I6ccE1haEl-msQX4mvk");

                // Act
                await analyzer.Analyze(dataSource);
            }
        }
    }
}
