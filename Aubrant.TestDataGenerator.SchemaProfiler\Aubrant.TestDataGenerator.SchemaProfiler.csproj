﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CsvHelper" Version="33.1.0" />
    <PackageReference Include="ExcelDataReader" Version="3.7.0" />
    <PackageReference Include="ExcelDataReader.DataSet" Version="3.7.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.8" />
    <PackageReference Include="MongoDB.Bson" Version="3.4.2" />
    <PackageReference Include="MongoDB.Driver" Version="3.4.2" />
    <PackageReference Include="MongoDB.Driver.Core" Version="2.30.0" />
    <PackageReference Include="System.Data.SQLite" Version="1.0.119" />
    <PackageReference Include="Neo4j.Driver" Version="5.28.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Aubrant.TestDataGenerator.Data\Aubrant.TestDataGenerator.Data.csproj" />
  </ItemGroup>

</Project>
