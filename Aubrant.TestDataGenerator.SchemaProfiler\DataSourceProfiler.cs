using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.Data.Enums;
using Aubrant.TestDataGenerator.SchemaProfiler.Common;
using Aubrant.TestDataGenerator.SchemaProfiler.Extractors;
using Aubrant.TestDataGenerator.SchemaProfiler.Logging;

namespace Aubrant.TestDataGenerator.SchemaProfiler
{
    /// <summary>
    /// Manages the process of extracting schema and loading sample data from various data sources.
    /// </summary>
    public class DataSourceProfiler
    {
        private readonly IDataSourceSchemaExtractor? dataSourceSchemaExtractor;
        private readonly string connectionString;
        private readonly string? dataSourceName;
        private readonly ILogger? _logger;
        private readonly SchemaExtractionOptions? _options;

        /// <summary>
        /// Initializes a new instance of the <see cref="DataSourceProfiler"/> class with a specific data source provider.
        /// </summary>
        /// <param name="provider">The data source provider type.</param>
        /// <param name="connectionString">The connection string or file path for the data source.</param>
        /// <param name="dataSourceName">Optional. The name to assign to the data source. If null, a name will be inferred.</param>
        /// <param name="configureOptions">Optional. An action to configure schema extraction options.</param>
        /// <param name="logger">Optional. An ILogger instance for logging. If null, a NullLogger will be used.</param>
        public DataSourceProfiler(DataSourceProvider provider, string connectionString, string? dataSourceName = null, Action<SchemaExtractionOptions>? configureOptions = null, ILogger? logger = null)
        {
            _logger = logger ?? new NullLogger();
            _options = new SchemaExtractionOptions();
            configureOptions?.Invoke(_options);

            var factory = new SchemaExtractorFactory(_logger);
            dataSourceSchemaExtractor = factory.CreateExtractor(provider, _options);
            this.connectionString = connectionString;
            this.dataSourceName = dataSourceName;
            _logger.LogInfo($"DataSourceProfiler initialized for {provider} with connection {connectionString}");
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DataSourceProfiler"/> class with a custom schema extractor.
        /// </summary>
        /// <param name="dataSourceSchemaExtractor">A custom implementation of IDataSourceSchemaExtractor.</param>
        /// <param name="connectionString">The connection string or file path for the data source.</param>
        /// <param name="dataSourceName">Optional. The name to assign to the data source. If null, a name will be inferred.</param>
        /// <param name="logger">Optional. An ILogger instance for logging. If null, a NullLogger will be used.</param>
        public DataSourceProfiler(IDataSourceSchemaExtractor dataSourceSchemaExtractor, string connectionString, string? dataSourceName = null, ILogger? logger = null)
        {
            _logger = logger ?? new NullLogger();
            this.dataSourceSchemaExtractor = dataSourceSchemaExtractor;
            this.connectionString = connectionString;
            this.dataSourceName = dataSourceName;
            _options = new SchemaExtractionOptions(); // Default options if not provided
            _logger.LogInfo($"DataSourceProfiler initialized with custom processor for connection {connectionString}");
        }

        /// <summary>
        /// Asynchronously extracts the schema from the configured data source.
        /// </summary>
        /// <returns>A Task representing the asynchronous operation, returning the extracted DataSource schema.</returns>
        public async Task<DataSource> GetSchemaAsync()
        {
            _logger?.LogInfo($"Starting schema extraction for {dataSourceName ?? connectionString}");
            var schema = await dataSourceSchemaExtractor!.ExtractAsync(connectionString, dataSourceName ?? string.Empty);
            _logger?.LogInfo($"Schema extraction completed for {schema.Name}");
            return schema;
        }

        /// <summary>
        /// Asynchronously loads sample data into the provided DataSource.
        /// </summary>
        /// <param name="dataSource">The DataSource object to populate with sample data.</param>
        /// <param name="sampleSize">The number of sample rows to load for each entity.</param>
        /// <returns>A Task representing the asynchronous operation.</returns>
        public async Task LoadSampleDataAsync(DataSource dataSource, int? sampleSize = null)
        {
            sampleSize = sampleSize ?? _options?.SampleSize;
            _logger?.LogInfo($"Starting sample data loading for {dataSource.Name} (sample size: {sampleSize})");
            await dataSourceSchemaExtractor!.LoadSampleDataAsync(dataSource, connectionString, sampleSize);
            _logger?.LogInfo($"Sample data loading completed for {dataSource.Name}");
        }
    }
}
