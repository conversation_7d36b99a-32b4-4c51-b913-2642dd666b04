using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.Data.Enums;
using Aubrant.TestDataGenerator.SchemaProfiler.Common;
using Aubrant.TestDataGenerator.SchemaProfiler.Logging;
using Neo4j.Driver;
using ILogger = Aubrant.TestDataGenerator.SchemaProfiler.Logging.ILogger;

namespace Aubrant.TestDataGenerator.SchemaProfiler.Extractors
{
    public class Neo4jSchemaExtractor : IDataSourceSchemaExtractor
    {
        private readonly ILogger _logger;
        private readonly int _sampleSize;

        public Neo4jSchemaExtractor(SchemaExtractionOptions? options = null, Logging.ILogger? logger = null)
        {
            options = options ?? new SchemaExtractionOptions();
            _sampleSize = options.SampleSize;
            _logger = logger ?? new NullLogger();
            _logger.LogInfo($"Neo4jSchemaExtractor initialized with SampleSize: {_sampleSize}");
        }

        public async Task<DataSource> ExtractAsync(string connectionString, string dataSourceName)
        {
            _logger.LogInfo($"Extracting schema from Neo4j: {connectionString}");
            var dataSource = new DataSource { Name = dataSourceName, Type = DataSourceType.NoSQL, Provider = DataSourceProvider.Neo4j, ConnectionString = connectionString };
            var (uri, user, password) = ParseConnectionString(connectionString);

            using (var driver = GraphDatabase.Driver(uri, AuthTokens.Basic(user, password)))
            {
                var session = driver.AsyncSession();
                try
                {
                    await ExtractNodesAndProperties(session, dataSource);
                    await ExtractRelationships(session, dataSource);
                }
                finally
                {
                    await session.CloseAsync();
                }
            }

            return dataSource;
        }

        public async Task LoadSampleDataAsync(DataSource dataSource, string connectionString, int? sampleSize = null)
        {
            _logger.LogInfo($"Loading sample data for Neo4j: {dataSource.Name}");
            var (uri, user, password) = ParseConnectionString(connectionString);

            using (var driver = GraphDatabase.Driver(uri, AuthTokens.Basic(user, password)))
            {
                var session = driver.AsyncSession();
                try
                {
                    foreach (var entity in dataSource.Entities)
                    {
                        await LoadSampleDataForEntityAsync(session, entity, sampleSize ?? _sampleSize);
                    }
                }
                finally
                {
                    await session.CloseAsync();
                }
            }
        }

        private (string uri, string user, string password) ParseConnectionString(string connectionString)
        {
            var parts = connectionString.Split(';');
            var uri = parts.FirstOrDefault(p => p.StartsWith("Uri="))?.Substring(4);
            var user = parts.FirstOrDefault(p => p.StartsWith("User="))?.Substring(5);
            var password = parts.FirstOrDefault(p => p.StartsWith("Password="))?.Substring(9);
            return (uri, user, password);
        }

        private async Task ExtractNodesAndProperties(IAsyncSession session, DataSource dataSource)
        {
            var labelsResult = await session.RunAsync("CALL db.labels()");
            var labels = await labelsResult.ToListAsync(); // Buffer the results

            foreach (var record in labels)
            {
                var label = record["label"].As<string>();
                var entity = new Entity { Name = label, Schema = "Neo4j" };

                var propertiesResult = await session.RunAsync($"MATCH (n:{label}) WITH n LIMIT {_sampleSize} UNWIND keys(n) AS key RETURN key, valueType(n[key]) AS type, count(*) AS occurrences");
                var properties = await propertiesResult.ToListAsync(); // Buffer the results

                foreach (var propRecord in properties)
                {
                    var field = new Field
                    {
                        Name = propRecord["key"].As<string>(),
                        NativeType = propRecord["type"].As<string>(),
                        Type = MapNeo4jTypeToDataType(propRecord["type"].As<string>()),
                        IsNullable = propRecord["occurrences"].As<int>() < _sampleSize
                    };
                    entity.Fields.Add(field);
                }
                dataSource.Entities.Add(entity);
            }
        }

        private async Task ExtractRelationships(IAsyncSession session, DataSource dataSource)
        {
            var relationshipTypesResult = await session.RunAsync("CALL db.relationshipTypes()");
            var relationshipTypes = await relationshipTypesResult.ToListAsync(); // Buffer the results

            foreach (var record in relationshipTypes)
            {
                var relType = record["relationshipType"].As<string>();
                var relationshipsResult = await session.RunAsync($"MATCH (a)-[r:{relType}]->(b) RETURN DISTINCT labels(a) AS from, labels(b) AS to");
                var relationships = await relationshipsResult.ToListAsync(); // Buffer the results

                foreach (var relRecord in relationships)
                {
                    var fromLabels = relRecord["from"].As<List<string>>();
                    var toLabels = relRecord["to"].As<List<string>>();

                    foreach (var fromLabel in fromLabels)
                    {
                        foreach (var toLabel in toLabels)
                        {
                            var fromEntity = dataSource.Entities.FirstOrDefault(e => e.Name == fromLabel);
                            var toEntity = dataSource.Entities.FirstOrDefault(e => e.Name == toLabel);

                            if (fromEntity != null && toEntity != null)
                            {
                                fromEntity.Relationships.Add(new Relationship
                                {
                                    SourceEntityId = fromEntity.Id,
                                    TargetEntity = toLabel,
                                    Cardinality = Cardinality.OneToMany, // Neo4j cardinality is complex
                                    SourceField = "",
                                    TargetField = ""
                                });

                                toEntity.Relationships.Add(new Relationship
                                {
                                    SourceEntityId = toEntity.Id,
                                    TargetEntity = fromLabel,
                                    Cardinality = Cardinality.ManyToOne,
                                    SourceField = "",
                                    TargetField = ""
                                });
                            }
                        }
                    }
                }
            }
        }

        private async Task LoadSampleDataForEntityAsync(IAsyncSession session, Entity entity, int sampleSize)
        {
            var dataTable = new System.Data.DataTable(entity.Name);
            foreach (var field in entity.Fields)
            {
                dataTable.Columns.Add(new System.Data.DataColumn(field.Name, typeof(object)));
            }

            var query = $"MATCH (n:{entity.Name}) RETURN n LIMIT {sampleSize}";
            var result = await session.RunAsync(query);

            await foreach (var record in result)
            {
                var node = record["n"].As<INode>();
                var row = dataTable.NewRow();
                foreach (var field in entity.Fields)
                {
                    if (node.Properties.TryGetValue(field.Name, out var value))
                    {
                        row[field.Name] = value;
                    }
                    else
                    {
                        row[field.Name] = DBNull.Value;
                    }
                }
                dataTable.Rows.Add(row);
            }
            entity.Data = dataTable;
        }

        private DataType MapNeo4jTypeToDataType(string neo4jType)
        {
            var typeName = neo4jType.Split(' ')[0].ToLower().Trim();
            return typeName switch
            {
                "integer" => DataType.Integer,
                "float" => DataType.Decimal,
                "string" => DataType.String,
                "boolean" => DataType.Boolean,
                "date" => DataType.DateTime,
                "datetime" => DataType.DateTime,
                "point" => DataType.String, // Representing Geo data as string
                _ => DataType.Unsupported
            };
        }
    }
}
