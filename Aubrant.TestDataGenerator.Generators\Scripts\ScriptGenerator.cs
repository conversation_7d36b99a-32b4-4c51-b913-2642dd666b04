using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Functions;
using Aubrant.TestDataGenerator.Core.Interfaces;
using Microsoft.CodeAnalysis.CSharp.Scripting;
using Microsoft.CodeAnalysis.Scripting;
using System.ComponentModel;
using System.Data;
using System.Text.Json;

namespace Aubrant.TestDataGenerator.Generators.Scripts
{
    public class ScriptGenerator : IRuntimeGenerator
    {
        private Script<object> script;
        private ScriptRunner<object> scriptRunner;

        public string Name => "ScriptGenerator";
        public string Description => "Executes C# scripts dynamically with context of current record and built-in functions";
        public DataType ReturnType => DataType.Any;

        [Description("C# script expression")]
        public string Expression { get; set; }

        /// <summary>
        /// Parameterless constructor required for Semantic Analyzer
        /// </summary>
        public ScriptGenerator()
        {
        }

        /// <summary>
        /// C# script generator that allows dynamic execution of C# code snippets.
        /// </summary>
        /// <param name="expression">C# script expression</param>
        public ScriptGenerator(string expression)
        {
            InitializeScript(expression);
        }

        private void InitializeScript(string expression)
        {
            Expression = expression?.Trim() ?? throw new ArgumentNullException(nameof(expression), "Expression cannot be null.");

            var options = ScriptOptions.Default
                .AddReferences(typeof(DataRow).Assembly)
                .AddReferences(typeof(ScriptExtensions).Assembly)
                .AddReferences(typeof(Microsoft.CSharp.RuntimeBinder.CSharpArgumentInfo).Assembly)
                .AddImports("System")
                .AddImports("System.Linq")
                .AddImports("Aubrant.TestDataGenerator.Core.Utils");

            script = CSharpScript.Create(Expression, options, typeof(ScriptGlobals));
            scriptRunner = script.CreateDelegate();
        }

        public DataValue Generate(DataGeneratorContext context)
        {
            if (context == null) throw new ArgumentNullException(nameof(context));
            if (context.Row == null) throw new ArgumentNullException(nameof(context.Row));

            // Evaluate the script with globals as the context
            var globals = new ScriptGlobals(context, new ParameterCollection());

            var result = scriptRunner(globals).Result;

            // Wrap the result in a DataValue (type detection is basic, can be extended)
            return result switch
            {
                int i => new DataValue(i),
                decimal d => new DataValue(d),
                DateTime dt => new DataValue(dt),
                bool b => new DataValue(b),
                string s => new DataValue(s),
                _ => new DataValue(DataType.Any, result)
            };
        }

        public void Parse(DataType dataType, IDictionary<string, object> settings)
        {
            if (settings.TryGetValue("Expression", out object? expressionObject))
            {
                if (expressionObject is JsonElement expressionElement && expressionElement.ValueKind == JsonValueKind.String)
                {
                    InitializeScript(expressionElement.GetString());
                }
            }
        }
    }
}