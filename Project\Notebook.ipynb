{"cells": [{"cell_type": "code", "execution_count": null, "id": "de76c0f2", "metadata": {"polyglot_notebook": {"kernelName": "csharp"}, "vscode": {"languageId": "polyglot-notebook"}}, "outputs": [{"data": {"text/html": ["<div><div></div><div></div><div><strong>Installed Packages</strong><ul><li><span>LINQPad.Runtime, 8.3.7</span></li></ul></div></div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#r \".\\TestDataGenerator.Generators\\bin\\Debug\\net8.0\\TestDataGenerator.Core.dll\"\n", "#r \".\\TestDataGenerator.Generators\\bin\\Debug\\net8.0\\TestDataGenerator.Generators.dll\"\n", "// #r \"nuget: LINQPad, 5.46.0\"\n", "#r \"nuget: LINQPad.Runtime, 8.3.7\"\n", "\n", "using System.Data;\n", "using System.Linq;\n", "using LINQPad;\n", "using TestDataGenerator.Core;\n", "using TestDataGenerator.Core.Enums;\n", "using TestDataGenerator.Generators;"]}, {"cell_type": "markdown", "id": "12177e59", "metadata": {}, "source": ["* Create new Data Table:"]}, {"cell_type": "code", "execution_count": null, "id": "5753a7c1", "metadata": {"polyglot_notebook": {"kernelName": "csharp"}, "vscode": {"languageId": "polyglot-notebook"}}, "outputs": [], "source": ["var table = new DataTable(\"Employee\", \"dbo\");\n", "\n", "table.Columns.Add(\"EmployeeId\");\n", "table.Columns.Add(\"FirstName\");\n", "table.Columns.Add(\"LastName\");\n", "table.Columns.Add(\"Title\");\n", "table.Columns.Add(\"Gender\");\n", "table.Columns.Add(\"BirthDay\");\n", "table.Columns.Add(\"IP\");\n", "table.Columns.Add(\"IsActive\");"]}, {"cell_type": "markdown", "id": "1a1165fb", "metadata": {}, "source": ["* Test Generators:"]}, {"cell_type": "code", "execution_count": null, "id": "71d2753e", "metadata": {"polyglot_notebook": {"kernelName": "csharp"}, "vscode": {"languageId": "polyglot-notebook"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["System.Data.EnumerableRowCollection`1[System.Object[]]\r\n"]}, {"data": {"text/html": ["<details open=\"open\" class=\"dni-treeview\"><summary><span class=\"dni-code-hint\"><code>[ [ 1, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, 7/18/2025 9:55:06 AM, ***************, <PERSON><PERSON><PERSON> ], [ 2, <PERSON><PERSON>, <PERSON>, IT Manager, F, 6/26/2025 7:34:30 AM, ***************, <PERSON><PERSON><PERSON> ], [ 3, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, 5/23/2025 10:22:52 AM, **************, <PERSON> ], [ 4, <PERSON><PERSON>, <PERSON>, IT Manager, F, 6/22/2025 4:57:16 AM, 192.168.158.175, True ], [ 5, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, 6/11/2025 12:21:34 PM, 192.168.158.75, <PERSON> ], [ 6, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, 7/6/2025 9:33:41 AM, 192.168.82.106, <PERSON><PERSON><PERSON> ], [ 7, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, 7/19/2025 9:36:50 AM, 192.168.13.33, <PERSON> ], [ 8, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, 7/3/2025 2:59:21 PM, 192.168.248.130, <PERSON><PERSON><PERSON> ], [ 9, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, 6/11/2025 9:24:37 AM, 192.168.141.207, <PERSON><PERSON><PERSON> ], [ 10, He<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, 6/16/2025 7:02:20 PM, 192.168.159.219, True ], [ 11, He<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, 7/2/2025 5:28:38 <PERSON>, 192.168.230.186, <PERSON><PERSON><PERSON> ], [ 12, He<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, 5/30/2025 6:31:18 <PERSON>, 192.168.234.109, <PERSON> ], [ 13, He<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, 6/20/2025 6:47:10 AM, 192.168.251.62, True ], [ 14, Heiner, Morales, QA, M, 7/17/2025 5:03:25 PM, 192.168.144.122, True ], [ 15, Heiner, Morales, QA, M, 6/17/2025 12:46:49 PM, 192.168.106.123, False ], [ 16, Heiner, Morales, IT Manager, M, 6/7/2025 10:32:09 PM, 192.168.28.14, True ], [ 17, Heiner, Morales, Developer, U, 5/29/2025 12:11:20 AM, 192.168.41.37, True ], [ 18, Heiner, Morales, Developer, F, 6/16/2025 5:28:25 AM, 192.168.120.134, False ], [ 19, Heiner, Morales, QA, M, 6/28/2025 10:19:54 PM, 192.168.32.44, False ], [ 20, Heiner, Morales, QA, F, 5/21/2025 3:48:11 PM, 192.168.114.104, True ] ... (more) ]</code></span></summary><div><table><thead><tr></tr></thead><tbody><tr><td><i>(values)</i></td><td><table><thead><tr><th><i>index</i></th><th>value</th></tr></thead><tbody><tr><td>0</td><td><table><thead><tr><th><i>index</i></th><th>value</th></tr></thead><tbody><tr><td>0</td><td><div class=\"dni-plaintext\"><pre>1</pre></div></td></tr><tr><td>1</td><td><div class=\"dni-plaintext\"><pre>Heiner</pre></div></td></tr><tr><td>2</td><td><div class=\"dni-plaintext\"><pre>Morales</pre></div></td></tr><tr><td>3</td><td><div class=\"dni-plaintext\"><pre>QA</pre></div></td></tr><tr><td>4</td><td><div class=\"dni-plaintext\"><pre>M</pre></div></td></tr><tr><td>5</td><td><div class=\"dni-plaintext\"><pre>7/18/2025 9:55:06 AM</pre></div></td></tr><tr><td>6</td><td><div class=\"dni-plaintext\"><pre>***************</pre></div></td></tr><tr><td>7</td><td><div class=\"dni-plaintext\"><pre>False</pre></div></td></tr></tbody></table></td></tr><tr><td>1</td><td><table><thead><tr><th><i>index</i></th><th>value</th></tr></thead><tbody><tr><td>0</td><td><div class=\"dni-plaintext\"><pre>2</pre></div></td></tr><tr><td>1</td><td><div class=\"dni-plaintext\"><pre>Heiner</pre></div></td></tr><tr><td>2</td><td><div class=\"dni-plaintext\"><pre>Morales</pre></div></td></tr><tr><td>3</td><td><div class=\"dni-plaintext\"><pre>IT Manager</pre></div></td></tr><tr><td>4</td><td><div class=\"dni-plaintext\"><pre>F</pre></div></td></tr><tr><td>5</td><td><div class=\"dni-plaintext\"><pre>6/26/2025 7:34:30 AM</pre></div></td></tr><tr><td>6</td><td><div class=\"dni-plaintext\"><pre>***************</pre></div></td></tr><tr><td>7</td><td><div class=\"dni-plaintext\"><pre>False</pre></div></td></tr></tbody></table></td></tr><tr><td>2</td><td><table><thead><tr><th><i>index</i></th><th>value</th></tr></thead><tbody><tr><td>0</td><td><div class=\"dni-plaintext\"><pre>3</pre></div></td></tr><tr><td>1</td><td><div class=\"dni-plaintext\"><pre>Heiner</pre></div></td></tr><tr><td>2</td><td><div class=\"dni-plaintext\"><pre>Morales</pre></div></td></tr><tr><td>3</td><td><div class=\"dni-plaintext\"><pre>QA</pre></div></td></tr><tr><td>4</td><td><div class=\"dni-plaintext\"><pre>U</pre></div></td></tr><tr><td>5</td><td><div class=\"dni-plaintext\"><pre>5/23/2025 10:22:52 AM</pre></div></td></tr><tr><td>6</td><td><div class=\"dni-plaintext\"><pre>**************</pre></div></td></tr><tr><td>7</td><td><div class=\"dni-plaintext\"><pre>True</pre></div></td></tr></tbody></table></td></tr><tr><td>3</td><td><table><thead><tr><th><i>index</i></th><th>value</th></tr></thead><tbody><tr><td>0</td><td><div class=\"dni-plaintext\"><pre>4</pre></div></td></tr><tr><td>1</td><td><div class=\"dni-plaintext\"><pre>Heiner</pre></div></td></tr><tr><td>2</td><td><div class=\"dni-plaintext\"><pre>Morales</pre></div></td></tr><tr><td>3</td><td><div class=\"dni-plaintext\"><pre>IT Manager</pre></div></td></tr><tr><td>4</td><td><div class=\"dni-plaintext\"><pre>F</pre></div></td></tr><tr><td>5</td><td><div class=\"dni-plaintext\"><pre>6/22/2025 4:57:16 AM</pre></div></td></tr><tr><td>6</td><td><div class=\"dni-plaintext\"><pre>192.168.158.175</pre></div></td></tr><tr><td>7</td><td><div class=\"dni-plaintext\"><pre>True</pre></div></td></tr></tbody></table></td></tr><tr><td>4</td><td><table><thead><tr><th><i>index</i></th><th>value</th></tr></thead><tbody><tr><td>0</td><td><div class=\"dni-plaintext\"><pre>5</pre></div></td></tr><tr><td>1</td><td><div class=\"dni-plaintext\"><pre>Heiner</pre></div></td></tr><tr><td>2</td><td><div class=\"dni-plaintext\"><pre>Morales</pre></div></td></tr><tr><td>3</td><td><div class=\"dni-plaintext\"><pre>QA</pre></div></td></tr><tr><td>4</td><td><div class=\"dni-plaintext\"><pre>U</pre></div></td></tr><tr><td>5</td><td><div class=\"dni-plaintext\"><pre>6/11/2025 12:21:34 PM</pre></div></td></tr><tr><td>6</td><td><div class=\"dni-plaintext\"><pre>192.168.158.75</pre></div></td></tr><tr><td>7</td><td><div class=\"dni-plaintext\"><pre>True</pre></div></td></tr></tbody></table></td></tr><tr><td>5</td><td><table><thead><tr><th><i>index</i></th><th>value</th></tr></thead><tbody><tr><td>0</td><td><div class=\"dni-plaintext\"><pre>6</pre></div></td></tr><tr><td>1</td><td><div class=\"dni-plaintext\"><pre>Heiner</pre></div></td></tr><tr><td>2</td><td><div class=\"dni-plaintext\"><pre>Morales</pre></div></td></tr><tr><td>3</td><td><div class=\"dni-plaintext\"><pre>QA</pre></div></td></tr><tr><td>4</td><td><div class=\"dni-plaintext\"><pre>F</pre></div></td></tr><tr><td>5</td><td><div class=\"dni-plaintext\"><pre>7/6/2025 9:33:41 AM</pre></div></td></tr><tr><td>6</td><td><div class=\"dni-plaintext\"><pre>192.168.82.106</pre></div></td></tr><tr><td>7</td><td><div class=\"dni-plaintext\"><pre>False</pre></div></td></tr></tbody></table></td></tr><tr><td>6</td><td><table><thead><tr><th><i>index</i></th><th>value</th></tr></thead><tbody><tr><td>0</td><td><div class=\"dni-plaintext\"><pre>7</pre></div></td></tr><tr><td>1</td><td><div class=\"dni-plaintext\"><pre>Heiner</pre></div></td></tr><tr><td>2</td><td><div class=\"dni-plaintext\"><pre>Morales</pre></div></td></tr><tr><td>3</td><td><div class=\"dni-plaintext\"><pre>QA</pre></div></td></tr><tr><td>4</td><td><div class=\"dni-plaintext\"><pre>M</pre></div></td></tr><tr><td>5</td><td><div class=\"dni-plaintext\"><pre>7/19/2025 9:36:50 AM</pre></div></td></tr><tr><td>6</td><td><div class=\"dni-plaintext\"><pre>192.168.13.33</pre></div></td></tr><tr><td>7</td><td><div class=\"dni-plaintext\"><pre>True</pre></div></td></tr></tbody></table></td></tr><tr><td>7</td><td><table><thead><tr><th><i>index</i></th><th>value</th></tr></thead><tbody><tr><td>0</td><td><div class=\"dni-plaintext\"><pre>8</pre></div></td></tr><tr><td>1</td><td><div class=\"dni-plaintext\"><pre>Heiner</pre></div></td></tr><tr><td>2</td><td><div class=\"dni-plaintext\"><pre>Morales</pre></div></td></tr><tr><td>3</td><td><div class=\"dni-plaintext\"><pre>QA</pre></div></td></tr><tr><td>4</td><td><div class=\"dni-plaintext\"><pre>U</pre></div></td></tr><tr><td>5</td><td><div class=\"dni-plaintext\"><pre>7/3/2025 2:59:21 PM</pre></div></td></tr><tr><td>6</td><td><div class=\"dni-plaintext\"><pre>192.168.248.130</pre></div></td></tr><tr><td>7</td><td><div class=\"dni-plaintext\"><pre>False</pre></div></td></tr></tbody></table></td></tr><tr><td>8</td><td><table><thead><tr><th><i>index</i></th><th>value</th></tr></thead><tbody><tr><td>0</td><td><div class=\"dni-plaintext\"><pre>9</pre></div></td></tr><tr><td>1</td><td><div class=\"dni-plaintext\"><pre>Heiner</pre></div></td></tr><tr><td>2</td><td><div class=\"dni-plaintext\"><pre>Morales</pre></div></td></tr><tr><td>3</td><td><div class=\"dni-plaintext\"><pre>Developer</pre></div></td></tr><tr><td>4</td><td><div class=\"dni-plaintext\"><pre>M</pre></div></td></tr><tr><td>5</td><td><div class=\"dni-plaintext\"><pre>6/11/2025 9:24:37 AM</pre></div></td></tr><tr><td>6</td><td><div class=\"dni-plaintext\"><pre>192.168.141.207</pre></div></td></tr><tr><td>7</td><td><div class=\"dni-plaintext\"><pre>False</pre></div></td></tr></tbody></table></td></tr><tr><td>9</td><td><table><thead><tr><th><i>index</i></th><th>value</th></tr></thead><tbody><tr><td>0</td><td><div class=\"dni-plaintext\"><pre>10</pre></div></td></tr><tr><td>1</td><td><div class=\"dni-plaintext\"><pre>Heiner</pre></div></td></tr><tr><td>2</td><td><div class=\"dni-plaintext\"><pre>Morales</pre></div></td></tr><tr><td>3</td><td><div class=\"dni-plaintext\"><pre>QA</pre></div></td></tr><tr><td>4</td><td><div class=\"dni-plaintext\"><pre>F</pre></div></td></tr><tr><td>5</td><td><div class=\"dni-plaintext\"><pre>6/16/2025 7:02:20 PM</pre></div></td></tr><tr><td>6</td><td><div class=\"dni-plaintext\"><pre>192.168.159.219</pre></div></td></tr><tr><td>7</td><td><div class=\"dni-plaintext\"><pre>True</pre></div></td></tr></tbody></table></td></tr><tr><td>10</td><td><table><thead><tr><th><i>index</i></th><th>value</th></tr></thead><tbody><tr><td>0</td><td><div class=\"dni-plaintext\"><pre>11</pre></div></td></tr><tr><td>1</td><td><div class=\"dni-plaintext\"><pre>Heiner</pre></div></td></tr><tr><td>2</td><td><div class=\"dni-plaintext\"><pre>Morales</pre></div></td></tr><tr><td>3</td><td><div class=\"dni-plaintext\"><pre>Developer</pre></div></td></tr><tr><td>4</td><td><div class=\"dni-plaintext\"><pre>F</pre></div></td></tr><tr><td>5</td><td><div class=\"dni-plaintext\"><pre>7/2/2025 5:28:38 AM</pre></div></td></tr><tr><td>6</td><td><div class=\"dni-plaintext\"><pre>192.168.230.186</pre></div></td></tr><tr><td>7</td><td><div class=\"dni-plaintext\"><pre>False</pre></div></td></tr></tbody></table></td></tr><tr><td>11</td><td><table><thead><tr><th><i>index</i></th><th>value</th></tr></thead><tbody><tr><td>0</td><td><div class=\"dni-plaintext\"><pre>12</pre></div></td></tr><tr><td>1</td><td><div class=\"dni-plaintext\"><pre>Heiner</pre></div></td></tr><tr><td>2</td><td><div class=\"dni-plaintext\"><pre>Morales</pre></div></td></tr><tr><td>3</td><td><div class=\"dni-plaintext\"><pre>QA</pre></div></td></tr><tr><td>4</td><td><div class=\"dni-plaintext\"><pre>F</pre></div></td></tr><tr><td>5</td><td><div class=\"dni-plaintext\"><pre>5/30/2025 6:31:18 PM</pre></div></td></tr><tr><td>6</td><td><div class=\"dni-plaintext\"><pre>192.168.234.109</pre></div></td></tr><tr><td>7</td><td><div class=\"dni-plaintext\"><pre>True</pre></div></td></tr></tbody></table></td></tr><tr><td>12</td><td><table><thead><tr><th><i>index</i></th><th>value</th></tr></thead><tbody><tr><td>0</td><td><div class=\"dni-plaintext\"><pre>13</pre></div></td></tr><tr><td>1</td><td><div class=\"dni-plaintext\"><pre>Heiner</pre></div></td></tr><tr><td>2</td><td><div class=\"dni-plaintext\"><pre>Morales</pre></div></td></tr><tr><td>3</td><td><div class=\"dni-plaintext\"><pre>QA</pre></div></td></tr><tr><td>4</td><td><div class=\"dni-plaintext\"><pre>M</pre></div></td></tr><tr><td>5</td><td><div class=\"dni-plaintext\"><pre>6/20/2025 6:47:10 AM</pre></div></td></tr><tr><td>6</td><td><div class=\"dni-plaintext\"><pre>192.168.251.62</pre></div></td></tr><tr><td>7</td><td><div class=\"dni-plaintext\"><pre>True</pre></div></td></tr></tbody></table></td></tr><tr><td>13</td><td><table><thead><tr><th><i>index</i></th><th>value</th></tr></thead><tbody><tr><td>0</td><td><div class=\"dni-plaintext\"><pre>14</pre></div></td></tr><tr><td>1</td><td><div class=\"dni-plaintext\"><pre>Heiner</pre></div></td></tr><tr><td>2</td><td><div class=\"dni-plaintext\"><pre>Morales</pre></div></td></tr><tr><td>3</td><td><div class=\"dni-plaintext\"><pre>QA</pre></div></td></tr><tr><td>4</td><td><div class=\"dni-plaintext\"><pre>M</pre></div></td></tr><tr><td>5</td><td><div class=\"dni-plaintext\"><pre>7/17/2025 5:03:25 PM</pre></div></td></tr><tr><td>6</td><td><div class=\"dni-plaintext\"><pre>192.168.144.122</pre></div></td></tr><tr><td>7</td><td><div class=\"dni-plaintext\"><pre>True</pre></div></td></tr></tbody></table></td></tr><tr><td>14</td><td><table><thead><tr><th><i>index</i></th><th>value</th></tr></thead><tbody><tr><td>0</td><td><div class=\"dni-plaintext\"><pre>15</pre></div></td></tr><tr><td>1</td><td><div class=\"dni-plaintext\"><pre>Heiner</pre></div></td></tr><tr><td>2</td><td><div class=\"dni-plaintext\"><pre>Morales</pre></div></td></tr><tr><td>3</td><td><div class=\"dni-plaintext\"><pre>QA</pre></div></td></tr><tr><td>4</td><td><div class=\"dni-plaintext\"><pre>M</pre></div></td></tr><tr><td>5</td><td><div class=\"dni-plaintext\"><pre>6/17/2025 12:46:49 PM</pre></div></td></tr><tr><td>6</td><td><div class=\"dni-plaintext\"><pre>192.168.106.123</pre></div></td></tr><tr><td>7</td><td><div class=\"dni-plaintext\"><pre>False</pre></div></td></tr></tbody></table></td></tr><tr><td>15</td><td><table><thead><tr><th><i>index</i></th><th>value</th></tr></thead><tbody><tr><td>0</td><td><div class=\"dni-plaintext\"><pre>16</pre></div></td></tr><tr><td>1</td><td><div class=\"dni-plaintext\"><pre>Heiner</pre></div></td></tr><tr><td>2</td><td><div class=\"dni-plaintext\"><pre>Morales</pre></div></td></tr><tr><td>3</td><td><div class=\"dni-plaintext\"><pre>IT Manager</pre></div></td></tr><tr><td>4</td><td><div class=\"dni-plaintext\"><pre>M</pre></div></td></tr><tr><td>5</td><td><div class=\"dni-plaintext\"><pre>6/7/2025 10:32:09 PM</pre></div></td></tr><tr><td>6</td><td><div class=\"dni-plaintext\"><pre>192.168.28.14</pre></div></td></tr><tr><td>7</td><td><div class=\"dni-plaintext\"><pre>True</pre></div></td></tr></tbody></table></td></tr><tr><td>16</td><td><table><thead><tr><th><i>index</i></th><th>value</th></tr></thead><tbody><tr><td>0</td><td><div class=\"dni-plaintext\"><pre>17</pre></div></td></tr><tr><td>1</td><td><div class=\"dni-plaintext\"><pre>Heiner</pre></div></td></tr><tr><td>2</td><td><div class=\"dni-plaintext\"><pre>Morales</pre></div></td></tr><tr><td>3</td><td><div class=\"dni-plaintext\"><pre>Developer</pre></div></td></tr><tr><td>4</td><td><div class=\"dni-plaintext\"><pre>U</pre></div></td></tr><tr><td>5</td><td><div class=\"dni-plaintext\"><pre>5/29/2025 12:11:20 AM</pre></div></td></tr><tr><td>6</td><td><div class=\"dni-plaintext\"><pre>192.168.41.37</pre></div></td></tr><tr><td>7</td><td><div class=\"dni-plaintext\"><pre>True</pre></div></td></tr></tbody></table></td></tr><tr><td>17</td><td><table><thead><tr><th><i>index</i></th><th>value</th></tr></thead><tbody><tr><td>0</td><td><div class=\"dni-plaintext\"><pre>18</pre></div></td></tr><tr><td>1</td><td><div class=\"dni-plaintext\"><pre>Heiner</pre></div></td></tr><tr><td>2</td><td><div class=\"dni-plaintext\"><pre>Morales</pre></div></td></tr><tr><td>3</td><td><div class=\"dni-plaintext\"><pre>Developer</pre></div></td></tr><tr><td>4</td><td><div class=\"dni-plaintext\"><pre>F</pre></div></td></tr><tr><td>5</td><td><div class=\"dni-plaintext\"><pre>6/16/2025 5:28:25 AM</pre></div></td></tr><tr><td>6</td><td><div class=\"dni-plaintext\"><pre>192.168.120.134</pre></div></td></tr><tr><td>7</td><td><div class=\"dni-plaintext\"><pre>False</pre></div></td></tr></tbody></table></td></tr><tr><td>18</td><td><table><thead><tr><th><i>index</i></th><th>value</th></tr></thead><tbody><tr><td>0</td><td><div class=\"dni-plaintext\"><pre>19</pre></div></td></tr><tr><td>1</td><td><div class=\"dni-plaintext\"><pre>Heiner</pre></div></td></tr><tr><td>2</td><td><div class=\"dni-plaintext\"><pre>Morales</pre></div></td></tr><tr><td>3</td><td><div class=\"dni-plaintext\"><pre>QA</pre></div></td></tr><tr><td>4</td><td><div class=\"dni-plaintext\"><pre>M</pre></div></td></tr><tr><td>5</td><td><div class=\"dni-plaintext\"><pre>6/28/2025 10:19:54 PM</pre></div></td></tr><tr><td>6</td><td><div class=\"dni-plaintext\"><pre>192.168.32.44</pre></div></td></tr><tr><td>7</td><td><div class=\"dni-plaintext\"><pre>False</pre></div></td></tr></tbody></table></td></tr><tr><td>19</td><td><table><thead><tr><th><i>index</i></th><th>value</th></tr></thead><tbody><tr><td>0</td><td><div class=\"dni-plaintext\"><pre>20</pre></div></td></tr><tr><td>1</td><td><div class=\"dni-plaintext\"><pre>Heiner</pre></div></td></tr><tr><td>2</td><td><div class=\"dni-plaintext\"><pre>Morales</pre></div></td></tr><tr><td>3</td><td><div class=\"dni-plaintext\"><pre>QA</pre></div></td></tr><tr><td>4</td><td><div class=\"dni-plaintext\"><pre>F</pre></div></td></tr><tr><td>5</td><td><div class=\"dni-plaintext\"><pre>5/21/2025 3:48:11 PM</pre></div></td></tr><tr><td>6</td><td><div class=\"dni-plaintext\"><pre>192.168.114.104</pre></div></td></tr><tr><td>7</td><td><div class=\"dni-plaintext\"><pre>True</pre></div></td></tr></tbody></table></td></tr><tr><td colspan=\"2\"><i>... (more)</i></td></tr></tbody></table></td></tr></tbody></table></div></details><style>\r\n", ".dni-code-hint {\r\n", "    font-style: italic;\r\n", "    overflow: hidden;\r\n", "    white-space: nowrap;\r\n", "}\r\n", ".dni-treeview {\r\n", "    white-space: nowrap;\r\n", "}\r\n", ".dni-treeview td {\r\n", "    vertical-align: top;\r\n", "    text-align: start;\r\n", "}\r\n", "details.dni-treeview {\r\n", "    padding-left: 1em;\r\n", "}\r\n", "table td {\r\n", "    text-align: start;\r\n", "}\r\n", "table tr { \r\n", "    vertical-align: top; \r\n", "    margin: 0em 0px;\r\n", "}\r\n", "table tr td pre \r\n", "{ \r\n", "    vertical-align: top !important; \r\n", "    margin: 0em 0px !important;\r\n", "} \r\n", "table th {\r\n", "    text-align: start;\r\n", "}\r\n", "</style>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["var idGenerator = new AutoIncrementGenerator();\n", "var nameGenerator = new ConstantGenerator(\"Heiner\");\n", "var lastNameGenerator = new ConstantGenerator(\"Morales\");\n", "\n", "var titleGenerator = new ChoiceGenerator(\n", "\tnew OptionConfiguration { Value = \"IT Manager\", Probability = 15 },\n", "\tnew OptionConfiguration { Value = \"Developer\", Probability = 19 },\n", "\tnew OptionConfiguration { Value = \"Project Manager\", Probability = 5 },\n", "\tnew OptionConfiguration { Value = \"Product Owner\", Probability = 1 },\n", "\tnew OptionConfiguration { Value = \"QA\", Probability = 60 }\n", ");\n", "\n", "var genderGenerator = new ChoiceGenerator(\n", "\tnew OptionConfiguration { Value = \"M\", Label = \"Male\", Probability = 43 },\n", "\tnew OptionConfiguration { Value = \"F\", Label = \"Female\", Probability = 44 },\n", "\tnew OptionConfiguration { Value = \"U\", Label = \"Undefined\", Probability = 13 }\n", ");\n", "\n", "var birthDayGenerator = new RandomGenerator(DateTime.Today.AddMonths(-1), DateTime.Today.AddMonths(1));\n", "\n", "var ipGenerator = new IPAddressFunction();\n", "ipGenerator.Parameters[\"Octet1\"].Value = new DataValue(192);\n", "ipGenerator.Parameters[\"Octet2\"].Value = new DataValue(168);\n", "\n", "var isActiveGenerator = new RandomGenerator(DataType.Boolean);\n", "\n", "for (int i = 1; i <= 100; i++)\n", "{\n", "\ttable.Rows.Add\n", "\t(\n", "\t\tidGenerator.Generate().AsInt(), \t\t\t// EmployeeId\n", "\t\tnameGenerator.Generate(), \t\t\t\t\t// FirstName\n", "\t\tlastNameGenerator.Generate(), \t\t\t\t// LastName\n", "\t\ttitleGenerator.Generate().ToString(),\t\t// Title\n", "\t\tgenderGenerator.Generate().ToString(),\t\t// Gender\n", "\t\tbirthDayGenerator.Generate().AsDate(),  \t// BirthDate\t\t\n", "\t\tipGenerator.Generate().ToString(),\t\t\t// IPV4\n", "\t\tisActiveGenerator.Generate().AsBoolean()\t// IsActive\n", "\t);\n", "}\n", "\n", "table.AsEnumerable().Select(r => r.<PERSON>).Dump()"]}], "metadata": {"kernelspec": {"display_name": ".NET (C#)", "language": "C#", "name": ".net-csharp"}, "language_info": {"name": "csharp"}, "polyglot_notebook": {"kernelInfo": {"defaultKernelName": "csharp", "items": [{"aliases": [], "name": "csharp"}]}}}, "nbformat": 4, "nbformat_minor": 5}