namespace Aubrant.TestDataGenerator.Data.Enums
{
    /// <summary>
    /// Defines the general category of a data source.
    /// </summary>
    public enum DataSourceType
    {
        /// <summary>File-based data source (e.g., CSV, Excel).</summary>
        File,
        /// <summary>Relational database (e.g., SQL Server, SQLite).</summary>
        RelationalDatabase,
        /// <summary>NoSQL database (e.g., MongoDB).</summary>
        NoSQL,
    }
}