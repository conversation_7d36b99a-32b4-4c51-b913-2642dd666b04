﻿using Aubrant.TestDataGenerator.Core.Patterns;

namespace Aubrant.TestDataGenerator.Generators.Patterns
{
    /// <summary>
    /// Allows to create custom patterns that can be defined at runtime.
    /// </summary>
    public class RuntimePattern : BasePattern
    {
        public RuntimePattern() : base() { }

        public RuntimePattern(string name, string description, string pattern, List<Token> tokens) : base(name, description, pattern, tokens)
        { }
    }
}