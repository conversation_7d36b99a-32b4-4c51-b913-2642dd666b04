"use client"

import type React from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ArrowUpDown } from "lucide-react"
import { Pagination } from "./pagination"

export interface Column<T> {
  key: keyof T | string
  label: string
  sortable?: boolean
  align?: "left" | "center" | "right"
  render?: (item: T) => React.ReactNode
}

export interface DataGridProps<T> {
  data: T[]
  columns: Column<T>[]
  rowKey: keyof T
  sortField?: keyof T | string
  sortDirection?: "asc" | "desc"
  onSort?: (field: keyof T | string) => void
  currentPage?: number
  itemsPerPage?: number
  totalItems?: number
  onPageChange?: (page: number) => void
  onItemsPerPageChange?: (itemsPerPage: string) => void
  showPagination?: boolean
  emptyMessage?: string
  className?: string
}

export function DataGrid<T extends object>({
    data,
    columns,
    rowKey,
    sortField,
    sortDirection,
    onSort,
    currentPage = 1,
    itemsPerPage = 10,
    totalItems,
    onPageChange,
    onItemsPerPageChange,
    showPagination = false,
    emptyMessage = "No data available",
    className = "",
}: DataGridProps<T>) {
    // Calculate pagination if not provided
    const actualTotalItems = totalItems || data.length
    const totalPages = Math.ceil(actualTotalItems / itemsPerPage)
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    const paginatedData = totalItems ? data : data.slice(startIndex, endIndex)

    const getCellContent = (item: T, column: Column<T>) => {
        if (column.render) {
            return column.render(item)
        }

        const value = item[column.key as keyof T]
        return value?.toString() || ""
    }

    const getCellAlignment = (align?: string) => {
        switch (align) {
            case "center":
                return "text-center"
            case "right":
                return "text-right"
            default:
                return "text-left"
        }
    }

    if (data.length === 0) {
        return (
            <div className={`border rounded-lg ${className}`}>
                <div className="text-center py-8 text-gray-500">{emptyMessage}</div>
            </div>
        )
    }

    return (
        <div className={`space-y-4 ${className}`}>
            {/* Data Table */}
            <div className="border rounded-lg">
                <Table>
                    <TableHeader>
                        <TableRow>
                            {columns.map((column) => (
                                <TableHead key={column.key.toString()} className={getCellAlignment(column.align)}>
                                    {column.sortable && onSort ? (
                                        <button
                                            onClick={() => onSort(column.key)}
                                            className="flex items-center gap-2 hover:text-gray-900"
                                        >
                                            {column.label}
                                            <ArrowUpDown
                                                className={`h-4 w-4 transition-transform ${
                                                    sortField === column.key ? "text-gray-900" : "text-gray-400"
                                                } ${
                                                    sortField === column.key && sortDirection === "desc"
                                                        ? "rotate-180"
                                                        : ""
                                                }`}
                                            />
                                        </button>
                                    ) : (
                                        column.label
                                    )}
                                </TableHead>
                            ))}
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {paginatedData.map((item) => (
                            <TableRow key={item[rowKey] as React.Key} className="hover:bg-gray-50">
                                {columns.map((column) => (
                                    <TableCell
                                        key={`${item[rowKey] as React.Key}-${column.key.toString()}`}
                                        className={`${getCellAlignment(column.align)} py-2`}
                                    >
                                        {getCellContent(item, column)}
                                    </TableCell>
                                ))}
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>

            {/* Pagination */}
            {showPagination && (
                <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    totalItems={actualTotalItems}
                    itemsPerPage={itemsPerPage}
                    onPageChange={onPageChange}
                    onItemsPerPageChange={onItemsPerPageChange}
                />
            )}
        </div>
    )
}
