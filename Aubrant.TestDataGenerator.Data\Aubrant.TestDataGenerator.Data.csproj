﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="HotChocolate.Abstractions" Version="16.0.0-p.6.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.8" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" Version="9.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.8" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Aubrant.TestDataGenerator.Core\Aubrant.TestDataGenerator.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Database\SemanticAnalysis_20250808.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
