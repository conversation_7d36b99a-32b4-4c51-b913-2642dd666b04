﻿using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Interfaces;
using System.Text.Json;

namespace Aubrant.TestDataGenerator.Generators
{
    /// <summary>
    /// Represents a generator that produces a sequence of auto-incrementing integer values.
    /// </summary>
    public class AutoIncrementGenerator : ICompiledGenerator
    {
        private int current;

        /// <summary>
        /// Gets the name of the generator.
        /// </summary>
        public string Name => "AutoIncrement";

        /// <summary>
        /// Gets the description of the generator.
        /// </summary>
        public string Description => "Auto Increment Generator";

        /// <summary>
        /// Gets the return type of the generator.
        /// </summary>
        public DataType ReturnType => DataType.Integer;

        /// <summary>
        /// Gets or sets the starting value for the auto-increment sequence.
        /// </summary>
        [System.ComponentModel.Description("Gets or sets the starting value for the auto-increment sequence.")]
        public int Start { get; set; }
        /// <summary>
        /// Gets or sets the step value for the auto-increment sequence.
        /// </summary>
        [System.ComponentModel.Description("Gets or sets the step value for the auto-increment sequence.")]
        public int Step { get; set; }

        public AutoIncrementGenerator() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="AutoIncrementGenerator"/> class.
        /// </summary>
        /// <param name="start">The starting value for the sequence.</param>
        /// <param name="step">The value to increment by at each step.</param>
        public AutoIncrementGenerator(int start = 1, int step = 1)
        {
            Start = start;
            current = start;
            Step = step;
        }

        /// <summary>
        /// Generates the next value in the auto-increment sequence.
        /// </summary>
        /// <returns>A <see cref="DataValue"/> containing the next integer in the sequence.</returns>
        public DataValue Generate()
        {
            DataValue result = new DataValue(DataType.Integer, current);
            current += Step;
            return result;
        }

        public void Parse(DataType data, IDictionary<string, object> settings)
        {
            if (settings.TryGetValue("Start", out object? startValue) && startValue is JsonElement startElement && startElement.ValueKind == JsonValueKind.Number)
            {
                Start = startElement.GetInt32();
                current = Start; // Reset current value to the new start
            }

            if (settings.TryGetValue("Step", out object? stepValue) && stepValue is JsonElement stepElement && stepElement.ValueKind == JsonValueKind.Number)
            {
                Step = stepElement.GetInt32();
            }
        }
    }
}
