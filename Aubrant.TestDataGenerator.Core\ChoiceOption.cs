﻿using System.ComponentModel;

namespace Aubrant.TestDataGenerator.Core
{
    public class ChoiceOption
    {
        /// <summary>
        /// Option Value
        /// </summary>
        [Description("Option Value")]
        public DataValue? Value { get; }

        /// <summary>
        /// Option Range of Values
        /// </summary>
        [Description("Option Range of Values")]
        public DataRange? Range { get; }

        /// <summary>
        /// Probability of selecting value or range
        /// </summary>
        [Description("Probability of selecting value or range")]
        public double Probability { get; }

        /// <summary>
        /// An optional label for the value. To guide the user on the meaning of the value in the UI, not used to generate the value.
        /// </summary>
        [Description("An optional label for the value. To guide the user on the meaning of the value in the UI, not used to generate the value.")]
        public string? Label { get; set; }

        public ChoiceOption(DataValue value, double probability, string label = null)
        {
            Value = value;
            Probability = probability;
            Label = label;
        }

        public ChoiceOption((DataValue Min, DataValue Max) range, double probability, string? label = null)
        {
            Value = null;
            Range = new DataRange { Min = range.Min, Max = range.Max };
            Probability = probability;
            Label = label;
        }
    }    
}
