using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.Data.Enums;
using HotChocolate.Types;

namespace Aubrant.TestDataGenerator.UI.Server.Types
{
    public class KeyConstraintType : ObjectType<KeyConstraint>
    {
        protected override void Configure(IObjectTypeDescriptor<KeyConstraint> descriptor)
        {
            descriptor.Field(kc => kc.Id).Type<IdType>();
            descriptor.Field(kc => kc.EntityId).Type<IntType>();
            descriptor.Field(kc => kc.KeyType).Type<EnumType<KeyType>>();
            descriptor.Field(kc => kc.Columns).Type<ListType<StringType>>();
            descriptor.Field(kc => kc.ReferencedEntity).Type<StringType>();
            descriptor.Field(kc => kc.ReferencedColumns).Type<ListType<StringType>>();
            descriptor.Field(kc => kc.Entity).Type<EntityType>();
        }
    }
}
