﻿using Aubrant.TestDataGenerator.Core.Patterns;

namespace Aubrant.TestDataGenerator.Generators.Patterns
{
    public class DatePattern : BasePattern
    {
        public DatePattern() : base(
            name: "Date",
            description: "Valid date and time in MMM/dd/yyyy format with optional time components",
            pattern: "{MMM}/{DD}/{YYYY}",
            tokens: new List<Token>
            {
                new DigitsToken("MM", "Two-digit month", 2, (1, 12)),
                new ChoiceToken("MMM", "Three-letter month abbreviation", new[] { "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec" }),
                new ChoiceToken("MMMM", "Full month name", new[] { "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December" }),
                new DigitsToken("DD", "Day of the month as a number from 01 through 31", 2, (1, 31)),
                new ChoiceToken("DDD", "Abbreviated name of the day", new[] { "<PERSON>", "Tue", "Wed", "Thu", "Fri", "Sat", "<PERSON>" }),
                new ChoiceToken("DDDD", "Full name of the day", new[] { "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday" }),
                new DigitsToken("YYYY", "Four-digit year", 4, (1950, 2030)),
                new DigitsToken("HH", "Two-digit hour (00-23)", 2, (0, 23)),
                new DigitsToken("mm", "Two-digit minute", 2, (0, 59)),
                new DigitsToken("ss", "Two-digit second", 2, (0, 59)),
                new ChoiceToken("AMPM", "AM/PM indicator", new[] { "AM", "PM" })
            })
        {
        }
    }
}