"""
Defines the queries available in the API.
"""
type Query {
  "Retrieves a project by its unique identifier."
  project(id: Int!): Project
  "Retrieves a list of projects, with optional filtering."
  projects(
    "Filter projects by name (contains match)."
    nameContains: String
    "Filter projects by the type of data source they contain."
    dataSourceType: DataSourceType
    "Filter projects by author (exact match)."
    author: String
  ): [Project]
  "Retrieves a data source by its unique identifier."
  dataSource(id: Int!): DataSource
  "Retrieves a list of data sources for a given project."
  dataSources(projectId: Int!): [DataSource]
  "Retrieves an entity by its unique identifier."
  entity(id: Int!): Entity
  "Retrieves a list of entities for a given data source."
  entities(dataSourceId: Int!): [Entity]
  "Retrieves a field by its unique identifier."
  field(id: Int!): Field
  "Retrieves a list of fields for a given entity."
  fields(entityId: Int!): [Field]
}

"""
Defines the mutations available in the API for creating, updating, and deleting data.
"""
type Mutation {
  "Creates a new project."
  createProject(name: String!, description: String, author: String): Project
  "Updates an existing project."
  updateProject(
    id: Int!
    name: String
    description: String
    author: String
  ): Project
  "Deletes a project."
  deleteProject(id: Int!): Boolean

  "Creates a new data source."
  createDataSource(
    projectId: Int!
    name: String!
    description: String
    type: DataSourceType!
    provider: DataSourceProvider!
    connectionString: String!
  ): DataSource
  "Updates an existing data source."
  updateDataSource(
    id: Int!
    name: String
    description: String
    type: DataSourceType
    provider: DataSourceProvider
    connectionString: String
  ): DataSource
  "Deletes a data source."
  deleteDataSource(id: Int!): Boolean

  "Creates a new entity."
  createEntity(
    dataSourceId: Int!
    name: String!
    schema: String
    syncStrategy: SyncStrategy
  ): Entity
  "Updates an existing entity."
  updateEntity(
    id: Int!
    name: String
    schema: String
    syncStrategy: SyncStrategy
  ): Entity
  "Deletes an entity."
  deleteEntity(id: Int!): Boolean

  "Creates a new field."
  createField(
    entityId: Int!
    name: String!
    type: DataType!
    nativeType: String
    maxLength: Int
    isNullable: Boolean!
    isPrimaryKey: Boolean!
    isIdentity: Boolean!
    isUnique: Boolean!
  ): Field
  "Updates an existing field."
  updateField(
    id: Int!
    name: String
    type: DataType
    nativeType: String
    maxLength: Int
    isNullable: Boolean
    isPrimaryKey: Boolean
    isIdentity: Boolean
    isUnique: Boolean
  ): Field
  "Deletes a field."
  deleteField(id: Int!): Boolean

  "Creates a new field setting."
  createFieldSetting(
    fieldId: Int!
    type: GeneratorType!
    name: String
    settings: String
  ): FieldSetting
  "Updates an existing field setting."
  updateFieldSetting(
    fieldId: Int!
    type: GeneratorType
    name: String
    settings: String
  ): FieldSetting
  "Deletes a field setting."
  deleteFieldSetting(fieldId: Int!): Boolean

  "Creates a new key constraint."
  createKeyConstraint(
    entityId: Int!
    keyType: KeyType!
    columns: [String!]!
    referencedEntity: String
    referencedColumns: [String!]
  ): KeyConstraint
  "Updates an existing key constraint."
  updateKeyConstraint(
    id: Int!
    keyType: KeyType
    columns: [String!]
    referencedEntity: String
    referencedColumns: [String!]
  ): KeyConstraint
  "Deletes a key constraint."
  deleteKeyConstraint(id: Int!): Boolean

  "Creates a new relationship."
  createRelationship(
    sourceEntityId: Int!
    sourceEntity: String
    sourceField: String
    targetEntity: String
    targetField: String
    cardinality: Cardinality!
  ): Relationship
  "Updates an existing relationship."
  updateRelationship(
    id: Int!
    sourceEntity: String
    sourceField: String
    targetEntity: String
    targetField: String
    cardinality: Cardinality
  ): Relationship
  "Deletes a relationship."
  deleteRelationship(id: Int!): Boolean
}

"""
Represents a project configuration, containing multiple data sources.
"""
type Project {
  "The unique identifier of the project."
  id: Int!
  "The name of the project."
  name: String!
  "The description of the project."
  description: String
  "The author of the project."
  author: String
  "The creation date of the project."
  createdDate: String!
  "The data sources associated with the project."
  dataSources: [DataSource]
}

"""
Represents a data source, containing its metadata and extracted entities.
"""
type DataSource {
  "The unique identifier of the data source."
  id: Int!
  "The identifier of the project this data source belongs to."
  projectId: Int!
  "The name of the data source."
  name: String!
  "The description of the data source."
  description: String
  "The type of the data source."
  type: DataSourceType!
  "The provider of the data source."
  provider: DataSourceProvider!
  "The connection string for the data source."
  connectionString: String!
  "The project this data source belongs to."
  project: Project
  "The entities found in this data source."
  entities: [Entity]
}

"""
Represents an entity (e.g., a table in a relational database, a collection in NoSQL, or a sheet/file).
"""
type Entity {
  "The unique identifier of the entity."
  id: Int!
  "The identifier of the data source this entity belongs to."
  dataSourceId: Int!
  "The name of the entity."
  name: String
  "The schema name of the entity."
  schema: String
  "The synchronization strategy for the entity."
  syncStrategy: SyncStrategy
  "The data source this entity belongs to."
  dataSource: DataSource
  "The fields (columns) belonging to this entity."
  fields: [Field]
  "The key constraints defined for this entity."
  keyConstraints: [KeyConstraint]
  "The relationships this entity participates in."
  relationships: [Relationship]
}

"""
Represents a field (column) within an entity.
"""
type Field {
  "The unique identifier of the field."
  id: Int!
  "The identifier of the entity this field belongs to."
  entityId: Int!
  "The name of the field."
  name: String
  "The inferred data type of the field."
  type: DataType!
  "The native data type name from the source system."
  nativeType: String
  "The maximum length of the field, if applicable."
  maxLength: Int
  "Indicates whether the field can contain null values."
  isNullable: Boolean!
  "Indicates whether the field is part of the primary key."
  isPrimaryKey: Boolean!
  "Indicates whether the field is an identity (auto-incrementing) column."
  isIdentity: Boolean!
  "Indicates whether the field has a unique constraint."
  isUnique: Boolean!
  "The entity this field belongs to."
  entity: Entity
  "The data generation settings for the field."
  fieldSetting: FieldSetting
}

"""
Represents the data generation settings for a field.
"""
type FieldSetting {
  "The identifier of the field these settings belong to."
  fieldId: Int!
  "The type of generator to use for this field."
  type: GeneratorType!
  "The name of the generator."
  name: String
  "The settings for the generator."
  settings: String
  "The field these settings belong to."
  field: Field
}

"""
Represents a key constraint (Primary or Foreign) within an entity.
"""
type KeyConstraint {
  "The unique identifier of the key constraint."
  id: Int!
  "The identifier of the entity this key constraint belongs to."
  entityId: Int!
  "The type of the key (Primary or Foreign)."
  keyType: KeyType!
  "The list of column names that form this key constraint."
  columns: [String!]!
  "The name of the entity that this key references (for foreign keys)."
  referencedEntity: String
  "The list of column names in the referenced entity (for foreign keys)."
  referencedColumns: [String!]
  "The entity this key constraint belongs to."
  entity: Entity
}

"""
Represents a relationship between two entities.
"""
type Relationship {
  "The unique identifier of the relationship."
  id: Int!
  "The identifier of the source entity in the relationship."
  sourceEntityId: Int!
  "The name of the source entity."
  sourceEntity: String
  "The name of the field in the source entity that participates in the relationship."
  sourceField: String
  "The name of the target entity in the relationship."
  targetEntity: String
  "The name of the field in the target entity that participates in the relationship."
  targetField: String
  "The cardinality of the relationship."
  cardinality: Cardinality!
}

"""
Defines the general category of a data source.
"""
enum DataSourceType {
  "File-based data source (e.g., CSV, Excel)."
  File
  "Relational database (e.g., SQL Server, SQLite)."
  RelationalDatabase
  "NoSQL database (e.g., MongoDB)."
  NoSQL
}

"""
Defines the specific provider for a data source.
"""
enum DataSourceProvider {
  "Comma Separated Values file."
  CSV
  "Microsoft SQL Server database."
  SQLServer
  "SQLite database."
  SQLite
  "MongoDB database."
  MongoDB
  "Microsoft Excel file."
  Excel
  "Neo4j graph database."
  Neo4j
}

"""
Defines the common data types supported for schema extraction.
"""
enum DataType {
  "A 64-bit signed integer."
  Integer
  "A fixed-point number."
  Decimal
  "A sequence of characters."
  String
  "A date and time value."
  DateTime
  "A boolean value (true or false)."
  Boolean
  "A binary data blob."
  Binary
  "An unsupported data type."
  Unsupported
}

"""
Defines the type of a generator.
"""
enum GeneratorType {
  "No generator."
  None
  "Auto-incrementing integer generator."
  AutoIncrement
  "Random value generator."
  Random
  "Generator that chooses a value from a list."
  Choice
  "Generator that generates a value relative to another field."
  Relative
  "Generator that references a value from another field."
  Reference
  "Generator that uses a pattern to generate a value."
  Pattern
  "Generator that uses a C# script to generate a value."
  Script
  "Generator that uses AI to generate a value."
  AI
  "Generator that uses a function to generate a value."
  Function
}

"""
Defines the type of a key constraint (Primary or Foreign).
"""
enum KeyType {
  "Primary key."
  Primary
  "Foreign key."
  Foreign
}

"""
Defines the cardinality of a relationship between entities.
"""
enum Cardinality {
  "One-to-one relationship."
  OneToOne
  "One-to-many relationship."
  OneToMany
  "Many-to-one relationship."
  ManyToOne
}

"""
Defines the synchronization strategy for an entity.
"""
enum SyncStrategy {
  "The entity is read-only."
  Readonly
  "The entity will be truncated and then new data will be inserted."
  TruncateAndInsert
  "New data will be appended to the entity."
  Append
}
