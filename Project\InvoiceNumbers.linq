<Query Kind="Statements">
  <Reference Relative="..\TestDataGenerator.Generators\bin\Debug\net8.0\TestDataGenerator.Core.dll">&lt;UserProfile&gt;\source\repos\TestDataGenerator\TestDataGenerator.Generators\bin\Debug\net8.0\TestDataGenerator.Core.dll</Reference>
  <Reference Relative="..\TestDataGenerator.Generators\bin\Debug\net8.0\TestDataGenerator.Generators.dll">&lt;UserProfile&gt;\source\repos\TestDataGenerator\TestDataGenerator.Generators\bin\Debug\net8.0\TestDataGenerator.Generators.dll</Reference>
  <Namespace>System.Globalization</Namespace>
  <Namespace>TestDataGenerator.Core.Interfaces</Namespace>
  <Namespace>TestDataGenerator.Core.Enums</Namespace>
  <Namespace>TestDataGenerator.Core</Namespace>
  <Namespace>TestDataGenerator.Generators</Namespace>
  <Namespace>TestDataGenerator.Generators.Functions</Namespace>
</Query>

var context = new DataGeneratorContext { Random = new Random(), RowIndex = 0 };

var pattern = new RuntimePattern("InvoiceNumber", "Invoice Number",
	"MT-{YYYY}{MM}{DD}-{XXXX}",
	new()
	{
		new DigitsToken("YYYY", "Year", 4, (1900, 2050)),
		new DigitsToken("MM", "Month", 2),
		new DigitsToken("DD", "Day", 2),
		new DigitsToken("XXXX", "Sequence", 4),
	});

pattern.Generate(context).Dump("First generated value");
pattern.Generate(context).Dump("Second generated value");
pattern.Generate(context).Dump("...");