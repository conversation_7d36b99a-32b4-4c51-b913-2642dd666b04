namespace Aubrant.TestDataGenerator.SchemaProfiler.Enums
{
    /// <summary>
    /// Defines the logging levels for messages.
    /// </summary>
    [Flags]
    public enum LogLevel
    {
        /// <summary>
        /// No logging.
        /// </summary>
        None = 0,
        /// <summary>
        /// Error messages only.
        /// </summary>
        Error = 1,
        /// <summary>
        /// Warning messages.
        /// </summary>
        Warning = 2,
        /// <summary>
        /// Informational messages.
        /// </summary>
        Info = 4,
        /// <summary>
        /// All messages (Error, Warning, Info).
        /// </summary>
        All = Error | Warning | Info
    }
}