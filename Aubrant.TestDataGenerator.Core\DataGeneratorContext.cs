using System;
using System.Data;
using Aubrant.TestDataGenerator.Core.Interfaces;

namespace Aubrant.TestDataGenerator.Core
{

    /// <summary>
    /// Enhanced data generator context with relational integrity support
    /// </summary>
    public class DataGeneratorContext
    {
        /// <summary>
        /// Current row index being generated
        /// </summary>
        public int RowIndex { get; set; }

        /// <summary>
        /// Current data row being populated
        /// </summary>
        public DataRow Row { get; set; }

        /// <summary>
        /// Random instance for consistent randomization
        /// </summary>
        public Random Random { get; set; } = new Random();

        /// <summary>
        /// Reference data manager for cross-table relationships
        /// </summary>
        public IReferenceDataManager? ReferenceDataManager { get; set; }

        /// <summary>
        /// Current entity being generated
        /// </summary>
        public string? CurrentEntityName { get; set; }

        /// <summary>
        /// Current field being generated
        /// </summary>
        public string? CurrentFieldName { get; set; }

        /// <summary>
        /// Total number of rows being generated for current entity
        /// </summary>
        public int TotalRows { get; set; }

        /// <summary>
        /// Generation session ID for tracking related generations
        /// </summary>
        public Guid SessionId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Additional context data for complex scenarios
        /// </summary>
        public Dictionary<string, object> AdditionalContext { get; set; } = new();

        /// <summary>
        /// Gets a reference value from another entity
        /// </summary>
        /// <param name="entityName">Target entity name</param>
        /// <param name="fieldName">Target field name</param>
        /// <returns>Random reference value or null if not available</returns>
        public DataValue? GetReference(string entityName, string fieldName)
        {
            return ReferenceDataManager?.GetRandomReference(entityName, fieldName, Random);
        }

        /// <summary>
        /// Checks if reference data is available for the specified entity field
        /// </summary>
        /// <param name="entityName">Target entity name</param>
        /// <param name="fieldName">Target field name</param>
        /// <returns>True if reference data is available</returns>
        public bool HasReference(string entityName, string fieldName)
        {
            return ReferenceDataManager?.HasReferenceData(entityName, fieldName) ?? false;
        }

        /// <summary>
        /// Gets the count of available reference values
        /// </summary>
        /// <param name="entityName">Target entity name</param>
        /// <param name="fieldName">Target field name</param>
        /// <returns>Number of available reference values</returns>
        public int GetReferenceCount(string entityName, string fieldName)
        {
            return ReferenceDataManager?.GetReferenceCount(entityName, fieldName) ?? 0;
        }
    }
}