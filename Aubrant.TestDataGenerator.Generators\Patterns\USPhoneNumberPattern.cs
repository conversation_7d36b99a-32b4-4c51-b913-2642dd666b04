﻿using Aubrant.TestDataGenerator.Core.Patterns;

namespace Aubrant.TestDataGenerator.Generators.Patterns
{
    public class USPhoneNumberPattern : BasePattern
    {
        public USPhoneNumberPattern() : base(
            name: "US Phone Number",
            description: "Standard USA phone number with country and area code",
            pattern: "+{C} ({A}) {P}-{L}",
            tokens: new List<Token>
            {
            new ChoiceToken("C", "Country code", new[] { "1" }),
            new DigitsToken("A", "Area code", 3, (201, 281), (301, 386), (401, 484), (501, 586), (601, 689), (701, 787), (801, 878), (901, 989)),
            new DigitsToken("P", "Prefix", 3),
            new DigitsToken("L", "Line number", 4)
            })
        { }
    }
}
