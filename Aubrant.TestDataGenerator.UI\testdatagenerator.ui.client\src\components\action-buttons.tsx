import { type FC } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    Edit,
    Trash2,
    Play,
    Loader2,
} from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { TableCell } from "@/components/ui/table";
import type { Project } from "../types/database";

const GENERATE_ACTION_PREFIX = "generate-";
const DELETE_ACTION_PREFIX = "delete-";

type ActionButtonsProps = {
    project: Project;
    loadingActions: Record<string, boolean>;
    onGenerate: (project: Project) => void; 
    onEdit: (project: Project) => void;     
    onDelete: (project: Project) => void;
};

export const ActionButtons: FC<ActionButtonsProps> = ({ project, loadingActions, onGenerate, onEdit, onDelete }) => (
    <TableCell className="text-right py-2">
        <TooltipProvider>
            <div className="flex justify-end gap-2">
                <Tooltip>
                    <TooltipTrigger asChild>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onGenerate(project)}
                            disabled={loadingActions[`${GENERATE_ACTION_PREFIX}${project.Id}`]}
                            className="h-8 w-8 p-0 text-green-600 hover:text-green-700 hover:bg-green-50"
                        >
                            {loadingActions[`${GENERATE_ACTION_PREFIX}${project.Id}`] ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                                <Play className="h-4 w-4" />
                            )}
                        </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                        <p>Generate</p>
                    </TooltipContent>
                </Tooltip>
                <Tooltip>
                    <TooltipTrigger asChild>
                        <Button variant="outline" size="sm" onClick={() => onEdit(project)} className="h-8 w-8 p-0">
                            <Edit className="h-4 w-4" />
                        </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                        <p>Edit</p>
                    </TooltipContent>
                </Tooltip>
                <Tooltip>
                    <TooltipTrigger asChild>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onDelete(project)}
                            disabled={loadingActions[`${DELETE_ACTION_PREFIX}${project.Id}`]}
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                            {loadingActions[`${DELETE_ACTION_PREFIX}${project.Id}`] ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                                <Trash2 className="h-4 w-4" />
                            )}
                        </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                        <p>Delete</p>
                    </TooltipContent>
                </Tooltip>
            </div>
        </TooltipProvider>
    </TableCell>
);