
using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Functions;
using Bogus;

namespace Aubrant.TestDataGenerator.Generators.Functions
{
    /// <summary>
    /// A generator function that produces a random product or service review.
    /// </summary>
    public class ReviewFunction : BaseFunction
    {
        public override string Name => "Review";
        public override string Description => "Generates a random product or service review.";
        public override DataType ReturnType => DataType.String;

        /// <summary>
        /// Initializes a new instance of the ReviewFunction class.
        /// </summary>
        public ReviewFunction() : base("Text")
        {
            // No parameters needed for this function
        }

        /// <summary>
        /// Generates a random review using the Bogus library.
        /// </summary>
        public override DataValue Generate(DataGeneratorContext context)
        {
            var faker = new Faker();
            string review = faker.Rant.Review();
            return new DataValue(DataType.String, review);
        }
    }
}
