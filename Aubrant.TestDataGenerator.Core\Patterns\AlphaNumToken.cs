using Aubrant.TestDataGenerator.Core.Enums;
using System.Text.Json.Serialization;

namespace Aubrant.TestDataGenerator.Core.Patterns
{
    public class AlphaNumToken : Token
    {
        public int Length { get; }
        [JsonConverter(typeof(JsonStringEnumConverter))]
        public CaseOption Case { get; }

        [JsonConstructor]
        public AlphaNumToken(string name, int length, CaseOption @case = CaseOption.Random) : this(name, string.Empty, length, @case)
        {
        }

        public AlphaNumToken(string name, string description, int length, CaseOption caseOption = CaseOption.Random) : base(name, description)
        {
            if (length <= 0) throw new ArgumentException("Length must be positive.");
            Length = length;
            Case = caseOption;
        }

        public override DataValue Generate(DataGeneratorContext context)
        {
            var chars = new char[Length];
            var allowedLetters = Case switch
            {
                CaseOption.Upper => "ABCDEFGHIJKLMNOPQRSTUVWXYZ",
                CaseOption.Lower => "abcdefghijklmnopqrstuvwxyz",
                _ => "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
            };

            var allowed = "0123456789" + allowedLetters;
            for (int i = 0; i < Length; i++)
            {
                chars[i] = allowed[context.Random.Next(allowed.Length)];
            }

            return new DataValue(DataType.String, new string(chars));
        }
    }
}