"use client"

import type React from "react"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Moon, Sun, Layout, ArrowUpDown, Snowflake, Grid3X3 } from "lucide-react"

interface DiagramHeaderProps {
  isDarkMode: boolean
  onToggleDarkMode: () => void
  onArrangeLeftRight: () => void
  onArrangeSnowflake: () => void
  onArrangeCompact: () => void
  scale: number
  onZoomIn: () => void
  onZoomOut: () => void
  onZoomTo100: () => void
  onFitToWindow: () => void;
  onZoomToSelection: () => void;
}

export const DiagramHeader: React.FC<DiagramHeaderProps> = ({
  isDarkMode,
  onToggleDarkMode,
  onArrangeLeftRight,
  onArrangeSnowflake,
  onArrangeCompact,
  scale,
  onZoomIn,
  onZoomOut,
  onZoomTo100,
  onFitToWindow,
  onZoomToSelection,
}) => {
  return (
    <div className="border-b p-4 flex items-center justify-between bg-blue-50 dark:bg-blue-900/20">
      <div className="flex items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">Database Diagram Tool</h1>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="flex items-center gap-2 bg-transparent">
              <Layout className="h-4 w-4" />
              Auto Arrange
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-56">
            <DropdownMenuItem onClick={onArrangeLeftRight} className="flex items-center gap-2">
              <ArrowUpDown className="h-4 w-4" />
              <div>
                <div className="font-medium">Left-Right</div>
                <div className="text-xs text-muted-foreground">Based on relationship direction</div>
              </div>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={onArrangeSnowflake} className="flex items-center gap-2">
              <Snowflake className="h-4 w-4" />
              <div>
                <div className="font-medium">Snowflake</div>
                <div className="text-xs text-muted-foreground">Most connected tables in center</div>
              </div>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={onArrangeCompact} className="flex items-center gap-2">
              <Grid3X3 className="h-4 w-4" />
              <div>
                <div className="font-medium">Compact</div>
                <div className="text-xs text-muted-foreground">Rectangle grid layout</div>
              </div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <div className="flex items-center gap-2 ml-4">
          <div className="flex items-center gap-1 border rounded-md p-1">
            <Button variant="ghost" size="sm" onClick={onZoomOut} className="h-8 w-8 p-0" title="Zoom Out">
              <span className="text-lg font-bold">−</span>
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 min-w-[60px] px-2 text-sm font-medium hover:bg-muted/50"
                >
                  {Math.round(scale * 100)}%
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="center" className="w-48">
                <DropdownMenuItem onClick={onZoomTo100} className="flex items-center justify-center">
                  100%
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onFitToWindow} className="flex items-center justify-center">
                  Fit to Window
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onZoomToSelection} className="flex items-center justify-center">
                  Zoom to Selection
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Button variant="ghost" size="sm" onClick={onZoomIn} className="h-8 w-8 p-0" title="Zoom In">
              <span className="text-lg font-bold">+</span>
            </Button>
          </div>
        </div>
      </div>

      <Button variant="outline" size="icon" onClick={onToggleDarkMode}>
        {isDarkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
      </Button>
    </div>
  )
}
