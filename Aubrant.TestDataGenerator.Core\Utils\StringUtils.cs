﻿namespace Aubrant.TestDataGenerator.Core.Utils
{
    public static class StringUtils
    {
        public static string ReplaceFirst(this string text, string search, string replace, StringComparison comparisonType)
        {
            int pos = text.IndexOf(search, comparisonType);

            if (pos < 0)
            {
                return text;
            }

            return text.Substring(0, pos) + replace + text.Substring(pos + search.Length);
        }
    }
}
