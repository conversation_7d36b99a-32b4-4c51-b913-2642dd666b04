﻿using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Interfaces;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Aubrant.TestDataGenerator.Generators
{ /// <summary>
  /// Generator that creates foreign key references to existing primary key values
  /// </summary>
    public class ReferenceGenerator : IRuntimeGenerator
    {
        public string Name => "Reference";

        public string Description => "Generates foreign key values by referencing existing primary key values from other tables, maintaining referential integrity across related entities.";

        public DataType ReturnType => DataType.Any;

        /// <summary>
        /// Target entity name to reference
        /// </summary>
        [Description("Name of the target entity (table) to reference")]
        public string TargetEntity { get; set; } = string.Empty;

        /// <summary>
        /// Target field name to reference (usually primary key)
        /// </summary>
        [Description("Name of the target field to reference (usually primary key)")]
        public string TargetField { get; set; } = string.Empty;

        /// <summary>
        /// Probability of generating null values (0-100)
        /// </summary>
        [Description("Probability of generating null values (0-100 percent)")]
        public int NullProbability { get; set; } = 0;

        /// <summary>
        /// Whether to allow duplicate references
        /// </summary>
        [Description("Whether to allow duplicate foreign key references")]
        public bool AllowDuplicates { get; set; } = true;

        /// <summary>
        /// Distribution strategy for reference selection
        /// </summary>
        [Description("Strategy for selecting reference values")]
        public ReferenceDistributionStrategy DistributionStrategy { get; set; } = ReferenceDistributionStrategy.Uniform;

        /// <summary>
        /// Parameterless constructor for semantic analyzer
        /// </summary>
        public ReferenceGenerator()
        {
        }

        /// <summary>
        /// Constructor with target specification
        /// </summary>
        /// <param name="targetEntity">Target entity name</param>
        /// <param name="targetField">Target field name</param>
        /// <param name="nullProbability">Probability of null values (0-100)</param>
        public ReferenceGenerator(string targetEntity, string targetField, int nullProbability = 0)
        {
            TargetEntity = targetEntity ?? throw new ArgumentNullException(nameof(targetEntity));
            TargetField = targetField ?? throw new ArgumentNullException(nameof(targetField));
            NullProbability = Math.Clamp(nullProbability, 0, 100);
        }

        /// <summary>
        /// Parses generator settings from configuration
        /// </summary>
        /// <param name="dataType">Expected data type</param>
        /// <param name="settings">Configuration settings</param>
        public void Parse(DataType dataType, IDictionary<string, object> settings)
        {
            if (settings != null)
            {
                if (settings.TryGetValue("TargetEntity", out var targetEntity))
                    TargetEntity = targetEntity?.ToString() ?? string.Empty;

                if (settings.TryGetValue("TargetField", out var targetField))
                    TargetField = targetField?.ToString() ?? string.Empty;

                if (settings.TryGetValue("NullProbability", out var nullProbability))
                {
                    if (int.TryParse(nullProbability?.ToString(), out var probability))
                        NullProbability = Math.Clamp(probability, 0, 100);
                }

                if (settings.TryGetValue("AllowDuplicates", out var allowDuplicates))
                {
                    if (bool.TryParse(allowDuplicates?.ToString(), out var allow))
                        AllowDuplicates = allow;
                }
            }
        }

        /// <summary>
        /// Generates a reference value
        /// </summary>
        /// <param name="context">Data generation context</param>
        /// <returns>Generated reference value</returns>
        public DataValue Generate(DataGeneratorContext context)
        {
            if (context == null)
                throw new ArgumentNullException(nameof(context));

            // Check for null generation
            if (NullProbability > 0 && context.Random.Next(100) < NullProbability)
            {
                return new DataValue(DBNull.Value);
            }

            // Validate reference data manager
            if (context.ReferenceDataManager == null)
            {
                throw new InvalidOperationException("ReferenceDataManager is not available in the context. Ensure the data generation pipeline is properly configured for relational integrity.");
            }

            // Check if reference data is available
            if (!context.ReferenceDataManager.HasReferenceData(TargetEntity, TargetField))
            {
                if (NullProbability == 0)
                {
                    throw new InvalidOperationException($"No reference data available for {TargetEntity}.{TargetField}. Ensure the target entity is generated before this entity, or increase NullProbability to handle missing references.");
                }

                // Return null if no reference data and nulls are allowed
                return new DataValue(DBNull.Value);
            }

            // Get reference value based on distribution strategy
            var referenceValue = DistributionStrategy switch
            {
                ReferenceDistributionStrategy.Uniform => GetUniformReference(context),
                ReferenceDistributionStrategy.Weighted => GetWeightedReference(context),
                ReferenceDistributionStrategy.Sequential => GetSequentialReference(context),
                _ => GetUniformReference(context)
            };

            return referenceValue ?? new DataValue(DBNull.Value);
        }

        private DataValue? GetUniformReference(DataGeneratorContext context)
        {
            return context.ReferenceDataManager!.GetRandomReference(TargetEntity, TargetField, context.Random);
        }

        private DataValue? GetWeightedReference(DataGeneratorContext context)
        {
            // For weighted distribution, favor more recent values
            var allReferences = context.ReferenceDataManager!.GetAllReferences(TargetEntity, TargetField).ToList();
            if (!allReferences.Any()) return null;

            // Simple weighted approach: higher probability for later values
            var totalCount = allReferences.Count;
            var weights = Enumerable.Range(1, totalCount).Select(i => (double)i).ToArray();
            var totalWeight = weights.Sum();

            var randomValue = context.Random.NextDouble() * totalWeight;
            var cumulativeWeight = 0.0;

            for (int i = 0; i < allReferences.Count; i++)
            {
                cumulativeWeight += weights[i];
                if (randomValue <= cumulativeWeight)
                {
                    return allReferences[i];
                }
            }

            return allReferences.Last();
        }

        private DataValue? GetSequentialReference(DataGeneratorContext context)
        {
            // For sequential distribution, cycle through references in order
            var allReferences = context.ReferenceDataManager!.GetAllReferences(TargetEntity, TargetField).ToList();
            if (!allReferences.Any()) return null;

            var index = context.RowIndex % allReferences.Count;
            return allReferences[index];
        }

        /// <summary>
        /// Validates the generator configuration
        /// </summary>
        /// <returns>Validation result</returns>
        public ReferenceGeneratorValidationResult Validate()
        {
            var result = new ReferenceGeneratorValidationResult();

            if (string.IsNullOrWhiteSpace(TargetEntity))
            {
                result.Errors.Add("TargetEntity is required");
            }

            if (string.IsNullOrWhiteSpace(TargetField))
            {
                result.Errors.Add("TargetField is required");
            }

            if (NullProbability < 0 || NullProbability > 100)
            {
                result.Errors.Add("NullProbability must be between 0 and 100");
            }

            result.IsValid = !result.Errors.Any();
            return result;
        }
    }

    /// <summary>
    /// Strategies for reference value distribution
    /// </summary>
    public enum ReferenceDistributionStrategy
    {
        /// <summary>
        /// Uniform random distribution across all available references
        /// </summary>
        Uniform,

        /// <summary>
        /// Weighted distribution favoring more recent values
        /// </summary>
        Weighted,

        /// <summary>
        /// Sequential cycling through available references
        /// </summary>
        Sequential
    }

    /// <summary>
    /// Validation result for reference generator
    /// </summary>
    public class ReferenceGeneratorValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
    }
}
