using Aubrant.TestDataGenerator.Core.Enums;
using System.Text.Json.Serialization;

namespace Aubrant.TestDataGenerator.Core.Patterns
{
    public class ChoiceToken : Token
    {
        public string[] Options { get; }

        [JsonConstructor]
        public ChoiceToken(string name, string[] options) : this(name, string.Empty, options)
        {
        }

        public ChoiceToken(string name, string description, string[] options) : base(name, description)
        {
            Options = options ?? throw new ArgumentNullException(nameof(options));
        }

        public override DataValue Generate(DataGeneratorContext context)
        {
            var value = Options[context.Random.Next(Options.Length)];
            return new DataValue(DataType.String, value);
        }
    }
}