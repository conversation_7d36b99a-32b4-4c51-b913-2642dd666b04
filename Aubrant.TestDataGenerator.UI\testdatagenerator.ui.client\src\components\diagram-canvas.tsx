"use client"

import type React from "react"
import { EntityCard } from "./entity-card"
import { RelationshipConnections } from "./relationship-connections"
import { processRelationships } from "../../utils/relationship-utils"
import type { Entity, Relationship, ProcessedRelationship, Field } from "../types/database";

interface DiagramCanvasProps {
  entities: Entity[]
  selectedEntities: string[]
  activeEntity: string | null
  selectedField: Field | null
  selectedRelationship: {
    relationship: Relationship
    sourceEntity: Entity
    targetEntity: Entity
  } | null
  marquee: { x: number; y: number; width: number; height: number } | null
  panOffset: { x: number; y: number }
  scale: number
  alignmentGuides: { x?: number; y?: number }
  canvasRef: React.RefObject<HTMLDivElement>
  svgRef: React.RefObject<SVGSVGElement>
  handleMouseDown: (e: React.MouseEvent) => void
  handleMouseMove: (e: React.MouseEvent) => void
  handleMouseUp: () => void
  handleWheel: (e: React.WheelEvent) => void
  handleEntityClick: (entity: Entity, e: React.MouseEvent) => void
  handleFieldClick: (field: Field, entity: Entity) => void
  handleRelationshipClick: (processedRelationship: ProcessedRelationship, e: React.MouseEvent) => void
  handleEntityMouseDown: (entityName: string, e: React.MouseEvent) => void
}

export function DiagramCanvas({
  entities,
  selectedEntities,
  activeEntity,
  selectedField,
  selectedRelationship,
  panOffset,
  scale,
  alignmentGuides,
  marquee,
  canvasRef,
  svgRef,
  handleMouseDown,
  handleMouseMove,
  handleMouseUp,
  handleWheel,
  handleEntityClick,
  handleFieldClick,
  handleRelationshipClick,
  handleEntityMouseDown,
}: DiagramCanvasProps) {
  const processedRelationships = processRelationships(entities, selectedRelationship)

  return (
    <div className="flex-1 relative overflow-hidden">
      <div
        ref={canvasRef}
        className={`w-full h-full bg-muted/20 ${marquee ? "cursor-crosshair" : "cursor-grab active:cursor-grabbing"}`}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onWheel={handleWheel}
      >
        <div
          className="relative"
          style={{
            transform: `translate(${panOffset.x}px, ${panOffset.y}px) scale(${scale})`,
            transformOrigin: "0 0",
          }}
        >
          {/* SVG for connections */}
          <svg
            ref={svgRef}
            className="absolute inset-0 z-0"
            style={{
              width: "100%",
              height: "100%",
              minWidth: "2000px",
              minHeight: "2000px",
              overflow: "visible",
            }}
          >
            <RelationshipConnections
              processedRelationships={processedRelationships}
              onRelationshipClick={handleRelationshipClick}
            />

            {/* Alignment guides */}
            {alignmentGuides.x !== undefined && (
              <line
                x1={alignmentGuides.x}
                y1={0}
                x2={alignmentGuides.x}
                y2={2000}
                stroke="#3b82f6"
                strokeWidth="1"
                strokeDasharray="5,5"
                opacity="0.7"
              />
            )}
            {alignmentGuides.y !== undefined && (
              <line
                x1={0}
                y1={alignmentGuides.y}
                x2={2000}
                y2={alignmentGuides.y}
                stroke="#3b82f6"
                strokeWidth="1"
                strokeDasharray="5,5"
                opacity="0.7"
              />
            )}
          </svg>

          {/* Entity Tables */}
          {entities.map((entity) => (
            <EntityCard
              key={entity.Name}
              entity={entity}
              isSelected={selectedEntities.includes(entity.Name)}
              isActive={activeEntity === entity.Name}
              selectedField={selectedField}
              onEntityClick={(entity, e) => handleEntityClick(entity, e)}
              onFieldClick={handleFieldClick}
              onMouseDown={handleEntityMouseDown}
            />
          ))}

          {marquee && (
            <div
              className="absolute border border-blue-500 bg-blue-500/20 pointer-events-none z-20"
              style={{
                left: marquee.x,
                top: marquee.y,
                width: marquee.width,
                height: marquee.height,
              }}
            />
          )}
        </div>
      </div>
    </div>
  )
}