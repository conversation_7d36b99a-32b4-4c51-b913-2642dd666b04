using Aubrant.TestDataGenerator.Core.Enums;
using Aubrant.TestDataGenerator.Core.Interfaces;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Aubrant.TestDataGenerator.Core.Functions
{
    public abstract class BaseFunction : IRuntimeGenerator
    {
        public abstract string Name { get; }
        public abstract string Description { get; }
        public string Category { get; }
        public ParameterCollection Parameters { get; } = new();
        [JsonConverter(typeof(JsonStringEnumConverter))]
        public abstract DataType ReturnType { get; }

        protected BaseFunction(string category)
        {
            Category = category;
        }

        public abstract DataValue Generate(DataGeneratorContext context);

        public void Parse(DataType dataType, IDictionary<string, object> settings)
        {
            foreach (var kvp in settings)
            {
                // Find the parameter as defined by the function
                if (Parameters.TryGet(kvp.Key, out var parameter))
                {
                    if (kvp.Value is JsonElement element)
                    {
                        // Check for a field reference like {"Field": "FieldName"}
                        if (element.ValueKind == JsonValueKind.Object && element.TryGetProperty("Field", out var fieldElement))
                        {
                            parameter.Field = fieldElement.GetString();
                        }
                        else
                        {
                            // Otherwise, it's a constant value
                            JsonElement valueElement = element;
                            // Check for the explicit value wrapper like {"Value": "SomeValue"}
                            if (element.ValueKind == JsonValueKind.Object && element.TryGetProperty("Value", out var valueProp))
                            {
                                valueElement = valueProp;
                            }

                            // Deserialize the JSON value into the system type corresponding to the parameter's data type
                            var deserializedValue = JsonSerializer.Deserialize(valueElement.GetRawText(), GetSystemType(parameter.Type));
                            parameter.Value = new DataValue(parameter.Type, deserializedValue);
                        }
                    }
                }
            }
        }

        // Helper method to map DataType enum to a C# System.Type
        private Type GetSystemType(DataType dataType)
        {
            return dataType switch
            {
                DataType.Integer => typeof(long),
                DataType.Decimal => typeof(decimal),
                DataType.DateTime => typeof(DateTime),
                DataType.Boolean => typeof(bool),
                DataType.String => typeof(string),
                DataType.Binary => typeof(byte[]),
                _ => typeof(object),
            };
        }
    }
}