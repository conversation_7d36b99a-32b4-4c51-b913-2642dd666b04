using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.UI.Server.GraphQL;
using Aubrant.TestDataGenerator.UI.Server.Mutations;
using Microsoft.EntityFrameworkCore;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddDbContext<DatabaseContext>(options =>
    options.UseSqlite(builder.Configuration.GetConnectionString("DefaultConnection")));

builder.Services
    .AddGraphQLServer()
    .AddFiltering()
    .AddSorting()
    .AddProjections()
    .AddQueryType<Query>()
    .AddMutationType<Mutation>()
    .AddTypeExtension<ProjectMutations>()
    .AddTypeExtension<DataSourceMutations>()
    .AddTypeExtension<EntityMutations>()
    .AddTypeExtension<FieldMutations>()
    .AddTypeExtension<FieldSettingMutations>()
    .AddTypeExtension<KeyConstraintMutations>()
    .AddTypeExtension<RelationshipMutations>();

builder.Services.AddAuthorization();

var app = builder.Build();

app.UseHttpsRedirection();
app.UseRouting();
app.UseAuthorization();

if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}

app.MapGraphQL();
app.UseDefaultFiles();
app.UseStaticFiles();
app.MapFallbackToFile("index.html");

app.Run();