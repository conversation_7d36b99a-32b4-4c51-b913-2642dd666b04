﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Aubrant.TestDataGenerator.Core.Interfaces;

namespace Aubrant.TestDataGenerator.Data.ReferentialIntegrity
{/// <summary>
 /// Interface for generating test data with relational integrity
 /// </summary>
    public interface IRelationalDataGenerator
    {
        /// <summary>
        /// Generates test data for all entities in the data source with proper relational integrity
        /// </summary>
        /// <param name="dataSource">Data source containing entities and relationships</param>
        /// <param name="options">Generation options</param>
        /// <returns>Generation result with statistics and generated data</returns>
        Task<RelationalGenerationResult> GenerateAsync(DataSource dataSource, RelationalGenerationOptions options);

        /// <summary>
        /// Generates test data for a specific entity with relational context
        /// </summary>
        /// <param name="entity">Entity to generate data for</param>
        /// <param name="rowCount">Number of rows to generate</param>
        /// <param name="context">Relational generation context</param>
        /// <returns>Generated DataTable</returns>
        Task<DataTable> GenerateEntityDataAsync(Entity entity, int rowCount, RelationalGenerationContext context);

        /// <summary>
        /// Validates that the data source can be generated with relational integrity
        /// </summary>
        /// <param name="dataSource">Data source to validate</param>
        /// <returns>Validation result</returns>
        RelationalValidationResult ValidateDataSource(DataSource dataSource);
    }

    /// <summary>
    /// Options for relational data generation
    /// </summary>
    public class RelationalGenerationOptions
    {
        /// <summary>
        /// Default number of rows to generate per entity
        /// </summary>
        public int DefaultRowCount { get; set; } = 100;

        /// <summary>
        /// Entity-specific row counts
        /// </summary>
        public Dictionary<string, int> EntityRowCounts { get; set; } = new();

        /// <summary>
        /// Random seed for reproducible generation
        /// </summary>
        public int? RandomSeed { get; set; }

        /// <summary>
        /// Maximum number of entities to process in parallel
        /// </summary>
        public int MaxParallelism { get; set; } = Environment.ProcessorCount;

        /// <summary>
        /// Whether to validate referential integrity after generation
        /// </summary>
        public bool ValidateIntegrityAfterGeneration { get; set; } = true;

        /// <summary>
        /// Strategy for handling circular references
        /// </summary>
        public CircularReferenceStrategy CircularReferenceStrategy { get; set; } = CircularReferenceStrategy.NullFirstThenUpdate;

        /// <summary>
        /// Whether to generate data for entities with no relationships
        /// </summary>
        public bool IncludeOrphanedEntities { get; set; } = true;

        /// <summary>
        /// Timeout for generation operations
        /// </summary>
        public TimeSpan Timeout { get; set; } = TimeSpan.FromMinutes(30);

        /// <summary>
        /// Gets the row count for a specific entity
        /// </summary>
        /// <param name="entityName">Entity name</param>
        /// <returns>Row count for the entity</returns>
        public int GetRowCount(string entityName)
        {
            return EntityRowCounts.TryGetValue(entityName, out var count) ? count : DefaultRowCount;
        }
    }

    /// <summary>
    /// Context for relational data generation
    /// </summary>
    public class RelationalGenerationContext
    {
        /// <summary>
        /// Dependency graph for the data source
        /// </summary>
        public DependencyGraph DependencyGraph { get; set; } = null!;

        /// <summary>
        /// Reference data manager
        /// </summary>
        public IReferenceDataManager ReferenceDataManager { get; set; } = null!;

        /// <summary>
        /// Generation options
        /// </summary>
        public RelationalGenerationOptions Options { get; set; } = null!;

        /// <summary>
        /// Random instance for consistent generation
        /// </summary>
        public Random Random { get; set; } = null!;

        /// <summary>
        /// Generation session ID
        /// </summary>
        public Guid SessionId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Entities that have been generated
        /// </summary>
        public HashSet<string> GeneratedEntities { get; set; } = new();

        /// <summary>
        /// Entities currently being generated (for circular reference detection)
        /// </summary>
        public HashSet<string> EntitiesInProgress { get; set; } = new();

        /// <summary>
        /// Generated data tables by entity name
        /// </summary>
        public Dictionary<string, DataTable> GeneratedTables { get; set; } = new();

        /// <summary>
        /// Generation statistics
        /// </summary>
        public RelationalGenerationStatistics Statistics { get; set; } = new();
    }

    /// <summary>
    /// Result of relational data generation
    /// </summary>
    public class RelationalGenerationResult
    {
        /// <summary>
        /// Whether generation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Error message if generation failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Generated data tables by entity name
        /// </summary>
        public Dictionary<string, DataTable> GeneratedTables { get; set; } = new();

        /// <summary>
        /// Generation statistics
        /// </summary>
        public RelationalGenerationStatistics Statistics { get; set; } = new();

        /// <summary>
        /// Dependency graph used for generation
        /// </summary>
        public DependencyGraph? DependencyGraph { get; set; }

        /// <summary>
        /// Validation result if integrity validation was performed
        /// </summary>
        public RelationalValidationResult? ValidationResult { get; set; }

        /// <summary>
        /// Total generation time
        /// </summary>
        public TimeSpan TotalDuration { get; set; }
    }

    /// <summary>
    /// Statistics for relational data generation
    /// </summary>
    public class RelationalGenerationStatistics
    {
        /// <summary>
        /// Total number of entities processed
        /// </summary>
        public int EntitiesProcessed { get; set; }

        /// <summary>
        /// Total number of rows generated
        /// </summary>
        public int TotalRowsGenerated { get; set; }

        /// <summary>
        /// Number of foreign key references created
        /// </summary>
        public int ForeignKeyReferencesCreated { get; set; }

        /// <summary>
        /// Number of circular references handled
        /// </summary>
        public int CircularReferencesHandled { get; set; }

        /// <summary>
        /// Number of null foreign keys generated
        /// </summary>
        public int NullForeignKeysGenerated { get; set; }

        /// <summary>
        /// Generation time per entity
        /// </summary>
        public Dictionary<string, TimeSpan> EntityGenerationTimes { get; set; } = new();

        /// <summary>
        /// Reference data statistics
        /// </summary>
        public ReferenceDataStatistics? ReferenceDataStatistics { get; set; }

        /// <summary>
        /// Average generation time per entity
        /// </summary>
        public TimeSpan AverageEntityGenerationTime =>
            EntitiesProcessed > 0 ? TimeSpan.FromTicks(EntityGenerationTimes.Values.Sum(t => t.Ticks) / EntitiesProcessed) : TimeSpan.Zero;

        /// <summary>
        /// Average rows per entity
        /// </summary>
        public double AverageRowsPerEntity =>
            EntitiesProcessed > 0 ? (double)TotalRowsGenerated / EntitiesProcessed : 0;
    }

    /// <summary>
    /// Validation result for relational data generation
    /// </summary>
    public class RelationalValidationResult
    {
        /// <summary>
        /// Whether validation passed
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Validation errors
        /// </summary>
        public List<string> Errors { get; set; } = new();

        /// <summary>
        /// Validation warnings
        /// </summary>
        public List<string> Warnings { get; set; } = new();

        /// <summary>
        /// Referential integrity violations found
        /// </summary>
        public List<IntegrityViolation> IntegrityViolations { get; set; } = new();

        /// <summary>
        /// Orphaned records (foreign keys pointing to non-existent records)
        /// </summary>
        public List<OrphanedRecord> OrphanedRecords { get; set; } = new();
    }

    /// <summary>
    /// Represents a referential integrity violation
    /// </summary>
    public class IntegrityViolation
    {
        /// <summary>
        /// Source entity name
        /// </summary>
        public string SourceEntity { get; set; } = null!;

        /// <summary>
        /// Source field name
        /// </summary>
        public string SourceField { get; set; } = null!;

        /// <summary>
        /// Target entity name
        /// </summary>
        public string TargetEntity { get; set; } = null!;

        /// <summary>
        /// Target field name
        /// </summary>
        public string TargetField { get; set; } = null!;

        /// <summary>
        /// Number of violations found
        /// </summary>
        public int ViolationCount { get; set; }

        /// <summary>
        /// Description of the violation
        /// </summary>
        public string Description { get; set; } = null!;
    }

    /// <summary>
    /// Represents an orphaned record
    /// </summary>
    public class OrphanedRecord
    {
        /// <summary>
        /// Entity containing the orphaned record
        /// </summary>
        public string EntityName { get; set; } = null!;

        /// <summary>
        /// Row index of the orphaned record
        /// </summary>
        public int RowIndex { get; set; }

        /// <summary>
        /// Foreign key field name
        /// </summary>
        public string ForeignKeyField { get; set; } = null!;

        /// <summary>
        /// Foreign key value that doesn't exist in target table
        /// </summary>
        public object ForeignKeyValue { get; set; } = null!;

        /// <summary>
        /// Target entity that should contain the referenced value
        /// </summary>
        public string TargetEntity { get; set; } = null!;
    }
}
