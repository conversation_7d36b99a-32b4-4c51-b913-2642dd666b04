"use client"

import type React from "react"
import type { ProcessedRelationship } from "../types/database";

interface DebugConnectionsProps {
  processedRelationships: ProcessedRelationship[]
  onRelationshipClick: (relationship: ProcessedRelationship, e: React.MouseEvent) => void
}

export const DebugConnections: React.FC<DebugConnectionsProps> = ({ processedRelationships, onRelationshipClick }) => {
  

  return (
    <>
      {processedRelationships.map((relationship, index) => {
        const { sourceEntity, targetEntity, cardinality } = relationship

        

        // Find field positions
        const sourceFieldIndex = sourceEntity.Fields.findIndex((f) => f.Name === relationship.relationship.SourceField)
        const targetFieldIndex = targetEntity.Fields.findIndex((f) => f.Name === relationship.relationship.TargetField)

        

        // Calculate exact positions
        const fieldHeight = 28
        const headerHeight = 70
        const entityWidth = 320

        const sourceY = sourceEntity.position.y + headerHeight + sourceFieldIndex * fieldHeight + 14
        const targetY = targetEntity.position.y + headerHeight + targetFieldIndex * fieldHeight + 14

        // Determine which side to connect from
        const sourceIsLeft = sourceEntity.position.x < targetEntity.position.x

        const sourceX = sourceIsLeft
          ? sourceEntity.position.x + entityWidth // Right edge
          : sourceEntity.position.x // Left edge

        const targetX = sourceIsLeft
          ? targetEntity.position.x // Left edge
          : targetEntity.position.x + entityWidth // Right edge

        // Create path
        const path = `M ${sourceX} ${sourceY} L ${targetX} ${targetY}`

        

        return (
          <g key={`connection-${index}`}>
            {/* Connection line */}
            <path
              d={path}
              stroke={relationship.isSelected ? "#3b82f6" : "#64748b"}
              strokeWidth="2"
              fill="none"
              className="cursor-pointer"
              onClick={(e) => onRelationshipClick(relationship, e)}
            />

            {/* Source cardinality label */}
            <text
              x={sourceX + (sourceIsLeft ? -20 : 20)}
              y={sourceY - 10}
              fontSize="16"
              fontWeight="bold"
              fill="#ff0000"
              textAnchor="middle"
              className="select-none pointer-events-none"
            >
              {cardinality.source.symbol}
            </text>

            {/* Target cardinality label */}
            <text
              x={targetX + (sourceIsLeft ? 20 : -20)}
              y={targetY - 10}
              fontSize="16"
              fontWeight="bold"
              fill="#ff0000"
              textAnchor="middle"
              className="select-none pointer-events-none"
            >
              {cardinality.target.symbol}
            </text>

            {/* Debug dots to show connection points */}
            <circle cx={sourceX} cy={sourceY} r="3" fill="#00ff00" />
            <circle cx={targetX} cy={targetY} r="3" fill="#00ff00" />
          </g>
        )
      })}
    </>
  )
}