import type { ApiResponse, PaginatedResponse, Project, CreateProjectRequest } from "./types"

// Mock data - same as current static data
const mockProjects: Project[] = [
  {
    id: "1",
    name: "Customer Analytics DB",
    dataSources: [
      { id: "ds1", name: "Main DB", type: "Relational Database", provider: "PostgreSQL" },
      { id: "ds2", name: "<PERSON><PERSON>", type: "NoSQL Database", provider: "MongoDB" },
    ],
    owner: "<PERSON>",
    creationDate: "2024-01-15",
    lastExecutionDate: "2024-01-20 14:30:22",
  },
  {
    id: "2",
    name: "E-commerce Product Catalog",
    dataSources: [
      { id: "ds3", name: "Product DB", type: "Relational Database", provider: "MySQL" },
      { id: "ds4", name: "Search Index", type: "NoSQL Database", provider: "Elastic Search" },
    ],
    owner: "<PERSON>",
    creationDate: "2024-01-10",
    lastExecutionDate: "2024-01-19 09:15:45",
  },
  // ... rest of mock data
]

export class ProjectsApi {
  /**
   * Get paginated list of projects
   */
  async getProjects(params?: {
    page?: number
    limit?: number
    search?: string
    owner?: string
    databaseType?: string
  }): Promise<PaginatedResponse<Project>> {
    // TODO: Replace with real API call
    // return apiClient.get<PaginatedResponse<Project>>(`/projects?${new URLSearchParams(params).toString()}`)

    // Mock implementation
    await this.delay(500) // Simulate network delay

    const { page = 1, limit = 10, search = "", owner = "", databaseType = "" } = params || {}

    let filteredProjects = mockProjects

    // Apply filters
    if (search) {
      filteredProjects = filteredProjects.filter((p) => p.name.toLowerCase().includes(search.toLowerCase()))
    }

    if (owner && owner !== "All Owners") {
      filteredProjects = filteredProjects.filter((p) => p.owner === owner)
    }

    if (databaseType && databaseType !== "All Database Types") {
      filteredProjects = filteredProjects.filter((p) => p.dataSources.some((ds) => ds.provider === databaseType))
    }

    // Apply pagination
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedData = filteredProjects.slice(startIndex, endIndex)

    return {
      success: true,
      data: paginatedData,
      pagination: {
        page,
        limit,
        total: filteredProjects.length,
        totalPages: Math.ceil(filteredProjects.length / limit),
      },
    }
  }

  /**
   * Get a single project by ID
   */
  async getProject(id: string): Promise<ApiResponse<Project>> {
    // TODO: Replace with real API call
    // return apiClient.get<ApiResponse<Project>>(`/projects/${id}`)

    await this.delay(300)

    const project = mockProjects.find((p) => p.id === id)

    if (!project) {
      return {
        success: false,
        error: "Project not found",
      }
    }

    return {
      success: true,
      data: project,
    }
  }

  /**
   * Create a new project
   */
  async createProject(projectData: CreateProjectRequest): Promise<ApiResponse<Project>> {
    // TODO: Replace with real API call
    // return apiClient.post<ApiResponse<Project>>('/projects', projectData)

    await this.delay(1000) // Simulate longer processing time

    const newProject: Project = {
      id: `proj_${Date.now()}`,
      name: projectData.name,
      dataSources: projectData.dataSources.map((ds) => ({
        id: ds.id,
        name: ds.name,
        type: ds.type,
        provider: ds.provider,
      })),
      owner: projectData.author,
      creationDate: new Date().toISOString().split("T")[0],
      lastExecutionDate: "Never",
    }

    // Add to mock data (in real app, this would be handled by the backend)
    mockProjects.unshift(newProject)

    return {
      success: true,
      data: newProject,
      message: "Project created successfully",
    }
  }

  /**
   * Update an existing project
   */
  async updateProject(id: string, projectData: Partial<CreateProjectRequest>): Promise<ApiResponse<Project>> {
    // TODO: Replace with real API call
    // return apiClient.put<ApiResponse<Project>>(`/projects/${id}`, projectData)

    await this.delay(800)

    const projectIndex = mockProjects.findIndex((p) => p.id === id)

    if (projectIndex === -1) {
      return {
        success: false,
        error: "Project not found",
      }
    }

    // Update mock data
    const updatedProject = {
      ...mockProjects[projectIndex],
      name: projectData.name || mockProjects[projectIndex].name,
      // Update other fields as needed
    }

    mockProjects[projectIndex] = updatedProject

    return {
      success: true,
      data: updatedProject,
      message: "Project updated successfully",
    }
  }

  /**
   * Delete a project
   */
  async deleteProject(id: string): Promise<ApiResponse<void>> {
    // TODO: Replace with real API call
    // return apiClient.delete<ApiResponse<void>>(`/projects/${id}`)

    await this.delay(500)

    const projectIndex = mockProjects.findIndex((p) => p.id === id)

    if (projectIndex === -1) {
      return {
        success: false,
        error: "Project not found",
      }
    }

    mockProjects.splice(projectIndex, 1)

    return {
      success: true,
      message: "Project deleted successfully",
    }
  }

  /**
   * Generate test data for a project
   */
  async generateTestData(id: string): Promise<ApiResponse<{ jobId: string }>> {
    // TODO: Replace with real API call
    // return apiClient.post<ApiResponse<{ jobId: string }>>(`/projects/${id}/generate`)

    await this.delay(1500)

    const project = mockProjects.find((p) => p.id === id)

    if (!project) {
      return {
        success: false,
        error: "Project not found",
      }
    }

    // Update last execution date
    project.lastExecutionDate = new Date().toLocaleString()

    return {
      success: true,
      data: { jobId: `job_${Date.now()}` },
      message: "Test data generation started",
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }
}

export const projectsApi = new ProjectsApi()
