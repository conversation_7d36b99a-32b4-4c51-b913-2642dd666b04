namespace Aubrant.TestDataGenerator.SchemaProfiler.Common
{
    /// <summary>
    /// Represents options for schema extraction processes.
    /// </summary>
    public class SchemaExtractionOptions
    {
        /// <summary>
        /// Gets or sets the number of sample records to read for schema inference.
        /// Default is 100.
        /// </summary>
        public int SampleSize { get; set; } = 100;
    }
}