using Aubrant.TestDataGenerator.Data;
using Aubrant.TestDataGenerator.Data.Enums;
using Aubrant.TestDataGenerator.UI.Server.GraphQL;

namespace Aubrant.TestDataGenerator.UI.Server.Mutations
{
    [ExtendObjectType(typeof(Mutation))]
    public class EntityMutations
    {
        public async Task<Entity> CreateEntity(int dataSourceId, string name, string? schema, SyncStrategy? syncStrategy, [Service] DatabaseContext context)
        {
            var entity = new Entity
            {
                DataSourceId = dataSourceId,
                Name = name,
                Schema = schema,
                SyncStrategy = syncStrategy
            };

            context.Entities.Add(entity);
            await context.SaveChangesAsync();

            return entity;
        }

        public async Task<Entity> UpdateEntity(int id, string? name, string? schema, SyncStrategy? syncStrategy, [Service] DatabaseContext context)
        {
            var entity = await context.Entities.FindAsync(id);

            if (entity == null)
            {
                throw new System.Exception("Entity not found.");
            }

            if (name != null)
            {
                entity.Name = name;
            }

            if (schema != null)
            {
                entity.Schema = schema;
            }

            if (syncStrategy.HasValue)
            {
                entity.SyncStrategy = syncStrategy.Value;
            }

            await context.SaveChangesAsync();

            return entity;
        }

        public async Task<bool> DeleteEntity(int id, [Service] DatabaseContext context)
        {
            var entity = await context.Entities.FindAsync(id);

            if (entity == null)
            {
                return false;
            }

            context.Entities.Remove(entity);
            await context.SaveChangesAsync();

            return true;
        }
    }
}
