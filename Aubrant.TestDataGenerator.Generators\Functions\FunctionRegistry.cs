using Aubrant.TestDataGenerator.Core;
using Aubrant.TestDataGenerator.Core.Functions;
using System.Reflection;

namespace Aubrant.TestDataGenerator.Generators.Functions
{
    /// <summary>
    /// Provides a registry for discovering and instantiating data generation functions.
    /// </summary>
    public static class FunctionRegistry
    {
        private static readonly Lazy<IReadOnlyDictionary<string, Type>> _functions = new Lazy<IReadOnlyDictionary<string, Type>>(
            () =>
            {
                var baseFunctionType = typeof(BaseFunction);
                return Assembly.GetExecutingAssembly().GetTypes()
                    .Where(t => t.IsClass && !t.IsAbstract && baseFunctionType.IsAssignableFrom(t) && t != typeof(RuntimeFunction))
                    .Select(t => new { Type = t, Name = ((BaseFunction)Activator.CreateInstance(t)!).Name })
                    .ToDictionary(x => x.Name, x => x.Type);
            });

        /// <summary>
        /// Gets a read-only dictionary of all registered functions, keyed by their Name property.
        /// </summary>
        public static IReadOnlyDictionary<string, Type> Functions => _functions.Value;

        /// <summary>
        /// Tries to create an instance of a registered function by its name.
        /// </summary>
        /// <param name="functionName">The name of the function to create.</param>
        /// <param name="context">The data generator context to pass to the function's constructor if applicable.</param>
        /// <param name="instance">When this method returns, contains the function instance if found; otherwise, null.</param>
        /// <returns>True if the function was found and instantiated; otherwise, false.</returns>
        public static bool TryCreateFunction(string functionName, DataGeneratorContext context, out BaseFunction? instance)
        {
            if (Functions.TryGetValue(functionName, out var type))
            {
                // Try to find a constructor that takes DataGeneratorContext
                var constructor = type.GetConstructor(new[] { typeof(DataGeneratorContext) });
                if (constructor != null)
                {
                    instance = (BaseFunction)constructor.Invoke(new object[] { context })!;
                    return true;
                }

                // Fallback to parameterless constructor
                instance = (BaseFunction)Activator.CreateInstance(type)!;
                return true;
            }

            instance = null;
            return false;
        }
    }
}